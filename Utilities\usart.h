#ifndef __USART_H__
#define __USART_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "gd32f4xx.h"
#include <string.h>
#include <stdio.h>

//#define usart_size 20

//extern uint8_t message[20][usart_size];
//extern uint8_t message_num;

//extern uint8_t usart_flag;
//extern uint8_t usart_counter;
//extern uint8_t usart_tx_buffer[256];
#define USART_ESP8266   0  // PB10_TX  PB11_RX
typedef void (*UsartFun)(const void *, uint16_t);
 
void API_Printf_Hex(uint8_t *p_data, uint16_t length);
void USART0_Init(void);
void USART2_Init(void);

void UARTx_SendBuffer(uint32_t usart_periph,uint8_t* pSda,uint16_t len);

void UART_RxCpltCallback(uint32_t usart_periph);
void UART_IDLECallBack(uint32_t usart_periph);

extern int      FML_USART_Register(uint8_t drv, const UsartFun pUsartFun);
extern void     ESP32_IO_Init(void);

#ifdef __cplusplus
}
#endif

#endif /* __USART_H__ */
