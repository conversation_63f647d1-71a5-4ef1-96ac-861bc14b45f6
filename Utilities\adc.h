#ifndef __ADC_H__
#define __ADC_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "gd32f4xx.h"
#include "gd32f470v_start.h"
#include "usart.h"
//#include "drv_usb_hw.h"
//#include "cdc_acm_core.h"
//#include "user_step.h"

#define sampling_rate 100 
#define sampling_num  3 

extern uint16_t  adc_data[sampling_num][4*sampling_rate];
//extern uint16_t  adc_pd[2*sampling_rate];
extern float   adc_data_f[sampling_num][2*sampling_rate];
extern float breath_org[sampling_rate*2];
extern float  heart_org[sampling_rate*2];
extern float     pd_org[sampling_rate*2];

typedef enum 
{
    heart  = 0,
	  breath = 1,
	  pd    = 2
//	adc_0=0,
//	adc_1=1
} adc_typedef_enum;

//extern uint8_t flag_data_breath;
//extern uint8_t flag_data_heart;
//extern uint8_t flag_data_pd;

typedef union
{
	struct
	{
		uint8_t ADC0_0 	: 1 ; 			// [0]		
		uint8_t ADC1_0	: 1 ; 			// [1]	
		uint8_t ADC2_0 	: 1 ; 			// [2]		
		uint8_t ADC0_1 	: 1 ; 			// [3]	
		uint8_t ADC1_1 	: 1 ;      	// [4]	 
		uint8_t ADC2_1 	: 1 ;     	// [5]		 
		uint8_t REDEY 	: 1 ;       // [6]	 
		uint8_t CHANGE_OK  : 1 ;   	// [7]	 
	}
	STATE_BIT ;
	uint8_t Byte ;
}
ADC_STATE ;

extern ADC_STATE my_adcdma;


void ADC0_Init(void);
void ADC1_Init(void);
void ADC2_Init(void);

//void ADC_Callback(uint32_t adc_periph);

void ADC_ConvHalfCpltCallback(uint32_t adc_periph);
void ADC_ConvCpltCallback(uint32_t adc_periph);

//void ADC_conversion(adc_typedef_enum data_type,uint16_t transform_num,float *transform_data);
//void ADC_conversion(adc_typedef_enum data_type,uint16_t transform_num,float *transform_data1,float *transform_data2);

//void ADC_RunTask(void);

#ifdef __cplusplus
}
#endif

#endif /* __ADC_H__ */
