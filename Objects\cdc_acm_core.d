.\objects\cdc_acm_core.o: USBFS-object\cdc_acm_core.c
.\objects\cdc_acm_core.o: USBFS-object\cdc_acm_core.h
.\objects\cdc_acm_core.o: USBFS-object\usbd_enum.h
.\objects\cdc_acm_core.o: USBFS-object\usbd_core.h
.\objects\cdc_acm_core.o: USBFS-object\drv_usb_core.h
.\objects\cdc_acm_core.o: USBFS-object\drv_usb_regs.h
.\objects\cdc_acm_core.o: USBFS-object\usb_conf.h
.\objects\cdc_acm_core.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\cdc_acm_core.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\cdc_acm_core.o: .\Firmware\CMSIS\core_cm4.h
.\objects\cdc_acm_core.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\cdc_acm_core.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h
.\objects\cdc_acm_core.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\cdc_acm_core.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\cdc_acm_core.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h
.\objects\cdc_acm_core.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\cdc_acm_core.o: .\USER\gd32f4xx_libopt.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\cdc_acm_core.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\cdc_acm_core.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\cdc_acm_core.o: .\Utilities\gd32f470v_start.h
.\objects\cdc_acm_core.o: USBFS-object\usb_ch9_std.h
.\objects\cdc_acm_core.o: USBFS-object\usbd_conf.h
.\objects\cdc_acm_core.o: USBFS-object\drv_usb_dev.h
.\objects\cdc_acm_core.o: E:\MDK533\ARM\ARMCC\Bin\..\include\wchar.h
.\objects\cdc_acm_core.o: USBFS-object\usb_cdc.h
