; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 0x08000000 0x00200000  {    ; load region size_region (从Flash起始地址开始，2MB空间)
  ER_IROM1 0x08000000 0x00200000  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   .ANY (+XO)
  }  
  
  RW_IRAM2 0x20000000 UNINIT 0x00000004  {  ; RW data 
   .ANY(.NoInit)
  }
  
  RW_IRAM1 0x20000004 0x0001FFFC  {  ; RW data  
   .ANY (+RW +ZI)
  }
   
  RW_IRAM3 0x20020000 0x00030000  {  ; RW data -64KB SRAM2(0x20020000)
   *(.RAM_D3)
  }
  

}

