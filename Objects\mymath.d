.\objects\mymath.o: Utilities\mymath.c
.\objects\mymath.o: Utilities\mymath.h
.\objects\mymath.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\mymath.o: Utilities\usart.h
.\objects\mymath.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\mymath.o: .\Firmware\CMSIS\core_cm4.h
.\objects\mymath.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\mymath.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h
.\objects\mymath.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\mymath.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\mymath.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h
.\objects\mymath.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\mymath.o: .\USER\gd32f4xx_libopt.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\mymath.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\mymath.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\mymath.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\mymath.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\mymath.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\mymath.o: Utilities\whut_math.h
.\objects\mymath.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h
.\objects\mymath.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\mymath.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\mymath.o: Utilities\adc.h
.\objects\mymath.o: Utilities\gd32f470v_start.h
