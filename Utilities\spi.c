#include "spi.h"
#include "gd32f4xx.h"
#include <string.h>

void SPI1_Init(void)
{
    spi_parameter_struct spi_init_struct;

    rcu_periph_clock_enable(RCU_GPIOB);
    rcu_periph_clock_enable(RCU_SPI1);

    /* SPI5_CLK(PG13), SPI5_MISO(PG12), SPI5_MOSI(PG14), SPI5_IO2(PG10) and SPI5_IO3(PG11) GPIO pin configuration */
    gpio_af_set(GPIOB, GPIO_AF_5, GPIO_PIN_13| GPIO_PIN_14|GPIO_PIN_15);
    gpio_mode_set(GPIOB, GPIO_MODE_AF, GPIO_PUPD_NONE,  GPIO_PIN_13| GPIO_PIN_14|GPIO_PIN_15);
    gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,  GPIO_PIN_13| GPIO_PIN_14|GPIO_PIN_15);

    /* SPI5_CS(PG9) GPIO pin configuration */
    gpio_mode_set(GPIOB, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_12);
    gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_12);

    /* chip select invalid */
    SET_SPI1_NSS_HIGH;

    /* SPI5 parameter config */
    spi_init_struct.trans_mode           = SPI_TRANSMODE_FULLDUPLEX;
    spi_init_struct.device_mode          = SPI_MASTER;
    spi_init_struct.frame_size           = SPI_FRAMESIZE_8BIT;
    spi_init_struct.clock_polarity_phase = SPI_CK_PL_LOW_PH_1EDGE;
    spi_init_struct.nss                  = SPI_NSS_SOFT;
    spi_init_struct.prescale             = SPI_PSC_32;
    spi_init_struct.endian               = SPI_ENDIAN_MSB;
    spi_init(SPI1, &spi_init_struct);

//    /* quad wire SPI_IO2 and SPI_IO3 pin output enable */
//    qspi_io23_output_enable(SPI5);

    /* enable SPI5 */
    spi_enable(SPI1);
}

uint8_t  DRV_SPI_SwapByte(uint8_t  byte)
{
    while (spi_i2s_flag_get(SPI1, SPI_FLAG_TBE) == RESET); /*!< transmit buffer empty flag */

    spi_i2s_data_transmit(SPI1, byte);

    while (spi_i2s_flag_get(SPI1, SPI_FLAG_RBNE) == RESET);/*!< receive buffer not empty flag */

    return spi_i2s_data_receive(SPI1);
}

