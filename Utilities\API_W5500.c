#include "API_W5500.h"

#define SPI0_DUTM    0x00

__attribute__((section (".RAM_D3"))) LAN_PARA Lan_Para ={0};//??????????


void API_W5500_SPI0_Init(void)
{
    spi_parameter_struct spi_init_struct;

	  rcu_periph_clock_enable(RCU_GPIOA);
    rcu_periph_clock_enable(RCU_GPIOB);
    rcu_periph_clock_enable(RCU_SPI0);

    gpio_af_set(GPIOA, GPIO_AF_5, GPIO_PIN_6| GPIO_PIN_7);//MISO??MOSI????PA6??PA7
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_NONE,  GPIO_PIN_6| GPIO_PIN_7);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_6|GPIO_PIN_7);
	
    gpio_af_set(GPIOB, GPIO_AF_5, GPIO_PIN_3);//SCLK????PB3
    gpio_mode_set(GPIOB, GPIO_MODE_AF, GPIO_PUPD_NONE,  GPIO_PIN_3);
    gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_3);

    gpio_mode_set(GPIOB, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_2);//CS????????PB2
    gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_2);

      SET_SPI0_CS_Hi();    /* chip select invalid */

    /* SPI0 parameter config */
    spi_init_struct.trans_mode           = SPI_TRANSMODE_FULLDUPLEX;
    spi_init_struct.device_mode          = SPI_MASTER;
    spi_init_struct.frame_size           = SPI_FRAMESIZE_8BIT;
    spi_init_struct.clock_polarity_phase = SPI_CK_PL_LOW_PH_1EDGE;
    spi_init_struct.nss                  = SPI_NSS_SOFT;
    spi_init_struct.prescale             = SPI_PSC_32;
    spi_init_struct.endian               = SPI_ENDIAN_MSB;
    spi_init(SPI0, &spi_init_struct);	
    spi_enable(SPI0);
		Lan_Para.fPowerOn=1;//?????ٳ????
		
}

void API_W5500_GPIO_Init(void)//????W5500???��????��????
{
	 // rcu_periph_clock_enable(W5500_INT_RCU);
    rcu_periph_clock_enable(W5500_RST_RCU);
	 // gpio_mode_set(W5500_INT_PORT, GPIO_MODE_INPUT, GPIO_PUPD_NONE, W5500_INT_PIN);//PA8 INT????
   // gpio_output_options_set(W5500_INT_PORT, GPIO_PUPD_NONE, GPIO_OSPEED_50MHZ, W5500_INT_PIN);
	  gpio_mode_set(W5500_RST_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, W5500_RST_PIN);//PB4????��????
    gpio_output_options_set(W5500_RST_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, W5500_RST_PIN);
}


uint8_t  API_SPI_SwapByte(uint8_t  byte)
{
    while (spi_i2s_flag_get(SPI0, SPI_FLAG_TBE) == RESET); /*!< transmit buffer empty flag */

    spi_i2s_data_transmit(SPI0, byte);
    while (spi_i2s_flag_get(SPI0, SPI_FLAG_RBNE) == RESET);/*!< receive buffer not empty flag */
    return (spi_i2s_data_receive(SPI0));
}
uint8_t  API_SPI0_Read_Byte(void)//SPI0????1?????????
{   
    while (spi_i2s_flag_get(SPI0, SPI_FLAG_TBE) == RESET); /*!< transmit buffer empty flag */
    spi_i2s_data_transmit(SPI0, SPI0_DUTM);
    while (spi_i2s_flag_get(SPI0, SPI_FLAG_RBNE) == RESET);/*!< receive buffer not empty flag */
    return (spi_i2s_data_receive(SPI0));
}

void  API_SPI0_Send_Byte(uint8_t  byte)//SPI0????1?????????
{   
    while (spi_i2s_flag_get(SPI0, SPI_FLAG_TBE) == RESET); /*!< transmit buffer empty flag */
    spi_i2s_data_transmit(SPI0, byte);
    while (spi_i2s_flag_get(SPI0, SPI_FLAG_RBNE) == RESET);/*!< receive buffer not empty flag */
    spi_i2s_data_receive(SPI0);
}


void API_SPI0_Send_Short(uint16_t dat)//SPI0????2?????????(16��)
{
	API_SPI0_Send_Byte(dat/256);//��?????��
	API_SPI0_Send_Byte(dat);	//��?????��

}


uint8_t API_Read_W5500_1Byte(uint16_t reg)//??W5500?????????????1?????????
{
	uint8_t i;
	SET_SPI0_CS_Low();//??W5500??SCS?????		
	API_SPI0_Send_Short(reg);//???SPI1��16��????????
	API_SPI0_Send_Byte(FDM1|RWB_READ|COMMON_R);//???SPI1��???????,1????????????,??????,?????��????
	i =API_SPI0_Read_Byte();

	SET_SPI0_CS_Hi();;//??W5500??SCS?????
	return i;//??????????????????
}


void API_Write_W5500_1Byte(uint16_t reg, uint8_t dat)
{
		SET_SPI0_CS_Low();//??W5500??SCS?????		
	  API_SPI0_Send_Short(reg);//???SPI1��16��????????
	  API_SPI0_Send_Byte(FDM1|RWB_WRITE|COMMON_R);//???SPI0��???????,1????????????,��????,?????��????
		API_SPI0_Send_Byte(dat);
		SET_SPI0_CS_Hi();;//??W5500??SCS?????
}

void API_Write_W5500_2Byte(uint16_t reg, uint16_t dat)
{
		SET_SPI0_CS_Low();//??W5500??SCS?????	
		
	API_SPI0_Send_Short(reg);//???SPI1��16��????????
	API_SPI0_Send_Byte(FDM2|RWB_WRITE|COMMON_R);//???SPI1��???????,2????????????,��????,?????��????
	API_SPI0_Send_Short(dat);//��16��????
	SET_SPI0_CS_Hi();;//??W5500??SCS?????
}
void API_Write_W5500_nByte(uint16_t reg, uint8_t *dat_ptr, uint16_t size)
{
   uint16_t i;
	 SET_SPI0_CS_Low();//??W5500??SCS?????		
	 API_SPI0_Send_Short(reg);//???SPI1��16��????????
	 API_SPI0_Send_Byte(VDM|RWB_WRITE|COMMON_R);//???SPI0��???????,1????????????,��????,?????��????
   for(i=0;i<size;i++)
	 {
			API_SPI0_Send_Byte(*dat_ptr++);//��??????????
	 }
	 SET_SPI0_CS_Hi();;//??W5500??SCS?????
}

//???SPI0????????????��1?????????
void API_Write_W5500_SOCK_1Byte(uint8_t sock_num, uint16_t reg, uint8_t dat)
{
	 SET_SPI0_CS_Low();//??W5500??SCS?????		
	 API_SPI0_Send_Short(reg);
	 API_SPI0_Send_Byte(FDM1|RWB_WRITE|(sock_num*0x20+0x08));
	 API_SPI0_Send_Byte(dat);
	 SET_SPI0_CS_Hi();;//??W5500??SCS?????
}

//???SPI0????????????��2?????????
void API_Write_W5500_SOCK_2Byte(uint8_t sock_num, uint16_t reg, uint16_t dat)
{
	 SET_SPI0_CS_Low();//??W5500??SCS?????		
	 API_SPI0_Send_Short(reg);
	 API_SPI0_Send_Byte(FDM2|RWB_WRITE|(sock_num*0x20+0x08));
	 API_SPI0_Send_Short(dat);
	 SET_SPI0_CS_Hi();;//??W5500??SCS?????
}
//???SPI0????????????��4?????????
void API_Write_W5500_SOCK_4Byte(uint8_t sock_num, uint16_t reg, uint8_t*dat_ptr)
{
		SET_SPI0_CS_Low();//??W5500??SCS?????		
	  API_SPI0_Send_Short(reg);
		API_SPI0_Send_Byte(FDM4|RWB_WRITE|(sock_num*0x20+0x08));
	  API_SPI0_Send_Byte(*dat_ptr++);
	  API_SPI0_Send_Byte(*dat_ptr++);
	  API_SPI0_Send_Byte(*dat_ptr++);
	  API_SPI0_Send_Byte(*dat_ptr++);
	  SET_SPI0_CS_Hi();//??W5500??SCS?????
}

//??W5500????????????1?????????
uint8_t  API_Read_W5500_SOCK_1Byte(uint8_t sock_num, uint16_t reg)
{
		uint8_t i;
		SET_SPI0_CS_Low();//??W5500??SCS?????	
	  API_SPI0_Send_Short(reg);
	  API_SPI0_Send_Byte(FDM1|RWB_READ|(sock_num*0x20+0x08));
		i = API_SPI0_Read_Byte();
		SET_SPI0_CS_Hi();//??W5500??SCS?????
	  return i;//??????????????????
}

//??W5500????????????2?????????
uint16_t  API_Read_W5500_SOCK_2Byte(uint8_t sock_num, uint16_t reg)
{
		uint16_t i;
		SET_SPI0_CS_Low();//??W5500??SCS?????	
	  API_SPI0_Send_Short(reg);
	  API_SPI0_Send_Byte(FDM2|RWB_READ|(sock_num*0x20+0x08));
	
	
	    i =	API_SPI0_Read_Byte();
	    i=i<<8;
	    i+= API_SPI0_Read_Byte();
		SET_SPI0_CS_Hi();//??W5500??SCS?????
	  return i;//??????????????????
}

uint16_t  API_Read_SOCK_Data_Buffer(uint8_t sock_num, uint8_t *dat_ptr)//????????????????
{
		uint16_t rx_size,offset, offset1,i;
		uint8_t j;
		rx_size=API_Read_W5500_SOCK_2Byte(sock_num,Sn_RX_RSR);
		if(rx_size==0) return 0;//?????????????
		if(rx_size>1460) rx_size=1460;

		offset=API_Read_W5500_SOCK_2Byte(sock_num,Sn_RX_RD);
		offset1=offset;
		offset&=(S_RX_SIZE-1);//???????????????
		SET_SPI0_CS_Low();//??W5500??SCS?????	
		API_SPI0_Send_Short(offset);//��16��???
		API_SPI0_Send_Byte(VDM|RWB_READ|(sock_num*0x20+0x18));//��???????,N????????????,??????,?????s??????
		if((offset+rx_size)<S_RX_SIZE)//????????��????W5500?????????????????????
		{
			for(i=0;i<rx_size;i++)//??????rx_size?????????
			{
				j= API_SPI0_Read_Byte();
				*dat_ptr=j;//???????????????��??????�H????
				dat_ptr++;//??????�H?????????????1
			}
		}
		else//????????????W5500?????????????????????
		{
			offset=S_RX_SIZE-offset;
			for(i=0;i<offset;i++)//?????????offset?????????
			{
				j=API_SPI0_Read_Byte();
				*dat_ptr=j;//???????????????��??????�H????
				dat_ptr++;//??????�H?????????????1
			}
			SET_SPI0_CS_Hi(); //??W5500??SCS?????
			
			SET_SPI0_CS_Low();//??W5500??SCS?????
			//API_SPI0_Send_Short(0x00);//��16��???
			API_SPI0_Send_Byte(VDM|RWB_READ|(sock_num*0x20+0x18));//��???????,N????????????,??????,?????s??????
			j = API_SPI0_Read_Byte();

			for(;i<rx_size;i++)//????????rx_size-offset?????????
			{
				//API_SPI0_Send_Byte(0x00);//?????????????
				j = API_SPI0_Read_Byte();
				*dat_ptr=j;//???????????????��??????�H????
				dat_ptr++;//??????�H?????????????1
			}
		}
		SET_SPI0_CS_Hi(); //??W5500??SCS?????
		offset1+=rx_size;//??????????????,????��????????????????????
		API_Write_W5500_SOCK_2Byte(sock_num, Sn_RX_RD, offset1);
		API_Write_W5500_SOCK_1Byte(sock_num, Sn_CR, RECV);//????????????????
		return rx_size;//????????????????
}

void API_Write_SOCK_Data_Buffer(uint8_t sock_num, uint8_t *dat_ptr, uint16_t size)
{
		uint16_t offset,offset1,i;
		offset=API_Read_W5500_SOCK_2Byte(sock_num,Sn_TX_WR);
		offset1=offset;
		offset&=(S_TX_SIZE-1);//???????????????
		
		SET_SPI0_CS_Low();
		API_SPI0_Send_Short(offset);//��16��???
		API_SPI0_Send_Byte(VDM|RWB_WRITE|(sock_num*0x20+0x10));//��???????,N????????????,��????,?????s??????

		if((offset+size)<S_TX_SIZE)//????????��????W5500?????????????????????
		{
				for(i=0;i<size;i++)//???��??size?????????
				{
					API_SPI0_Send_Byte(*dat_ptr++);//��?????????????		
					
				}
			SET_SPI0_CS_Hi(); //??W5500??SCS?????
			__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();
			offset1+=size;//??????????????,?????��????????????????????????????????
			API_Write_W5500_SOCK_2Byte(sock_num, Sn_TX_WR, offset1);
			API_Write_W5500_SOCK_1Byte(sock_num, Sn_CR, SEND);//????????????????
		  __NOP();
		}
		else//????????????W5500?????????????????????
		{
			offset=S_TX_SIZE-offset;
			for(i=0;i<offset;i++)//???��???offset?????????
			{
					API_SPI0_Send_Byte(*dat_ptr++);//��?????????????
			}
			SET_SPI0_CS_Hi(); //??W5500??SCS?????
      __NOP(); __NOP(); __NOP(); __NOP(); __NOP(); __NOP(); __NOP(); __NOP();
			SET_SPI0_CS_Low();;//??W5500??SCS?????

			API_SPI0_Send_Short(0x00);//��16��???
			API_SPI0_Send_Byte(VDM|RWB_WRITE|(sock_num*0x20+0x10));//��???????,N????????????,��????,?????s??????

			for(;i<size;i++)//???��??size-offset?????????
			{
				API_SPI0_Send_Byte(*dat_ptr++);//��?????????????
			}
			SET_SPI0_CS_Hi(); //??W5500??SCS?????
			offset1+=size;//??????????????,?????��????????????????????????????????
			API_Write_W5500_SOCK_2Byte(sock_num, Sn_TX_WR, offset1);
			API_Write_W5500_SOCK_1Byte(sock_num, Sn_CR, SEND);//????????????????		
		}
}


void API_W5500_HardWare_Rest(void)//w5500?????��
{

		W5500_RST_Low();
		delay_1ms (50);
		W5500_RST_Hi();
		delay_1ms (200);
  	Lan_Para.fWaitingConnect=1;//???????
	 	//while(( API_Read_W5500_1Byte(PHYCFGR)&LINK)==0);//???????????????
	  while(( API_Read_W5500_1Byte(PHYCFGR)&LINK)==0)
		{
		   if(Lan_Para.fWaitingConnect==2)//20?????????????
			 {
					 printf("??????????")	;
				   break;
			 }
		}
		if(Lan_Para.fWaitingConnect !=2 )
		{
		   Lan_Para.fWaitingConnect =3;//??????
		  	printf("???????????????%dS\r\n",Lan_Para.WaitingConnectCnt)	;
		}
}


void API_Init_Net_Parameters(void)//????????????
{
		uint8_t  TempBuff1[16]={0},TempBuff2[16]={0};
		uint8_t  i=0;
		if(EEPROM_SPI_ReadBuffer(TempBuff1,ADDr_eeProm_LAN_MAC, 16) == 0x01)
		{
		    printf("eeprom??????????????????\r\n");
  			if(TempBuff1[7] == 0x01)//???��???????????????
				 {
				      for(i=0;i<4;i++)
					   {
					      Lan_Para.Sub_Mask[i]   =   TempBuff1[8+i];//????????
							  Lan_Para.Gateway_IP[i] =   TempBuff1[12+i];//????????
						 }
					  
				 }
				 else
				 {
						Lan_Para.Sub_Mask[0]   =   255;
						Lan_Para.Sub_Mask[1]   =   255;
						Lan_Para.Sub_Mask[2]   =   255;
						Lan_Para.Sub_Mask[3]   =   0;//????????
						Lan_Para.Gateway_IP[0] =   192;//????
						Lan_Para.Gateway_IP[1] =   168;//????
						Lan_Para.Gateway_IP[2] =   1;//????
						Lan_Para.Gateway_IP[3] =   1;//????
					  printf("eeprom��?????????????????????\r\n");
				 }
		}
		else
		{
				Lan_Para.Sub_Mask[0]   =   255;
				Lan_Para.Sub_Mask[1]   =   255;
				Lan_Para.Sub_Mask[2]   =   255;
				Lan_Para.Sub_Mask[3]   =   0;//????????
				Lan_Para.Gateway_IP[0] =   192;//????
				Lan_Para.Gateway_IP[1] =   168;//????
				Lan_Para.Gateway_IP[2] =   1;//????
				Lan_Para.Gateway_IP[3] =   1;//????
			  printf("eeprom?????????????????\r\n");
		}	
		if(EEPROM_SPI_ReadBuffer(TempBuff2,ADDr_eeProm_LAN_IP_Port, 16) == 0x01)
		{
				 printf("eeprom???IP??????\r\n");
    		 if(TempBuff2[0] == 0x01)//???��??IP????
				 {
						for(i=0;i<4;i++)
								Lan_Para.IP_B0[i] = TempBuff2[1+i];
						Lan_Para.Port_B0[0] = TempBuff2[5];	Lan_Para.Port_B0[1] =TempBuff2[6];
					 
				 }
				 else
				 {
						Lan_Para.IP_B0[0] = 192;
					  Lan_Para.IP_B0[1] = 168;
					  Lan_Para.IP_B0[2] = 1;
					  Lan_Para.IP_B0[3] = 199;
					  Lan_Para.Port_B0[0] = (5000>>8)&0xff;Lan_Para.Port_B0[1] = (5000&0xff);//??????5000
					  printf("eeprom��????IP??????\r\n");
				 }
				 if(TempBuff2[7] == 0x01)//?????????IP????
				 {
						for(i=0;i<4;i++)
								Lan_Para.IP_S[i] = TempBuff2[8+i];
						Lan_Para.Port_S[0] = TempBuff2[12]; Lan_Para.Port_S[1]  = TempBuff2[13];
				 }
				 else
				 {
							Lan_Para.IP_S[0] =192;
					    Lan_Para.IP_S[1] =168;
					    Lan_Para.IP_S[2] =1;
					    Lan_Para.IP_S[3] =190;
					    Lan_Para.Port_S[0] = (6000>>8)&0xff;Lan_Para.Port_S[1] = (6000&0xff);//?????????
				 }
		}
		else
		{
						Lan_Para.IP_B0[0] = 192;
					  Lan_Para.IP_B0[1] = 168;
					  Lan_Para.IP_B0[2] = 1;
					  Lan_Para.IP_B0[3] = 199;
						Lan_Para.Port_B0[0] = (5000>>8)&0xff;Lan_Para.Port_B0[1] = (5000&0xff);//??????5000
		
						Lan_Para.IP_S[0] =192;
						Lan_Para.IP_S[1] =168;
						Lan_Para.IP_S[2] =1;
						Lan_Para.IP_S[3] =190;
						Lan_Para.Port_S[0] = (6000>>8)&0xff;Lan_Para.Port_S[1] = (6000&0xff);//?????????
						printf("eeprom??????IP??????\r\n");
		}
		Lan_Para.mRun_S0 = 0x01;//???0????????TCP_Client??
}
uint8_t  API_Detect_Gateway(void)
{
		uint8_t ip_adde[4];
		ip_adde[0]=Lan_Para.IP_B0[0]+1;
		ip_adde[1]=Lan_Para.IP_B0[1]+1;
		ip_adde[2]=Lan_Para.IP_B0[2]+1;
		ip_adde[3]=Lan_Para.IP_B0[3]+1;

		//???????????????????????
		API_Write_W5500_SOCK_4Byte(0,Sn_DIPR,ip_adde);//????????????��??????IP?????IP?
		API_Write_W5500_SOCK_1Byte(0,Sn_MR,MR_TCP);//????socket?TCP??
			API_Write_W5500_SOCK_1Byte(0,Sn_CR,OPEN);//??Socket	
		delay_1ms(5);//???5ms 	
		
		if(API_Read_W5500_SOCK_1Byte(0,Sn_SR) != SOCK_INIT)//???socket?????
		{
			API_Write_W5500_SOCK_1Byte(0,Sn_CR,CLOSE);//??????,???Socket
			printf("Socket?????\r\n");
			return LAN_FALSE;//????FALSE(0x00)
		}
    printf("Socket????\r\n");
		API_Write_W5500_SOCK_1Byte(0,Sn_CR,CONNECT);//????Socket?Connect??						

		do
		{
			uint8_t j=0;
			j= API_Read_W5500_SOCK_1Byte(0,Sn_IR);//???Socket0?��????????
			if(j!=0)
			{
			API_Write_W5500_SOCK_1Byte(0,Sn_IR,j);
				printf("???sock0?��???\r\n");
			}
			delay_1ms(5);//???5ms 
			if((j&IR_TIMEOUT) == IR_TIMEOUT)
			{
				return LAN_FALSE;	
			}
			else if(API_Read_W5500_SOCK_1Byte(0,Sn_DHAR) != 0xff)
			{
				API_Write_W5500_SOCK_1Byte(0,Sn_CR,CLOSE);//???Socket
				return LAN_TRUE;							
			}
		}while(1);
}

void API_W5500_Register_Init(void)//?????W5500?????
{
    uint8_t i;
	  API_Write_W5500_1Byte(MR, RST);//??????��W5500,??1??��,??��???????0
	  delay_1ms(10);//???10ms
	
		API_Write_W5500_nByte(GAR,Lan_Para.Gateway_IP, 4);//????????(Gateway)??IP??? ??????????????????????????????????????????????????????Internet
    API_Write_W5500_nByte(SUBR,Lan_Para.Sub_Mask,4);//????????????(MASK)?	????????????????????	
		API_Write_W5500_nByte(SHAR,Lan_Para.MAC,6);	//????MAC??????????????????
		API_Write_W5500_nByte(SIPR,Lan_Para.IP_B0,4);	//???????IP???????????????????
		for(i=0;i<8;i++)	//???��?????????????????????��???��?W5500???????
		{
			API_Write_W5500_SOCK_1Byte(i,Sn_RXBUF_SIZE, 0x02);//Socket Rx memory size=2k
			API_Write_W5500_SOCK_1Byte(i,Sn_TXBUF_SIZE, 0x02);//Socket Tx mempry size=2k
		}
     API_Write_W5500_2Byte(RTR, 0x07d0);	//???????????????2000(200ms) ????��????100???,??????????2000(0x07D0),????200????
		 API_Write_W5500_1Byte(RCR,8);//?????????????????8??????????????????څ?,?????????��?(???????��??????��?Sn_IR ???��(TIMEOUT)?��?1??) 
}

void API_Socket_Init(uint8_t sock_num)
{
	printf("   ��ʼ��Socket%d����...\r\n", sock_num);
	//???��????????��?W5500??????????????????
	API_Write_W5500_SOCK_2Byte(0, Sn_MSSR, 1460);//??????????=1460(0x5b4)
	printf("   ��������Ƭ����: 1460�ֽ�\r\n");

	//??????????
	switch(sock_num)
	{
		case 0:
					{
						//??????0?????
						uint16_t local_port = Lan_Para.Port_B0[0]*256+Lan_Para.Port_B0[1];
						API_Write_W5500_SOCK_2Byte(0, Sn_PORT, local_port);
						printf("   ���ض˿�: %d\r\n", local_port);

						//??????0???(???)????
						uint16_t remote_port = Lan_Para.Port_S[0]*256+Lan_Para.Port_S[1];
						API_Write_W5500_SOCK_2Byte(0, Sn_DPORTR, remote_port);
						printf("   Զ�̶˿�: %d\r\n", remote_port);

						//??????0???(???)IP???
						API_Write_W5500_SOCK_4Byte(0, Sn_DIPR, Lan_Para.IP_S);
						printf("   Զ��IP: %d.%d.%d.%d\r\n",
						       Lan_Para.IP_S[0], Lan_Para.IP_S[1], Lan_Para.IP_S[2], Lan_Para.IP_S[3]);
					}
			
			break;

		case 1:
			break;

		case 2:
			break;

		case 3:
			break;

		case 4:
			break;

		case 5:
			break;

		case 6:
			break;

		case 7:
			break;

		default:
			break;
	}
}
//??????Socket??????????????,????��???,????????????????
uint8_t  API_Socket_Listen(uint8_t sock_num)
{
		API_Write_W5500_SOCK_1Byte(sock_num,Sn_MR,MR_TCP);//????socket?TCP?? 
		API_Write_W5500_SOCK_1Byte(sock_num,Sn_CR,OPEN);//??Socket	
		delay_1ms(5);//???5ms
		if(API_Read_W5500_SOCK_1Byte(sock_num,Sn_SR)!=SOCK_INIT)//???socket?????
		{
				API_Write_W5500_SOCK_1Byte(sock_num,Sn_CR,CLOSE);//??????,???Socket
				return LAN_FALSE;//????FALSE(0x00)
		}	
		API_Write_W5500_SOCK_1Byte(sock_num,Sn_CR,LISTEN);//????Socket???????	
		delay_1ms(5);//???5ms
		if(API_Read_W5500_SOCK_1Byte(sock_num,Sn_SR)!=SOCK_LISTEN)//???socket???????
		{
				API_Write_W5500_SOCK_1Byte(sock_num,Sn_CR,CLOSE);//???��????,???Socket
				return LAN_FALSE;//????FALSE(0x00)
		}
	return LAN_TRUE;

	//?????????Socket????????????????,??????????????????????????,????????Socket?��??
	//???��?Socket??????????????????????????????????????IP????????
}
/*
????Socket?????????????,????��???,??????????????????
??????????????????��??????????????????,??????????��???????
?��???????????,????????????????????
*/

uint8_t  API_Socket_Connect(uint8_t sock_num)//???????Socket(0~7)????????????????????
{
			API_Write_W5500_SOCK_1Byte(sock_num,Sn_MR,MR_TCP);//????socket?TCP??
			API_Write_W5500_SOCK_1Byte(sock_num,Sn_CR,OPEN);//??Socket
			delay_1ms(5);//???5ms
			if(API_Read_W5500_SOCK_1Byte(sock_num,Sn_SR)!=SOCK_INIT)//???socket?????
			{
				API_Write_W5500_SOCK_1Byte(sock_num,Sn_CR,CLOSE);//??????,???Socket
				return LAN_FALSE;//????FALSE(0x00)
			}
			API_Write_W5500_SOCK_1Byte(sock_num,Sn_CR,CONNECT);//????Socket?Connect??
			return LAN_TRUE;//????TRUE,???��??
}

/*
???Socket??????UDP??,????��???,??UDP????,Socket???????????????
?��??????????��????W5500?????UDP??
*/
uint8_t API_Socket_UDP(uint8_t sock_num)//???????Socket(0~7)?UDP??
{
	API_Write_W5500_SOCK_1Byte(sock_num,Sn_MR,MR_UDP);//????Socket?UDP??*/
	API_Write_W5500_SOCK_1Byte(sock_num,Sn_CR,OPEN);//??Socket*/
	delay_1ms(5);//???5ms
	if(API_Read_W5500_SOCK_1Byte(sock_num,Sn_SR)!=SOCK_UDP)//???Socket?????
	{
			API_Write_W5500_SOCK_1Byte(sock_num,Sn_CR,CLOSE);//??????,???Socket
			return LAN_FALSE;//????FALSE(0x00)
	}
	else
		return LAN_TRUE;

	//?????????Socket????UDP??????,??????????????????????????????????
	//???Socket?????????????,???????????????????????????????IP?????Socket?????
	//??????????IP?????Socket???????????,?????��???????��??,??????????????????
}

void API_W5500_Socket_Set(void)
{
		printf("   ���Socket0����״̬: 0x%02X\r\n", Lan_Para.sRun_S0);

		if(Lan_Para.sRun_S0==0)//???0?????????
		{
				printf("   Socket0��Ҫ��ʼ��������ģʽ: ");
				if(Lan_Para.mRun_S0==TCP_SERVER)//TCP????????
				{
					printf("TCP������ģʽ\r\n");
					if(API_Socket_Listen(0)==LAN_TRUE)
					{
						Lan_Para.sRun_S0=S_INIT;
						printf("   ? TCP������ģʽ���óɹ�\r\n");
					}
					else
					{
						Lan_Para.sRun_S0=0;
						printf("   ? TCP������ģʽ����ʧ��\r\n");
					}
				}
				else if(Lan_Para.mRun_S0==TCP_CLIENT)//TCP???????
				{
					printf("TCP�ͻ���ģʽ\r\n");
					if(API_Socket_Connect(0)==LAN_TRUE)
					{
						Lan_Para.sRun_S0=S_INIT;
						printf("   ? W5500����ΪTCP�ͻ���ģʽ�ɹ�\r\n");
					}
					else
					{
						Lan_Para.sRun_S0=0;
						printf("   ? W5500����ΪTCP�ͻ���ģʽʧ��\r\n");
					}
				}
				else//UDP??
				{
					printf("UDPģʽ\r\n");
					if(API_Socket_UDP(0)==LAN_TRUE)
					{
						Lan_Para.sRun_S0=S_INIT|S_CONN;
						printf("   ? UDPģʽ���óɹ�\r\n");
					}
					else
					{
						Lan_Para.sRun_S0=0;
						printf("   ? UDPģʽ����ʧ��\r\n");
					}
				}
		}
}

void API_Init_LAN(void)
{
	  uint8_t  i;
	  printf("\r\n=== W5500�����ʼ����ʼ ===\r\n");

	  printf("1. ��ʼ��SPI0�ӿ�...\r\n");
	  API_W5500_SPI0_Init();
	  printf("   SPI0��ʼ�����\r\n");

	  printf("2. ��ʼ��GPIO��������...\r\n");
    API_W5500_GPIO_Init();//????W5500???��????��????
    printf("   GPIO��ʼ�����\r\n");

    printf("3. ��ʼ���������...\r\n");
  	API_Init_Net_Parameters();//?????????
  	printf("   ��������������\r\n");
  	printf("   ����IP: %d.%d.%d.%d:%d\r\n",
  	       Lan_Para.IP_B0[0], Lan_Para.IP_B0[1], Lan_Para.IP_B0[2], Lan_Para.IP_B0[3],
  	       (Lan_Para.Port_B0[0]<<8) | Lan_Para.Port_B0[1]);
  	printf("   ����IP: %d.%d.%d.%d\r\n",
  	       Lan_Para.Gateway_IP[0], Lan_Para.Gateway_IP[1], Lan_Para.Gateway_IP[2], Lan_Para.Gateway_IP[3]);
  	printf("   ��������: %d.%d.%d.%d\r\n",
  	       Lan_Para.Sub_Mask[0], Lan_Para.Sub_Mask[1], Lan_Para.Sub_Mask[2], Lan_Para.Sub_Mask[3]);

  	printf("4. Ӳ����λW5500...\r\n");
	  API_W5500_HardWare_Rest();//??��???
	  printf("   Ӳ����λ���\r\n");

	  printf("5. ��ʼ��W5500�Ĵ���...\r\n");
	  API_W5500_Register_Init();//?????W5500?????
	  printf("   �Ĵ�����ʼ�����\r\n");

	  printf("6. ���������ͨ��...\r\n");
	  i = API_Detect_Gateway();//????????????
	  if(i == LAN_TRUE)
			printf("   ? ������ͨ�Լ����ȷ\r\n");
		if(i == LAN_FALSE)
			printf("   ? ������ͨ�Լ��ʧ��\r\n");

		printf("7. ��ʼ��Socket0...\r\n");
	  API_Socket_Init(0);//???Socket(0~7)?????,????????0
	  printf("   Socket0��ʼ�����\r\n");

	  printf("8. ����Socket����ģʽ...\r\n");
	  API_W5500_Socket_Set();
	  printf("   Socket����ģʽ�������\r\n");

	  printf("=== W5500�����ʼ����� ===\r\n\r\n");
}


void API_W5500_Interrupt_Process(void)
{
	uint8_t i,j;
 // static uint8_t Temp_cnt=0;
IntDispose:
	
//	i = API_Read_W5500_1Byte(IR);//????��????????
//	API_Write_W5500_1Byte(IR, (i&0xf0));//??��????��???

//	if((i & CONFLICT) == CONFLICT)//IP????????????
//	{
//		 Lan_Para.ErrorCode =0x01;
//		 if(Temp_cnt == 0) {
//				 printf("IP???????????????%d\r\n", Lan_Para.ErrorCode);
//				 Temp_cnt++;
//		 }
//	}
//	else
//	{
//		   Temp_cnt=0;
//			 Lan_Para.ErrorCode =0x00;
//	     printf("IP????????????????%d\r\n", Lan_Para.ErrorCode);
//	}
	
	
	i=API_Read_W5500_1Byte(SIR);//???????��????????	
	if((i & S0_INT) == S0_INT)//Socket0??????? 
	{
		j=API_Read_W5500_SOCK_1Byte(0,Sn_IR);//???Socket0?��????????
		API_Write_W5500_SOCK_1Byte(0,Sn_IR,j);
		if(j&IR_CON)//??TCP????,Socket0??????? 
		{
				Lan_Para.sRun_S0|=S_CONN;//??????????0x02,???????????????????????????
				Lan_Para.ErrorCode =0x00;
			  printf("???????????????\r\n");
		}
		if(j&IR_DISCON)//??TCP????Socket??????????
		{
				API_Write_W5500_SOCK_1Byte(0,Sn_CR,CLOSE);//?????,???????????? 
				API_Socket_Init(0);		//???Socket(0~7)?????,????????0
				Lan_Para.sRun_S0=0;//??????????0x00,??????????
			  Lan_Para.ErrorCode =0x02;
		    printf("???????????????????%d\r\n", Lan_Para.ErrorCode);
		}
		if(j&IR_SEND_OK)//Socket0??????????,???????????S_tx_process()???????????? 
		{
				Lan_Para.sData_S0|=S_TRANSMITOK;//????????????????? 
			  Lan_Para.SendComplete=1;
			  printf("?????????????????\r\n");
		}
		if(j&IR_RECV)//Socket?????????,????????S_rx_process()???? 
		{
				Lan_Para.sData_S0|=S_RECEIVE;//???????????????
			  printf("???????????????\r\n");
			  Lan_Para.ReceiveComplet =1;
		}
		if(j&IR_TIMEOUT)//Socket????????????????? 
		{
				API_Write_W5500_SOCK_1Byte(0,Sn_CR,CLOSE);// ?????,???????????? 			
				Lan_Para.sRun_S0=0;//??????????0x00,??????????
			  Lan_Para.ErrorCode =0x03;
		    printf("Socket??????????????????????%d\r\n", Lan_Para.ErrorCode);
		}
	}

	if(API_Read_W5500_1Byte(SIR) != 0) 
		goto IntDispose;
}

void API_Process_Socket_Data(uint8_t sock_num)
{
	uint16_t size;
	static uint32_t rx_cnt = 0;
	rx_cnt++;

	size=API_Read_SOCK_Data_Buffer(sock_num, Lan_Para.LAN_RevBuf);

	if(size > 0)
	{
		printf("[RX] �������ݰ� #%lu, Socket%d, ����: %d�ֽ�\r\n", rx_cnt, sock_num, size);
		printf("     ��������: ");
		API_Printf_Hex(Lan_Para.LAN_RevBuf,size);

		// �򵥵����ݷ���
		if(size >= 4)
		{
			printf("     ���ݷ���: ǰ4�ֽ� = 0x%02X 0x%02X 0x%02X 0x%02X\r\n",
			       Lan_Para.LAN_RevBuf[0], Lan_Para.LAN_RevBuf[1],
			       Lan_Para.LAN_RevBuf[2], Lan_Para.LAN_RevBuf[3]);
		}
	}
	else
	{
		printf("[RX] Socket%d �����ݽ���\r\n", sock_num);
	}
}

void API_W5500_1MS_RunTask()//W5500??????1Ms?????��?????Tim6?��?
{
		static uint16_t Temp_Cnt;
	  if(Lan_Para.fPowerOn==1)
		{
		   Temp_Cnt++;
			 if(Temp_Cnt>1000)//1S???
			 {
						Temp_Cnt =0;
				    if( Lan_Para.fWaitingConnect == 1)//?????????
						{
								Lan_Para.WaitingConnectCnt++;
							  if(Lan_Para.WaitingConnectCnt>20)//???20??
								{
							    	Lan_Para.WaitingConnectCnt =0;
									  Lan_Para.fWaitingConnect=2;//??????
								}
						}
			 } 
		}	
}


void API_W5500_ReciveDATA_Handle(void)
{
		static uint32_t debug_cnt = 0;
		debug_cnt++;

		API_W5500_Interrupt_Process();

		// ÿ1000��(10��)��ӡһ��״̬��Ϣ
		if(debug_cnt % 1000 == 0)
		{
			uint8_t socket_status = API_Read_W5500_SOCK_1Byte(0, Sn_SR);
			printf("[DEBUG] Socket0״̬: 0x%02X, ����״̬: %s, ����: %lu\r\n",
			       socket_status,
			       (socket_status == SOCK_ESTABLISHED) ? "������" :
			       (socket_status == SOCK_INIT) ? "�ѳ�ʼ��" :
			       (socket_status == SOCK_LISTEN) ? "������" :
			       (socket_status == SOCK_CLOSED) ? "�ѹر�" : "δ֪",
			       debug_cnt);
		}

	  if((Lan_Para.sData_S0 & S_RECEIVE) == S_RECEIVE)//???Socket0?????????
		{
			printf("[RX] ��⵽���ݽ���\r\n");
			Lan_Para.sData_S0&=~S_RECEIVE;
			API_Process_Socket_Data(0);//W5500???????????????????
		}
}

void API_W5500_Send_Data_S0(uint8_t *buff,uint16_t len)
{
			static uint32_t send_cnt = 0;
			send_cnt++;

			if(Lan_Para.sRun_S0== (S_INIT|S_CONN))
			{
				Lan_Para.sData_S0&=~S_TRANSMITOK;
				printf("[TX] �������ݰ� #%lu, ����: %d�ֽ�\r\n", send_cnt, len);
				API_Write_SOCK_Data_Buffer(0, buff, len);//???Socket(0~7)???????????,???0????23???????
				printf("     ��������: ");
				API_Printf_Hex(buff,len);

				// ��鷢��״̬
				uint8_t socket_status = API_Read_W5500_SOCK_1Byte(0, Sn_SR);
				if(socket_status != SOCK_ESTABLISHED)
				{
					printf("     [����] Socket״̬�쳣: 0x%02X\r\n", socket_status);
				}
			}
			else
			{
				printf("[TX] 发送失败 - Socket未连接 (状态: 0x%02X)\r\n", Lan_Para.sRun_S0);
			}
}

// 新增：网络状态监控函数
void API_W5500_Print_Status(void)
{
	uint8_t socket_status = API_Read_W5500_SOCK_1Byte(0, Sn_SR);
	uint8_t phy_status = API_Read_W5500_1Byte(PHYCFGR);
	uint16_t tx_free = API_Read_W5500_SOCK_2Byte(0, Sn_TX_FSR);
	uint16_t rx_size = API_Read_W5500_SOCK_2Byte(0, Sn_RX_RSR);

	printf("\r\n=== W5500网络状态报告 ===\r\n");
	printf("PHY状态: 0x%02X - ", phy_status);
	if(phy_status & LINK) {
		printf("链路连接 ");
	} else {
		printf("链路断开 ");
	}
	if(phy_status & SPD) {
		printf("100M ");
	} else {
		printf("10M ");
	}
	if(phy_status & DPX) {
		printf("全双工\r\n");
	} else {
		printf("半双工\r\n");
	}

	printf("Socket0状态: 0x%02X - ", socket_status);
	switch(socket_status) {
		case SOCK_CLOSED: printf("已关闭\r\n"); break;
		case SOCK_INIT: printf("已初始化\r\n"); break;
		case SOCK_LISTEN: printf("监听中\r\n"); break;
		case SOCK_ESTABLISHED: printf("已连接\r\n"); break;
		case SOCK_CLOSE_WAIT: printf("等待关闭\r\n"); break;
		case SOCK_UDP: printf("UDP模式\r\n"); break;
		default: printf("未知状态\r\n"); break;
	}

	printf("发送缓冲区空闲: %d字节\r\n", tx_free);
	printf("接收缓冲区数据: %d字节\r\n", rx_size);
	printf("运行状态: 0x%02X\r\n", Lan_Para.sRun_S0);
	printf("错误代码: 0x%02X\r\n", Lan_Para.ErrorCode);

	// 检查是否需要重连
	if(socket_status == SOCK_CLOSED && Lan_Para.mRun_S0 == TCP_CLIENT) {
		printf("[警告] TCP连接已断开，尝试重新连接...\r\n");
		Lan_Para.sRun_S0 = 0; // 重置状态，触发重连
		Lan_Para.ErrorCode = 0; // 清除错误代码
	}

	printf("========================\r\n\r\n");
}

// 新增：网络连接检查和自动重连
void API_W5500_Check_Connection(void)
{
	static uint16_t reconnect_cnt = 0;
	uint8_t socket_status = API_Read_W5500_SOCK_1Byte(0, Sn_SR);

	// 如果是TCP客户端模式且连接断开
	if(Lan_Para.mRun_S0 == TCP_CLIENT && socket_status == SOCK_CLOSED && Lan_Para.sRun_S0 != 0) {
		reconnect_cnt++;
		if(reconnect_cnt >= 50) { // 5秒后重连 (100ms * 50)
			printf("[重连] 检测到连接断开，开始重连...\r\n");
			Lan_Para.sRun_S0 = 0; // 重置状态
			API_W5500_Socket_Set(); // 重新设置Socket
			reconnect_cnt = 0;
		}
	} else {
		reconnect_cnt = 0; // 连接正常，重置计数器
	}
}
