#include "usart.h"
//#include "gd32f4xx.h"



//���ڳ�ʼ��
//1.ʱ��ʹ��
//2.GPIO����
//3.GPIO���ã������������ٶȵȣ�
//4.��������


//uint8_t rx1_buffer[usart_size]={0};
//uint8_t rx1_counter=0;
//uint8_t message[20][usart_size]={0};
//uint8_t message_num=0;

//uint8_t usart_flag=0;
//uint8_t usart_counter=0;
//uint8_t usart_tx_buffer[256]={0};

#define SUPPORT_USART_NUM       1
#define SUPPORT_FUN_MAX_NUM     1

/** ����BUF��С */
#define USART_BUF_SIZE          (2048ul)  // ����/���ջ���

/**
  * @breif ���ջ�����п��ƽṹ�嶨��
  */
typedef struct
{
    uint8_t  state;                                 /*!< ����״̬ */
    
    uint16_t  end;                                   /*!< ѭ������β�ڱ� */
    
    uint16_t  head;                                  /*!< ѭ���������ڱ� */
    
    uint16_t  num;                                   /*!< ѭ���������ܴ洢��������� */
}UBufferCtrl_t;

/**
  * @brief USART �����Ϣ�ṹ�嶨��
  */
typedef struct{
    uint8_t arrSendBuf[USART_BUF_SIZE];            	        /*!< ���ͻ��� */

    uint8_t *pRecvBuf;                                      /*!< ���ջ��� */
    
    UBufferCtrl_t tRecvBufCtrl;                             /*!< ���ջ�����п��� */

    uint8_t ucRecvByte1msTic;                      		    /*!< ����һ�ֽ�1ms��ʱ */
    
    uint8_t isRecvEnd;                              	    /*!< ������ɱ�־ */
    
    uint16_t uiSendBufLenth;                        	    /*!< ���ͻ��泤�� */
    
    UsartFun pCallFun[SUPPORT_FUN_MAX_NUM];                 /*!< ע��Ľ��ն�ʱ�ص����� */
} USART_DriveHandleType;

__attribute__((section (".RAM_D3"))) USART_DriveHandleType sg_tUsartDriveHandle[SUPPORT_USART_NUM] ;
__attribute__((section (".RAM_D3"))) uint8_t sg_arrUasrt2RecvBuf[USART_BUF_SIZE + 1];
__attribute__((section (".RAM_D3"))) uint8_t tmpBuf_test[USART_BUF_SIZE + 1] = {0};
__attribute__((section (".RAM_D3"))) uint8_t usart2_buf=0;


void USART0_Init(void)
{
    /* enable GPIO clock */
    rcu_periph_clock_enable(RCU_GPIOA);  // 改为GPIOA

    /* enable USART clock */
    rcu_periph_clock_enable(RCU_USART0);

	  /* configure the USART0 Tx pin and USART0 Rx pin */
    gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_9);   // PA9 - TX
    gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_10);  // PA10 - RX

	  /* configure USART0 Tx as alternate function push-pull */
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_9);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_9);

    /* configure USART0 Rx as alternate function push-pull */
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_10);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_10);
	
	  /* USART configure */
    usart_deinit(USART0);
    usart_baudrate_set(USART0, 115200U);
    usart_receive_config(USART0, USART_RECEIVE_ENABLE);
    usart_transmit_config(USART0, USART_TRANSMIT_ENABLE);
    usart_enable(USART0);

		/* USART interrupt configuration */
//    nvic_irq_enable(USART0_IRQn, 0, 0);
//    usart_interrupt_enable(USART0, USART_INT_RBNE);
//		usart_interrupt_enable(USART0, USART_INT_IDLE);
//		usart_interrupt_enable(USART1, USART_INT_TBE);

    // 发送测试字符确保串口工作正常
    usart_data_transmit(USART0, 'U');
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    usart_data_transmit(USART0, 'A');
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    usart_data_transmit(USART0, 'R');
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    usart_data_transmit(USART0, 'T');
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    usart_data_transmit(USART0, '\r');
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    usart_data_transmit(USART0, '\n');
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
}

void USART2_Init(void)
{
    /* enable GPIO clock */
    rcu_periph_clock_enable(RCU_GPIOB);

    /* enable USART clock */
    rcu_periph_clock_enable(RCU_USART2);
	
	  /* configure the USART0 Tx pin and USART0 Rx pin */
    gpio_af_set(GPIOB, GPIO_AF_7, GPIO_PIN_10);
    gpio_af_set(GPIOB, GPIO_AF_7, GPIO_PIN_11);
	   
	  /* configure USART0 Tx as alternate function push-pull */
    gpio_mode_set(GPIOB, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_10);
    gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_10);

    /* configure USART0 Rx as alternate function push-pull */
    gpio_mode_set(GPIOB, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_11);
    gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_11);
	
	  /* USART configure */
    usart_deinit(USART2);
    usart_baudrate_set(USART2, 115200U);
    usart_receive_config(USART2, USART_RECEIVE_ENABLE);
    usart_transmit_config(USART2, USART_TRANSMIT_ENABLE);
    usart_enable(USART2); 
		
		/* USART interrupt configuration */
    nvic_irq_enable(USART2_IRQn, 0, 0);
    usart_interrupt_enable(USART2, USART_INT_RBNE);
		usart_interrupt_enable(USART2, USART_INT_IDLE);
//		usart_interrupt_enable(USART1, USART_INT_TBE);
}


/* retarget the C library printf function to the USART */
int fputc(int ch, FILE *f)
{
    usart_data_transmit(USART0, (uint8_t)ch);
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    return ch;
}

void API_Printf_Hex(uint8_t *p_data, uint16_t length)
{
    uint16_t i;
	  for(i=0;i<length;i++)
	  {
		   printf("0x%02X",p_data[i]);
			 printf(" ");
		}
}

static int InitBuffer(USART_DriveHandleType *pHandle, uint8_t *pBuf, uint16_t size)
{
    pHandle->tRecvBufCtrl.end = 0;
    pHandle->tRecvBufCtrl.head = 0;
    pHandle->tRecvBufCtrl.num = size;
    pHandle->tRecvBufCtrl.state = 0x01;
    pHandle->pRecvBuf = pBuf;
    
    memset(pBuf, 0, size);

    return 0;
}

void ESP32_IO_Init(void)
{
    /* Once the WiFi UART is intialized, start an asynchrounous recursive
     listening. the HAL_UART_Receive_IT() call below will wait until one char is
     received to trigger the HAL_UART_RxCpltCallback(). The latter will recursively
     call the former to read another char.  */
	
     memset(&sg_tUsartDriveHandle, 0, sizeof(sg_tUsartDriveHandle));
     InitBuffer(&sg_tUsartDriveHandle[USART_ESP8266], sg_arrUasrt2RecvBuf, sizeof(sg_arrUasrt2RecvBuf) - 1);
 
//          HAL_UART_Receive_IT(&huart3, &usart3_buf, 1); 
//		    __HAL_UART_ENABLE_IT(&huart3, UART_IT_IDLE) ;   //ʹ��idle�ж�


}

/**
  * @brief      �����ֽڵ�������
  * @param[in][out] pCtrl - UBufferCtrl_t �������
  * @param[in]  pBuf - ����
  * @param[in]  data - �ֽ�����
  * @retval     ���ص�ֵ��������
  *             @arg -1: д��ʧ��
  *             @arg 0: д��ɹ�
  */
static int AddByteToBuffer(UBufferCtrl_t* pCtrl, uint8_t *pBuf, uint8_t data)
{
    if (0x00 == ((pCtrl->state) & 0x02) && pBuf != NULL)                      /* �����򻺳���д������ */
    {
        pBuf[(pCtrl->end)++] = data;

        if ((pCtrl->end) >= (pCtrl->num))
        {
            (pCtrl->end) = 0;
        }

        if (((pCtrl->state) & 0x01) != 0x00)                 /* �������������ݴ����ͻ��� */
        {
            (pCtrl->state) &= ~0x01;                         /* �������������ݴ����ͻ��� */
        }
        else if (((pCtrl->state) & 0x04) != 0x00)            /* ���������� */
        {
            (pCtrl->head) = (pCtrl->end);                     /* ��β�ڱ��ƶ����ڱ� */
        }
        else if ((pCtrl->end) == (pCtrl->head))
        {
            if (pCtrl->state & 0x80)                         /* �Ƿ����������� */
            {
                (pCtrl->state) |= 0x02;                      /* ��ֹ�򻺳���д������ */
            }
            else
            {
                (pCtrl->state) |= 0x04;                      /* ���������� */
            }
        }

        return 0;
    }

    return -1;
}

/**
  * @brief      �ӻ����ж�ȡ�ֽ�������
  * @param[in][out] pCtrl - UBufferCtrl_t �������
  * @param[in]  pBuf - ����
  * @param[out] pOut - ��ȡд��Ļ���
  * @return     ��ȡ�����ֽڳ���
  */
static int ReadBytesToBuffer(UBufferCtrl_t* pCtrl, uint8_t *pBuf, uint8_t *pOut)
{
    uint16_t readlenth = 0;
    
    if (((pCtrl->state) & 0x01) == 0x00 && pBuf != NULL)
    {
        if (pCtrl->end > pCtrl->head)
        {
            readlenth = pCtrl->end - pCtrl->head;
            memcpy(pOut, &pBuf[pCtrl->head], readlenth);
        }
        else
        {
            readlenth = pCtrl->num - pCtrl->head + 1;
            memcpy(pOut, &pBuf[pCtrl->head - 1], readlenth);
            memcpy(&pOut[readlenth], &pBuf[0], pCtrl->end - 1);
            readlenth += pCtrl->end - 1;
        }
        
        pCtrl->head = 0;
        pCtrl->end  = 0;
        
     if (((pCtrl->state) & 0x02) == 0x00)     /* �����򻺳����д������� */
        {
            (pCtrl->state) |= 0x01;              /* �������������ݵȴ��������� */
        }
    }
    
    (pCtrl->state) &= ~0x02;                         /* �����򻺳����д������� */
    (pCtrl->state) &= ~0x04;                         /* ������δ�� */
    
    return readlenth;
}

/**
  * @brief      �������ݴ�������.
  * @param[in][out] pHandle ���ڴ������
  * @param[in]  data ��������
  * @retval     None
  */
static void RecvDataHandler(USART_DriveHandleType *pHandle, uint8_t data)
{

    if (AddByteToBuffer(&pHandle->tRecvBufCtrl, pHandle->pRecvBuf, data))
    {
        pHandle->isRecvEnd = 1;
    }
}

void FML_USART_RecvTask(uint8_t drv)
{
     uint16_t i;
    uint16_t lenth;
    uint8_t tmpBuf[USART_BUF_SIZE + 1] = {0};

    if (drv < SUPPORT_USART_NUM)
    {
 
            sg_tUsartDriveHandle[drv].isRecvEnd = 0;
            lenth = ReadBytesToBuffer(&sg_tUsartDriveHandle[drv].tRecvBufCtrl, sg_tUsartDriveHandle[drv].pRecvBuf, tmpBuf);
 		      	memcpy(tmpBuf_test,tmpBuf,lenth);
// printf("-------\n");
//			if(drv==0)
//			{
//			 	for(uint16_t i=0;i<lenth;i++)
//        printf("%c",tmpBuf[i]);
//			}

// printf("---back----\n");
            if (lenth > 0)
            {
                tmpBuf[lenth + 1] = 0;
                
                for (i = 0; i < SUPPORT_FUN_MAX_NUM; i++)
                {
                    if (sg_tUsartDriveHandle[drv].pCallFun[i] != NULL)
                    {
                        sg_tUsartDriveHandle[drv].pCallFun[i](tmpBuf, lenth);
                    }
                }
            }      
    }
}

 /**
  * @brief      ע�ᴮ�����ݽ��ջص�����.
  * @param[in]  drv ��������
  * @param[in]  pUsartFun �ص�����
  * @retval     0,�ɹ�; -1,ʧ��
  */
int FML_USART_Register(uint8_t drv, const UsartFun pUsartFun)
{
    int i;
    
    if (drv < SUPPORT_USART_NUM)
    {
        for (i = 0; i < SUPPORT_FUN_MAX_NUM; i++)
        {
            if (sg_tUsartDriveHandle[drv].pCallFun[i] == 0)
            {
                sg_tUsartDriveHandle[drv].pCallFun[i] = pUsartFun;
                return 0;
            }
        }
    }
    
    return -1;
}

void UARTx_SendBuffer(uint32_t usart_periph,uint8_t* pSda,uint16_t len)
{
	int i=0;
	for(i=0;i<len;i++)
	{
	  while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE)){};
      usart_data_transmit(usart_periph, *pSda);		
	    pSda++;
	}
	 while(RESET == usart_flag_get(usart_periph, USART_FLAG_TC)){};
}

//���ڽ����ж� 
void UART_RxCpltCallback(uint32_t usart_periph)
{
//  if( usart_periph == USART2 )
//	{
//	   rx1_buffer[rx1_counter++] = (uint8_t)usart_data_receive(USART2);
//		if(rx1_counter>usart_size)
//			rx1_counter=0;
//	}
	if( usart_periph == USART2 )
	{
	   usart2_buf = (uint8_t)usart_data_receive(USART2);
		 RecvDataHandler(&sg_tUsartDriveHandle[USART_ESP8266], usart2_buf);
	}
	
	
}
//���ڿ����ж�
void UART_IDLECallBack(uint32_t usart_periph)
{
	if(usart_periph == USART2)
	{
		usart_interrupt_flag_clear(USART2,USART_INT_FLAG_IDLE);	
	  usart_data_receive(USART2);			
    FML_USART_RecvTask( USART_ESP8266 );
		
//		
//		memset (message[message_num], '\0', usart_size);
//	  memcpy(message[message_num],rx1_buffer,rx1_counter);
//		message_num++;
//		if(message_num==20)
//			message_num=0;
//		memset (rx1_buffer, '\0', usart_size);
//		usart_counter=rx1_counter;
//		rx1_counter=0;
//		
//		usart_flag=1;
	}		
}

