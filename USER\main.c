#include "gd32f4xx.h"
#include "gd32f470v_start.h"
#include "systick.h"
#include "usart.h"
#include <stdio.h>
#include <string.h>
#include "time.h"
#include "rtc.h"
#include "adc.h"
#include "dma.h"
#include "gpio.h"
#include "dac.h"
#include "drv_usb_hw.h"
#include "cdc_acm_core.h"
#include "usb.h"

#include "esp32_wifi.h"
#include "arm_math.h"

#include "API_TNRG.h"
#include "API_W5500.h"


uint8_t TeseBuff[15]={0x01,0x02,0x03,0x04,0x05,
                      0x06,0x11,0x12,0x13,0x14,
                      0x15,0x16,0x17,0xaa,0xbb};
 __attribute__((section (".RAM_D3"))) float  USB_send[420]={0.0};

void key_funtion(void);
void ADC_RunTask(void);
/*!
    \brief      main function
    \param[in]  none
    \param[out] none
    \retval     none
*/
int main(void)
{
	/* ����ҪBootloader�������Flash��ʼ��ַ���У���������������ƫ�� */
 // nvic_vector_table_set(NVIC_VECTTAB_FLASH, 0x40000);  // ע�͵�������ƫ������
 /* enable global interrupt, the same as __set_PRIMASK(0) */
 __enable_irq();

 	// 首先初始化系统时钟
 	systick_config();

	// 初始化串口并等待稳定
	USART0_Init();

	// 延时确保串口稳定
	for(volatile uint32_t i = 0; i < 1000000; i++);

	printf("USART0 PA9/PA10 初始化完成\r\n");

    printf("\r\n\r\n");
    printf("========================================\r\n");
    printf("  GD32F470 + W5500 网络通信系统启动\r\n");
    printf("  编译时间: %s %s\r\n", __DATE__, __TIME__);
    printf("========================================\r\n");

    printf("系统初始化开始...\r\n");
    printf("✓ 系统时钟配置完成\r\n");

	  GPIO_Init();
	  printf("? GPIO????????\r\n");



  	TIMER6_Init();
  	printf("? ?????6????????\r\n");

	  TIMER3_Init();
	  printf("? ?????3????????\r\n");

	  RTC_Init();
	  printf("? RTC????????\r\n");

	  TIMER1_Init();
	  printf("? ?????1????????\r\n");

  	SPI1_Init();
  	printf("? SPI1????????\r\n");

	  API_RNG_Init();//?????????????????MAC???
	  printf("? ???????????????????\r\n");

	  timer_enable(TIMER1);
	  printf("? ?????1????\r\n");

		API_Init_LAN();//????????TWXj

		printf("????????????????????...\r\n");
		printf("????????: ");
		for(int i=0; i<15; i++) {
			printf("0x%02X ", TeseBuff[i]);
		}
		printf("\r\n\r\n");

    while(1)
		{

				if(g_tTimeSign.bTic10msSign)                      /* 10ms */
				{
					  API_W5500_ReciveDATA_Handle();//??????????
						g_tTimeSign.bTic10msSign = FALSE;
				}
				if(g_tTimeSign.bTic100msSign)                      /* 100ms */
				{
				  	API_W5500_Send_Data_S0(TeseBuff,15);//���ڷ�������
				  	API_W5500_Check_Connection();//�������״̬
						g_tTimeSign.bTic100msSign = FALSE;
				}


    }
}





