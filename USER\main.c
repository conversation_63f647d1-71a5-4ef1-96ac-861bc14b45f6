#include "gd32f4xx.h"
#include "gd32f470v_start.h"
#include "systick.h"
#include "usart.h"
#include <stdio.h>
#include <string.h>
#include "time.h"
#include "rtc.h"
#include "adc.h"
#include "dma.h"
#include "gpio.h"
#include "dac.h"
#include "drv_usb_hw.h"
#include "cdc_acm_core.h"
#include "usb.h"

#include "esp32_wifi.h"
#include "arm_math.h"

#include "API_TNRG.h"
#include "API_W5500.h"


uint8_t TeseBuff[15]={0x01,0x02,0x03,0x04,0x05,
                      0x06,0x11,0x12,0x13,0x14,
                      0x15,0x16,0x17,0xaa,0xbb};
 __attribute__((section (".RAM_D3"))) float  USB_send[420]={0.0};

void key_funtion(void);
void ADC_RunTask(void);
/*!
    \brief      main function
    \param[in]  none
    \param[out] none
    \retval     none
*/
int main(void)
{
	/* set the NVIC vector table base address to APP code area */
 // nvic_vector_table_set(NVIC_VECTTAB_FLASH, 0x40000);  // 如果从0x08000000开始则注释掉
 nvic_vector_table_set(NVIC_VECTTAB_FLASH, 0x40000);     // 如果从0x08040000开始则保留
 /* enable global interrupt, the same as __set_PRIMASK(0) */
 __enable_irq();
	
		  USART0_Init();
	  printf("? ����0��ʼ�����\r\n");

    printf("\r\n\r\n");
    printf("========================================\r\n");
    printf("  GD32F470 + W5500 ����ͨ��ϵͳ����\r\n");
    printf("  ����ʱ��: %s %s\r\n", __DATE__, __TIME__);
    printf("========================================\r\n");

    printf("ϵͳ��ʼ����ʼ...\r\n");
    systick_config();
    printf("? ϵͳʱ���������\r\n");

	  GPIO_Init();
	  printf("? GPIO��ʼ�����\r\n");



  	TIMER6_Init();
  	printf("? ��ʱ��6��ʼ�����\r\n");

	  TIMER3_Init();
	  printf("? ��ʱ��3��ʼ�����\r\n");

	  RTC_Init();
	  printf("? RTC��ʼ�����\r\n");

	  TIMER1_Init();
	  printf("? ��ʱ��1��ʼ�����\r\n");

  	SPI1_Init();
  	printf("? SPI1��ʼ�����\r\n");

	  API_RNG_Init();//?????????????????MAC???
	  printf("? �������������ʼ�����\r\n");

	  timer_enable(TIMER1);
	  printf("? ��ʱ��1����\r\n");

		API_Init_LAN();//????????TWXj

		printf("ϵͳ��ʼ����ɣ�������ѭ��...\r\n");
		printf("��������: ");
		for(int i=0; i<15; i++) {
			printf("0x%02X ", TeseBuff[i]);
		}
		printf("\r\n\r\n");

    while(1)
		{

				if(g_tTimeSign.bTic10msSign)                      /* 10ms */
				{
					  API_W5500_ReciveDATA_Handle();//??????????
						g_tTimeSign.bTic10msSign = FALSE;
				}
				if(g_tTimeSign.bTic100msSign)                      /* 100ms */
				{
				  	API_W5500_Send_Data_S0(TeseBuff,15);//网口发送数据
				  	API_W5500_Check_Connection();//检查连接状态
						g_tTimeSign.bTic100msSign = FALSE;
				}


    }
}





