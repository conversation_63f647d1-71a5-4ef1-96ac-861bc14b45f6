# GD32F470 + W5500 项目配置说明

## 内存配置修改

### 1. 主函数修改
已将向量表偏移设置注释掉，程序现在从Flash起始地址运行：

```c
int main(void)
{
    /* 不需要Bootloader，程序从Flash起始地址运行，无需设置向量表偏移 */
    // nvic_vector_table_set(NVIC_VECTTAB_FLASH, 0x40000);  // 注释掉向量表偏移设置
    /* enable global interrupt, the same as __set_PRIMASK(0) */
    __enable_irq();
    // ...
}
```

### 2. 链接脚本修改
修改了 `Objects/boot.sct` 文件：

```
; 修改前（Bootloader + APP模式）
LR_IROM1 0x08040000 0x00C0000  {    ; 从256KB偏移开始
  ER_IROM1 0x08040000 0x00C0000  {

; 修改后（单独APP模式）  
LR_IROM1 0x08000000 0x00200000  {    ; 从Flash起始地址开始，2MB空间
  ER_IROM1 0x08000000 0x00200000  {
```

### 3. 内存布局
- **Flash起始地址**: 0x08000000 (2MB空间)
- **SRAM1**: 0x20000000 (128KB)
- **SRAM2**: 0x20020000 (192KB) 
- **SRAM3**: 0x10000000 (64KB)

## 编译和烧录

### 1. Keil编译
- 项目配置已正确设置TextAddressRange为0x08000000
- 直接编译即可生成从0x08000000开始的程序

### 2. 烧录方式
可以使用以下任一方式烧录：

#### 方式1：Keil直接下载
- 在Keil中点击Download按钮
- 程序会自动烧录到0x08000000地址

#### 方式2：J-Link烧录
```bash
# 使用J-Link Commander
loadfile Objects\boot.hex
r
g
```

#### 方式3：ST-Link烧录
```bash
# 使用STM32CubeProgrammer
STM32_Programmer_CLI.exe -c port=SWD -w Objects\boot.hex -v -rst
```

## 调试信息

程序已添加详细的串口调试信息：

### 1. 系统启动信息
- 各模块初始化状态
- 网络参数配置信息
- W5500初始化过程

### 2. 网络状态监控
- 每10秒打印网络状态报告
- 实时连接状态监控
- 数据收发统计

### 3. 数据传输调试
- 发送数据包计数和内容显示
- 接收数据包分析
- 连接异常检测和重连机制

## 串口输出示例

```
========================================
  GD32F470 + W5500 网络通信系统启动
  编译时间: Dec  7 2024 15:30:25
========================================
系统初始化开始...
✓ 系统时钟配置完成
✓ GPIO初始化完成
✓ 串口0初始化完成
...

=== W5500网络初始化开始 ===
1. 初始化SPI0接口...
   SPI0初始化完成
2. 初始化GPIO控制引脚...
   GPIO初始化完成
3. 初始化网络参数...
   网络参数配置完成
   本机IP: *************:5000
   网关IP: ***********
   子网掩码: *************
...

[TX] 发送数据包 #1, 长度: 15字节
     数据内容: 01 02 03 04 05 06 11 12 13 14 15 16 17 AA BB

=== W5500网络状态报告 ===
PHY状态: 0x01 - 链路连接 10M 全双工
Socket0状态: 0x17 - 已连接
发送缓冲区空闲: 2048字节
接收缓冲区数据: 0字节
运行状态: 0x03
错误代码: 0x00
========================
```

## 注意事项

1. **确保网线连接正常**
2. **检查网络配置是否与您的网络环境匹配**
3. **串口波特率默认为115200**
4. **如需修改IP地址，请修改`API_Init_Net_Parameters()`函数**
