#ifndef __USER_STEP_H__
#define __USER_STEP_H__

//#define __FPU_PRESENT 1

//#include "main.h"
 #include "25LC080A.h"
 #include "spi.h"
 #include "esp32_wifi.h"
 #include "dac.h"
 #include "adc.h"
 #include "mymath.h"
 #include "flash.h"

 #include "rtc.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
 #define ROUND_TO_UINT16(x)   ((uint16_t)(x)+0.5)>(x)? ((uint16_t)(x)):((uint16_t)(x)+1) 

typedef struct  
{
  uint8_t year_char[4]; //"2021"
  uint8_t month_char[3]; //"Jun"
  uint8_t week_char[3];  //"Sun" 
  uint8_t day_char[2];   //"31"
  uint8_t hour_char[2];   //"12"
  uint8_t min_char[2];   //"30"
  uint8_t sec_char[2];   //"30"
	
 uint16_t year_num ; //2021
 uint8_t month_num; //6
 uint8_t week_num;  //3
 uint8_t day_num;   //31
 uint8_t hour_num;   //12
 uint8_t min_num;   //30
 uint8_t sec_num;   //30
	
 uint8_t enable;
 uint8_t useful;
	
} Net_time_t;

extern  Net_time_t my_net_time;

/**
  * @brief 网络连接类型
  */
/**
  * @breif WIFI 信息结构体定义
  */
typedef struct
{
 
   char szSsid[32];                     /*!< WIFI 的名称 */
   uint8_t  len_WifiSSID			 ;				//最大32

   char szPassword[32];               /*!< WIFI 的密码 */
	 uint8_t  len_WifiPASSWORD	 ;	   	//最大16
	
   uint8_t signalIntensity;         /*!< 信号强度, 0-10: 数值越大信号越好 */
} User_WifiInfo_t;
/**
  * @breif TCP 状态信息结构体定义
  */
typedef struct
{
 
    char szNetAddr[32];                             /*!< TCP/UDP 网络地址/域名 */

    uint16_t netPort;                               /*!< TCP/UDP 网络通信端口号 */

 } User_SeverInfo_t;


/**
  * @breif 设备信息结构体定义
*/
typedef struct  
{

 User_WifiInfo_t     Curr_Wifi_info;
 User_SeverInfo_t    Currs_Sever_info;
 uint8_t STM32_CPUID[20];	  /* CPUID */
 uint8_t Device_ID[16];	    /* 设备ID */
	
 uint8_t   Bin_Ver[2];	      /* 后台固件版本 16位 Version[0]高八位 Version[1]低八位 */
 uint8_t   soft_Ver[3];    /*  软件程序版本*/
 uint8_t   Hard_Ver[2];     /* 硬件版本 例：V5.3 Hard_Ver[0]=5； Hard_Ver[1]=3；*/
 uint8_t  AT_version[16];  /* AT固件版本号 */

 uint8_t WiFi_Send_Mode;   /* 数据传输模式 0-精简 1-不分包全数据 2-分包全数据 */
 uint16_t WiFi_Send_Count;  /* 发送数据索引 */ 
	uint8_t Pd_auto_flag;     /* PD校准标志 0-不校准 1-离床校准 2-在床校准 */ 
	
	uint8_t	org_datashow_enable;/* 串口原始数据传输使能 0-不传输 1-传输 */ 
	uint16_t	dac_setval;        /* 驱动电流参数 */ 
	
	
} ESP_User_DeviceInfo_t;
extern    ESP_User_DeviceInfo_t my_device_info;





typedef struct
{
  uint8_t  Command[20];
	uint8_t  Command_plus[8];
  uint8_t  Data_buf[20][20];
	uint8_t  Data_plus[8][1050];
  uint8_t tail; 				
  uint8_t head;
	uint8_t tail_plus; 				
  uint8_t head_plus;
}WIFI_CommandRingBuf;
typedef struct
{
  uint8_t  Command[20];
  uint8_t  Data_buf[20][20];
  uint8_t tail; 				
  uint8_t head;
 
}BLE_CommandRingBuf;
 

void ESP32_Command_Add( uint8_t * command, uint8_t * Data_buf);
void ESP32_Command_Add_Plus( uint8_t * command, uint8_t * Data_buf);
//void ESP32_Command_Read( uint8_t * command, uint8_t * Data_buf);
//void ESP32_Command_Read_Plus( uint8_t * command, uint8_t * Data_buf);
 void ESP32BLE_Command_Add( uint8_t * command, uint8_t * Data_buf);
 
uint8_t Get_zhcdInFo( void );
 
uint8_t GetWifiInFo(void);	//获得WIFI信息并设置
uint8_t SetWifiInFo(void);	//写入ROM
void    Get_SeverInfo( uint8_t * Sever_IP, uint16_t * Sever_port);
uint8_t Set_SeverInfo(uint8_t * Sever_IP, uint16_t * Sever_port );	//写入ROM
uint8_t Get_DevVersion( void );
uint8_t Set_DevVersion( void );
uint8_t Get_DevID(void);	 
uint8_t Set_DevID(void);	 


uint8_t Get_HardVer( void );
uint8_t Set_HardVer( void );

uint8_t Get_AlgorithmInFo( void );
uint8_t Set_AlgorithmInFo( void );

void User_Init(void);
void IPD_Command(void);
void WIFI_Send_AD(uint8_t * heart_data,uint8_t * breath_data);
  void SysReset_Condition(uint32_t Init_num);
void	PD_AUTO_Process( uint16_t PD_set_val );


////typedef struct
////{
////  uint8_t  SendData_buf[20][20];
////  uint8_t  SendData_len[20];
////  uint16_t tail; 				
////  uint16_t head;
////}SendDataRingBuf;

///**
//  * @breif AT发送数据任务列表结构体定义
//  */
//typedef struct
//{
//    uint8_t  state;                                 /*!< 控制状态 */

//    uint8_t  end;                                   /*!< 循环队列尾哨兵 */

//    uint8_t  head;                                  /*!< 循环队列首哨兵 */
//	
//    uint8_t  num;                                   /*!< 循环队列中能存储的最多组数 */

//    uint8_t  isFirst;                               /*!< 循环队列首哨兵首次读取 */

//    uint8_t  SendData_buf[20][20];                 /*!< 任务列表 */
//	
//	  uint16_t  SendData_len[20];

//} SendDataRingBuf;




// 


///** @defgroup RTC_Month_Date_Definitions RTC Month Date Definitions
//  * @{
//  */


///** @defgroup RTC_WeekDay_Definitions RTC WeekDay Definitions
//  * @{
//  */
//#define RTC_WEEKDAY_MONDAY             ((uint8_t)0x01)
//#define RTC_WEEKDAY_TUESDAY            ((uint8_t)0x02)
//#define RTC_WEEKDAY_WEDNESDAY          ((uint8_t)0x03)
//#define RTC_WEEKDAY_THURSDAY           ((uint8_t)0x04)
//#define RTC_WEEKDAY_FRIDAY             ((uint8_t)0x05)
//#define RTC_WEEKDAY_SATURDAY           ((uint8_t)0x06)
//#define RTC_WEEKDAY_SUNDAY             ((uint8_t)0x07)
///**
//  * @}
//  */



//extern char* Week_name[];
//extern char* Month_name[];
//uint8_t  week_str2num(char *week);
//uint8_t  month_str2num(char *month);

 //void FloatToUint8(uint8_t* char_array,float *data,uint16_t data_len);
//void Uint8ToFloat(uint8_t * char_array,float *data,uint16_t data_len);
//void Uint8ToInt16(uint8_t * char_array,int16_t  *data,uint16_t data_len);
//void FloatToUInt8_1000_3300(uint8_t* char_array,int16_t  *data, int16_t * ref , int16_t * amp  ,uint16_t data_len);
//void Auto_Show_FloatToUInt8( float *data_in ,float *ShowRange_AMP,float *ShowRange_REF,uint16_t data_len);
//void Auto_Show( int16_t *data_in ,int16_t  *ShowRange_AMP,int16_t  *ShowRange_REF,uint16_t data_len);
//void Downsample_1024_100(float* data_in,float *data_out,uint16_t data_len);
//void U8TOINT16(uint8_t * data_in,int16_t *data_out,uint16_t data_len);
//void FloatToUInt8_0_0(uint8_t* char_array,float *data,uint16_t data_len);
//void Get_OFC_data(uint8_t type);
//void user_init(void);


//uint8_t GetWifiInFo(void);	//获得WIFI信息并设置
//uint8_t SetWifiInFo(void);	//写入ROM
//uint8_t Set_SeverInfo(uint8_t * Sever_IP, uint16_t * Sever_port );	//写入ROM
//void Get_SeverInfo( uint8_t * Sever_IP, uint16_t * Sever_port);
//uint8_t Get_DevVersion( void );
//uint8_t Set_DevVersion( void );
//uint8_t Get_DevID(void);	 
//uint8_t Set_DevID(void);	 




// 
// int ESP32_SendData_Read(SendDataRingBuf* info, uint8_t * SendData,uint16_t *data_len);
// int ESP32_SendData_Add(SendDataRingBuf* info, uint8_t * SendData,uint16_t data_len);
// 


// 
//     
//extern uint8_t chuanshu_data;


#endif /*__USER_STEP_H__ */
