.\objects\api_w5500.o: Utilities\API_W5500.c
.\objects\api_w5500.o: Utilities\API_W5500.h
.\objects\api_w5500.o: Utilities\spi.h
.\objects\api_w5500.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\api_w5500.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\api_w5500.o: .\Firmware\CMSIS\core_cm4.h
.\objects\api_w5500.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h
.\objects\api_w5500.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\api_w5500.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\api_w5500.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h
.\objects\api_w5500.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\api_w5500.o: .\USER\gd32f4xx_libopt.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\api_w5500.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\api_w5500.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\api_w5500.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\api_w5500.o: .\USER\systick.h
.\objects\api_w5500.o: Utilities\25LC080A.h
.\objects\api_w5500.o: Utilities\My_CRC.h
.\objects\api_w5500.o: Utilities\eeprom_spi.h
.\objects\api_w5500.o: Utilities\usart.h
.\objects\api_w5500.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
.\objects\api_w5500.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
