<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\boot.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\boot.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Thu Aug 07 18:11:24 2025
<BR><P>
<H3>Maximum Stack Usage =       2112 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
USART2_IRQHandler &rArr; UART_IDLECallBack &rArr; FML_USART_RecvTask &rArr; ReadBytesToBuffer
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[2b]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2b]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[2b]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">CAN0_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">CAN1_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[67]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">DMA1_Channel0_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">DMA1_Channel1_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">DMA1_Channel2_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">EXTI10_15_IRQHandler</a> from gd32f4xx_it.o(i.EXTI10_15_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">EXTI5_9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1d]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[69]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[71]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[10]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">PendSV_Handler</a> from gd32f4xx_it.o(i.PendSV_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1e]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1c]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[f]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">SDIO_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6c]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6d]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6e]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">SVC_Handler</a> from gd32f4xx_it.o(i.SVC_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[73]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[1b]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">TIMER1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">TIMER2_IRQHandler</a> from gd32f4xx_it.o(i.TIMER2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">TIMER3_IRQHandler</a> from gd32f4xx_it.o(i.TIMER3_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">TIMER6_IRQHandler</a> from gd32f4xx_it.o(i.TIMER6_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[70]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6f]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[68]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6a]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6b]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">USART0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">USART2_IRQHandler</a> from gd32f4xx_it.o(i.USART2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">USBFS_IRQHandler</a> from gd32f4xx_it.o(i.USBFS_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">USBFS_WKUP_IRQHandler</a> from gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[66]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[74]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[d]">_usb_config_desc_get</a> from usbd_enum.o(i._usb_config_desc_get) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[c]">_usb_dev_desc_get</a> from usbd_enum.o(i._usb_dev_desc_get) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[1]">_usb_std_clearfeature</a> from usbd_enum.o(i._usb_std_clearfeature) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[7]">_usb_std_getconfiguration</a> from usbd_enum.o(i._usb_std_getconfiguration) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[5]">_usb_std_getdescriptor</a> from usbd_enum.o(i._usb_std_getdescriptor) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[9]">_usb_std_getinterface</a> from usbd_enum.o(i._usb_std_getinterface) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[0]">_usb_std_getstatus</a> from usbd_enum.o(i._usb_std_getstatus) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[2]">_usb_std_reserved</a> from usbd_enum.o(i._usb_std_reserved) referenced 4 times from usbd_enum.o(.data)
 <LI><a href="#[4]">_usb_std_setaddress</a> from usbd_enum.o(i._usb_std_setaddress) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[8]">_usb_std_setconfiguration</a> from usbd_enum.o(i._usb_std_setconfiguration) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[6]">_usb_std_setdescriptor</a> from usbd_enum.o(i._usb_std_setdescriptor) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[3]">_usb_std_setfeature</a> from usbd_enum.o(i._usb_std_setfeature) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[a]">_usb_std_setinterface</a> from usbd_enum.o(i._usb_std_setinterface) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[b]">_usb_std_synchframe</a> from usbd_enum.o(i._usb_std_synchframe) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[e]">_usb_str_desc_get</a> from usbd_enum.o(i._usb_str_desc_get) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[75]">fputc</a> from usart.o(i.fputc) referenced from printf8.o(i.__0printf$8)
 <LI><a href="#[72]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[74]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[131]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[76]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[7e]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[132]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[133]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[134]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[135]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[136]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[137]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[f]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c2]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadBytesToBuffer
</UL>

<P><STRONG><a name="[127]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
</UL>

<P><STRONG><a name="[138]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[79]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[139]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[13a]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[78]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[c0]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
</UL>

<P><STRONG><a name="[13b]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[7a]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[7b]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[7c]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[13c]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[77]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[13d]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[7d]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[13e]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[13f]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[140]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[7f]"></a>ADC_ConvCpltCallback</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, adc.o(i.ADC_ConvCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_ConvCpltCallback &rArr; dma_interrupt_flag_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel2_IRQHandler
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel0_IRQHandler
</UL>

<P><STRONG><a name="[81]"></a>ADC_ConvHalfCpltCallback</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, adc.o(i.ADC_ConvHalfCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_ConvHalfCpltCallback &rArr; dma_interrupt_flag_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel2_IRQHandler
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel0_IRQHandler
</UL>

<P><STRONG><a name="[82]"></a>API_Chose_TS5A3359_GAIN</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, gpio.o(i.API_Chose_TS5A3359_GAIN))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = API_Chose_TS5A3359_GAIN
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[85]"></a>API_Detect_Gateway</STRONG> (Thumb, 212 bytes, Stack size 16 bytes, api_w5500.o(i.API_Detect_Gateway))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = API_Detect_Gateway &rArr; API_Write_W5500_SOCK_4Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[8b]"></a>API_Init_LAN</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, api_w5500.o(i.API_Init_LAN))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = API_Init_LAN &rArr; API_Init_Net_Parameters &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Socket_Set
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Init
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_Net_Parameters
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8e]"></a>API_Init_Net_Parameters</STRONG> (Thumb, 516 bytes, Stack size 40 bytes, api_w5500.o(i.API_Init_Net_Parameters))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = API_Init_Net_Parameters &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[94]"></a>API_Printf_Hex</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, usart.o(i.API_Printf_Hex))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Printf_Hex &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Send_Data_S0
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Process_Socket_Data
</UL>

<P><STRONG><a name="[95]"></a>API_Process_Socket_Data</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, api_w5500.o(i.API_Process_Socket_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = API_Process_Socket_Data &rArr; API_Read_SOCK_Data_Buffer &rArr; API_Write_W5500_SOCK_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_ReciveDATA_Handle
</UL>

<P><STRONG><a name="[97]"></a>API_RNG_Init</STRONG> (Thumb, 288 bytes, Stack size 32 bytes, api_tnrg.o(i.API_RNG_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = API_RNG_Init &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_flag_get
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_enable
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_hard_rand_data
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[96]"></a>API_Read_SOCK_Data_Buffer</STRONG> (Thumb, 238 bytes, Stack size 32 bytes, api_w5500.o(i.API_Read_SOCK_Data_Buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = API_Read_SOCK_Data_Buffer &rArr; API_Write_W5500_SOCK_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Process_Socket_Data
</UL>

<P><STRONG><a name="[a3]"></a>API_Read_W5500_1Byte</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, api_w5500.o(i.API_Read_W5500_1Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Read_W5500_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
</UL>

<P><STRONG><a name="[89]"></a>API_Read_W5500_SOCK_1Byte</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, api_w5500.o(i.API_Read_W5500_SOCK_1Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Read_W5500_SOCK_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_UDP
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Listen
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Connect
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
</UL>

<P><STRONG><a name="[9e]"></a>API_Read_W5500_SOCK_2Byte</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, api_w5500.o(i.API_Read_W5500_SOCK_2Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Read_W5500_SOCK_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
</UL>

<P><STRONG><a name="[a1]"></a>API_SPI0_Read_Byte</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, api_w5500.o(i.API_SPI0_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = API_SPI0_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
</UL>

<P><STRONG><a name="[a0]"></a>API_SPI0_Send_Byte</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, api_w5500.o(i.API_SPI0_Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_nByte
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_2Byte
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_1Byte
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
</UL>

<P><STRONG><a name="[9f]"></a>API_SPI0_Send_Short</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, api_w5500.o(i.API_SPI0_Send_Short))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_nByte
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_2Byte
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_1Byte
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
</UL>

<P><STRONG><a name="[a7]"></a>API_Socket_Connect</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, api_w5500.o(i.API_Socket_Connect))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = API_Socket_Connect &rArr; API_Write_W5500_SOCK_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Socket_Set
</UL>

<P><STRONG><a name="[91]"></a>API_Socket_Init</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, api_w5500.o(i.API_Socket_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = API_Socket_Init &rArr; API_Write_W5500_SOCK_4Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
</UL>

<P><STRONG><a name="[a8]"></a>API_Socket_Listen</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, api_w5500.o(i.API_Socket_Listen))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = API_Socket_Listen &rArr; API_Write_W5500_SOCK_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Socket_Set
</UL>

<P><STRONG><a name="[a9]"></a>API_Socket_UDP</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, api_w5500.o(i.API_Socket_UDP))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = API_Socket_UDP &rArr; API_Write_W5500_SOCK_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Socket_Set
</UL>

<P><STRONG><a name="[e1]"></a>API_W5500_1MS_RunTask</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, api_w5500.o(i.API_W5500_1MS_RunTask))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[8d]"></a>API_W5500_GPIO_Init</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = API_W5500_GPIO_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[8f]"></a>API_W5500_HardWare_Rest</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_HardWare_Rest))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = API_W5500_HardWare_Rest &rArr; API_Read_W5500_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[ac]"></a>API_W5500_Interrupt_Process</STRONG> (Thumb, 246 bytes, Stack size 16 bytes, api_w5500.o(i.API_W5500_Interrupt_Process))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = API_W5500_Interrupt_Process &rArr; API_Socket_Init &rArr; API_Write_W5500_SOCK_4Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Init
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_ReciveDATA_Handle
</UL>

<P><STRONG><a name="[ad]"></a>API_W5500_ReciveDATA_Handle</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_ReciveDATA_Handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = API_W5500_ReciveDATA_Handle &rArr; API_Process_Socket_Data &rArr; API_Read_SOCK_Data_Buffer &rArr; API_Write_W5500_SOCK_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Process_Socket_Data
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[90]"></a>API_W5500_Register_Init</STRONG> (Thumb, 114 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_Register_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = API_W5500_Register_Init &rArr; API_Write_W5500_nByte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_nByte
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_2Byte
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_1Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[8c]"></a>API_W5500_SPI0_Init</STRONG> (Thumb, 184 bytes, Stack size 32 bytes, api_w5500.o(i.API_W5500_SPI0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = API_W5500_SPI0_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[b4]"></a>API_W5500_Send_Data_S0</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, api_w5500.o(i.API_W5500_Send_Data_S0))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = API_W5500_Send_Data_S0 &rArr; API_Write_SOCK_Data_Buffer &rArr; API_Write_W5500_SOCK_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[92]"></a>API_W5500_Socket_Set</STRONG> (Thumb, 132 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_Socket_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = API_W5500_Socket_Set &rArr; API_Socket_UDP &rArr; API_Write_W5500_SOCK_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_UDP
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Listen
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Connect
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[b5]"></a>API_Write_SOCK_Data_Buffer</STRONG> (Thumb, 278 bytes, Stack size 32 bytes, api_w5500.o(i.API_Write_SOCK_Data_Buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = API_Write_SOCK_Data_Buffer &rArr; API_Write_W5500_SOCK_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Send_Data_S0
</UL>

<P><STRONG><a name="[ae]"></a>API_Write_W5500_1Byte</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, api_w5500.o(i.API_Write_W5500_1Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Write_W5500_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
</UL>

<P><STRONG><a name="[b0]"></a>API_Write_W5500_2Byte</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, api_w5500.o(i.API_Write_W5500_2Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Write_W5500_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
</UL>

<P><STRONG><a name="[87]"></a>API_Write_W5500_SOCK_1Byte</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, api_w5500.o(i.API_Write_W5500_SOCK_1Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Write_W5500_SOCK_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_UDP
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Listen
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Connect
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
</UL>

<P><STRONG><a name="[a2]"></a>API_Write_W5500_SOCK_2Byte</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, api_w5500.o(i.API_Write_W5500_SOCK_2Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Write_W5500_SOCK_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Init
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
</UL>

<P><STRONG><a name="[86]"></a>API_Write_W5500_SOCK_4Byte</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, api_w5500.o(i.API_Write_W5500_SOCK_4Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Write_W5500_SOCK_4Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Init
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
</UL>

<P><STRONG><a name="[af]"></a>API_Write_W5500_nByte</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, api_w5500.o(i.API_Write_W5500_nByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = API_Write_W5500_nByte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
</UL>

<P><STRONG><a name="[13]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DMA1_Channel0_IRQHandler &rArr; dma_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_get
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DMA1_Channel1_IRQHandler &rArr; dma_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_get
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DMA1_Channel2_IRQHandler &rArr; dma_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_get
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[b7]"></a>DRV_SPI_SwapByte</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, spi.o(i.DRV_SPI_SwapByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
</UL>

<P><STRONG><a name="[16]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[93]"></a>EEPROM_SPI_ReadBuffer</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, eeprom_spi.o(i.EEPROM_SPI_ReadBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_Net_Parameters
</UL>

<P><STRONG><a name="[b8]"></a>EEPROM_SPI_SendInstruction</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, eeprom_spi.o(i.EEPROM_SPI_SendInstruction))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
</UL>

<P><STRONG><a name="[b9]"></a>EEPROM_SPI_WaitStandbyState</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[9d]"></a>EEPROM_SPI_WriteBuffer</STRONG> (Thumb, 394 bytes, Stack size 40 bytes, eeprom_spi.o(i.EEPROM_SPI_WriteBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
</UL>

<P><STRONG><a name="[ba]"></a>EEPROM_SPI_WritePage</STRONG> (Thumb, 170 bytes, Stack size 32 bytes, eeprom_spi.o(i.EEPROM_SPI_WritePage))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
</UL>

<P><STRONG><a name="[bc]"></a>EEPROM_WriteDisable</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, eeprom_spi.o(i.EEPROM_WriteDisable))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = EEPROM_WriteDisable &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[bb]"></a>EEPROM_WriteEnable</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, eeprom_spi.o(i.EEPROM_WriteEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = EEPROM_WriteEnable &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[41]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI10_15_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI10_15_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[bf]"></a>FML_USART_RecvTask</STRONG> (Thumb, 174 bytes, Stack size 2072 bytes, usart.o(i.FML_USART_RecvTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadBytesToBuffer
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[c3]"></a>GPIO_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = GPIO_Init &rArr; Init_GPIO_TS5A339 &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[11]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c4]"></a>Init_GPIO_TS5A339</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, gpio.o(i.Init_GPIO_TS5A339))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Init_GPIO_TS5A339 &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[12]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c5]"></a>RTC_Init</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, rtc.o(i.RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = RTC_Init &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_rtc_clock_config
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ce]"></a>SPI1_Init</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, spi.o(i.SPI1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SPI1_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>SystemInit</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[d1]"></a>TIMER1_Init</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, time.o(i.TIMER1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TIMER1_Init &rArr; timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_shadow_config
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_pulse_value_config
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_mode_config
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[36]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIMER2_IRQHandler &rArr; usb_timer_irq
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIMER3_IRQHandler &rArr; TIM_PeriodElapsedCallback &rArr; gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[db]"></a>TIMER3_Init</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, time.o(i.TIMER3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TIMER3_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[50]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIMER6_IRQHandler &rArr; TIM_PeriodElapsedCallback &rArr; gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[df]"></a>TIMER6_Init</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, time.o(i.TIMER6_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TIMER6_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[da]"></a>TIM_PeriodElapsedCallback</STRONG> (Thumb, 446 bytes, Stack size 8 bytes, time.o(i.TIM_PeriodElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_PeriodElapsedCallback &rArr; gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_key_state_get
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_1MS_RunTask
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_IRQHandler
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_IRQHandler
</UL>

<P><STRONG><a name="[e3]"></a>UART_IDLECallBack</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usart.o(i.UART_IDLECallBack))
<BR><BR>[Stack]<UL><LI>Max Depth = 2104<LI>Call Chain = UART_IDLECallBack &rArr; FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_clear
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[e6]"></a>UART_RxCpltCallback</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usart.o(i.UART_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_RxCpltCallback &rArr; RecvDataHandler &rArr; AddByteToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RecvDataHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[e7]"></a>USART0_Init</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, usart.o(i.USART0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = USART0_Init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[40]"></a>USART2_IRQHandler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2112<LI>Call Chain = USART2_IRQHandler &rArr; UART_IDLECallBack &rArr; FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>USBFS_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USBFS_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = USBFS_IRQHandler &rArr; usbd_isr &rArr; usbd_int_epout &rArr; usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USBFS_WKUP_IRQHandler &rArr; resume_mcu_clk
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_pll48m_clock_config
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_ck48m_clock_config
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_clock_active
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f3]"></a>__0printf$8</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printf8.o(i.__0printf$8), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[141]"></a>__1printf$8</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf8.o(i.__0printf$8), UNUSED)

<P><STRONG><a name="[8a]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf8.o(i.__0printf$8))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Send_Data_S0
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Socket_Set
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Process_Socket_Data
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_Net_Parameters
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
</UL>

<P><STRONG><a name="[142]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[143]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[144]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[88]"></a>delay_1ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_1ms))
<BR><BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_UDP
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Listen
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Connect
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
</UL>

<P><STRONG><a name="[cf]"></a>delay_decrement</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[80]"></a>dma_interrupt_flag_clear</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_interrupt_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>

<P><STRONG><a name="[b6]"></a>dma_interrupt_flag_get</STRONG> (Thumb, 516 bytes, Stack size 20 bytes, gd32f4xx_dma.o(i.dma_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = dma_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel2_IRQHandler
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel0_IRQHandler
</UL>

<P><STRONG><a name="[be]"></a>exti_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
</UL>

<P><STRONG><a name="[bd]"></a>exti_interrupt_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
</UL>

<P><STRONG><a name="[75]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, usart.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf8.o(i.__0printf$8)
</UL>
<P><STRONG><a name="[e2]"></a>gd_eval_key_state_get</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, gd32f470v_start.o(i.gd_eval_key_state_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[9c]"></a>get_hard_rand_data</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, api_tnrg.o(i.get_hard_rand_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_hard_rand_data
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_get_true_random_data
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
</UL>

<P><STRONG><a name="[b1]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[83]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_nByte
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_2Byte
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_1Byte
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
</UL>

<P><STRONG><a name="[84]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_nByte
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_2Byte
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_1Byte
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
</UL>

<P><STRONG><a name="[fc]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_key_state_get
</UL>

<P><STRONG><a name="[aa]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
</UL>

<P><STRONG><a name="[ab]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
</UL>

<P><STRONG><a name="[72]"></a>main</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = main &rArr; API_RNG_Init &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_vector_table_set
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Send_Data_S0
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_ReciveDATA_Handle
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[dc]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
</UL>

<P><STRONG><a name="[105]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>

<P><STRONG><a name="[103]"></a>nvic_vector_table_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_vector_table_set))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c6]"></a>pmu_backup_write_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_pmu.o(i.pmu_backup_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[12c]"></a>pmu_to_deepsleepmode</STRONG> (Thumb, 204 bytes, Stack size 8 bytes, gd32f4xx_pmu.o(i.pmu_to_deepsleepmode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = pmu_to_deepsleepmode
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_suspend
</UL>

<P><STRONG><a name="[f1]"></a>rcu_ck48m_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_ck48m_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[10f]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[106]"></a>rcu_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[c7]"></a>rcu_osci_on</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_osci_on))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[c8]"></a>rcu_osci_stab_wait</STRONG> (Thumb, 342 bytes, Stack size 20 bytes, gd32f4xx_rcu.o(i.rcu_osci_stab_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[98]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
</UL>

<P><STRONG><a name="[10e]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>

<P><STRONG><a name="[10d]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>

<P><STRONG><a name="[f0]"></a>rcu_pll48m_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_pll48m_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[c9]"></a>rcu_rtc_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_rtc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[107]"></a>rcu_system_clock_source_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_system_clock_source_config))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[108]"></a>rcu_system_clock_source_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_system_clock_source_get))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[cb]"></a>rtc_init</STRONG> (Thumb, 190 bytes, Stack size 20 bytes, gd32f4xx_rtc.o(i.rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_exit
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[109]"></a>rtc_init_mode_enter</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[10a]"></a>rtc_init_mode_exit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_exit))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[ca]"></a>rtc_register_sync_wait</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_register_sync_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_register_sync_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[b3]"></a>spi_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[a6]"></a>spi_i2s_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>

<P><STRONG><a name="[a5]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>

<P><STRONG><a name="[a4]"></a>spi_i2s_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>

<P><STRONG><a name="[b2]"></a>spi_init</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[104]"></a>systick_config</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, systick.o(i.systick_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = systick_config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[102]"></a>timer_auto_reload_shadow_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[d4]"></a>timer_channel_output_config</STRONG> (Thumb, 484 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_channel_output_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[d6]"></a>timer_channel_output_mode_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[d5]"></a>timer_channel_output_pulse_value_config</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[d7]"></a>timer_channel_output_shadow_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_shadow_config))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[d2]"></a>timer_deinit</STRONG> (Thumb, 374 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[100]"></a>timer_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_disable))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_delay
</UL>

<P><STRONG><a name="[de]"></a>timer_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[d3]"></a>timer_init</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_init))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[101]"></a>timer_interrupt_disable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_disable))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[dd]"></a>timer_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[e0]"></a>timer_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[d9]"></a>timer_interrupt_flag_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_IRQHandler
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_IRQHandler
</UL>

<P><STRONG><a name="[99]"></a>trng_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_trng.o(i.trng_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = trng_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
</UL>

<P><STRONG><a name="[9a]"></a>trng_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_enable))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
</UL>

<P><STRONG><a name="[9b]"></a>trng_flag_get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_hard_rand_data
</UL>

<P><STRONG><a name="[fd]"></a>trng_get_true_random_data</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_get_true_random_data))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_hard_rand_data
</UL>

<P><STRONG><a name="[e9]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[e5]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[fa]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[e8]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[ec]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[fb]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[e4]"></a>usart_interrupt_flag_clear</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[ed]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[ea]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[eb]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[f2]"></a>usb_clock_active</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_usb_dev.o(i.usb_clock_active))
<BR><BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[119]"></a>usb_ctlep_startout</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, drv_usb_dev.o(i.usb_ctlep_startout))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_recev
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enum_error
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[122]"></a>usb_iepintr_read</STRONG> (Thumb, 42 bytes, Stack size 12 bytes, drv_usb_dev.o(i.usb_iepintr_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = usb_iepintr_read
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[12a]"></a>usb_rxfifo_read</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, drv_usb_core.o(i.usb_rxfifo_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usb_rxfifo_read
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_rxfifo
</UL>

<P><STRONG><a name="[d8]"></a>usb_timer_irq</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, gd32f4xx_hw.o(i.usb_timer_irq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_timer_irq
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER2_IRQHandler
</UL>

<P><STRONG><a name="[128]"></a>usb_transc_active</STRONG> (Thumb, 142 bytes, Stack size 20 bytes, drv_usb_dev.o(i.usb_transc_active))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usb_transc_active
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
</UL>

<P><STRONG><a name="[11f]"></a>usb_transc_clrstall</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, drv_usb_dev.o(i.usb_transc_clrstall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_transc_clrstall
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall_clear
</UL>

<P><STRONG><a name="[110]"></a>usb_transc_inxfer</STRONG> (Thumb, 268 bytes, Stack size 32 bytes, drv_usb_dev.o(i.usb_transc_inxfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_write
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
</UL>

<P><STRONG><a name="[11d]"></a>usb_transc_outxfer</STRONG> (Thumb, 144 bytes, Stack size 20 bytes, drv_usb_dev.o(i.usb_transc_outxfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usb_transc_outxfer
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_recev
</UL>

<P><STRONG><a name="[11e]"></a>usb_transc_stall</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, drv_usb_dev.o(i.usb_transc_stall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_transc_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall
</UL>

<P><STRONG><a name="[112]"></a>usb_txfifo_flush</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, drv_usb_core.o(i.usb_txfifo_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = usb_txfifo_flush &rArr; usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_udelay
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
</UL>

<P><STRONG><a name="[111]"></a>usb_txfifo_write</STRONG> (Thumb, 34 bytes, Stack size 20 bytes, drv_usb_core.o(i.usb_txfifo_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usb_txfifo_write
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_emptytxfifo_write
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_inxfer
</UL>

<P><STRONG><a name="[113]"></a>usb_udelay</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, gd32f4xx_hw.o(i.usb_udelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_flush
</UL>

<P><STRONG><a name="[12f]"></a>usbd_class_request</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, usbd_enum.o(i.usbd_class_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_class_request
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>

<P><STRONG><a name="[114]"></a>usbd_ctl_recev</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_ctl_recev))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = usbd_ctl_recev &rArr; usbd_ep_recev &rArr; usb_transc_outxfer
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_recev
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_out_transc
</UL>

<P><STRONG><a name="[116]"></a>usbd_ctl_send</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_ctl_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
</UL>

<P><STRONG><a name="[118]"></a>usbd_ctl_status_recev</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_transc.o(i.usbd_ctl_status_recev))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = usbd_ctl_status_recev &rArr; usbd_ep_recev &rArr; usb_transc_outxfer
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_recev
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
</UL>

<P><STRONG><a name="[11a]"></a>usbd_ctl_status_send</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_transc.o(i.usbd_ctl_status_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = usbd_ctl_status_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_out_transc
</UL>

<P><STRONG><a name="[11c]"></a>usbd_enum_error</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, usbd_enum.o(i.usbd_enum_error))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = usbd_enum_error &rArr; usbd_ep_stall &rArr; usb_transc_stall
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>

<P><STRONG><a name="[115]"></a>usbd_ep_recev</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, usbd_core.o(i.usbd_ep_recev))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = usbd_ep_recev &rArr; usb_transc_outxfer
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_outxfer
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_recev
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_recev
</UL>

<P><STRONG><a name="[117]"></a>usbd_ep_send</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, usbd_core.o(i.usbd_ep_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_inxfer
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_send
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
</UL>

<P><STRONG><a name="[f9]"></a>usbd_ep_stall</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, usbd_core.o(i.usbd_ep_stall))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_ep_stall &rArr; usb_transc_stall
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enum_error
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_std_setfeature
</UL>

<P><STRONG><a name="[f7]"></a>usbd_ep_stall_clear</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, usbd_core.o(i.usbd_ep_stall_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_ep_stall_clear &rArr; usb_transc_clrstall
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_clrstall
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_std_clearfeature
</UL>

<P><STRONG><a name="[120]"></a>usbd_in_transc</STRONG> (Thumb, 160 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_in_transc))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = usbd_in_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_recev
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_send
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[ee]"></a>usbd_isr</STRONG> (Thumb, 222 bytes, Stack size 16 bytes, drv_usbd_int.o(i.usbd_isr))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = usbd_isr &rArr; usbd_int_epout &rArr; usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_suspend
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_rxfifo
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_enumfinish
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_IRQHandler
</UL>

<P><STRONG><a name="[124]"></a>usbd_out_transc</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_out_transc))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = usbd_out_transc &rArr; usbd_ctl_status_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_recev
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
</UL>

<P><STRONG><a name="[125]"></a>usbd_setup_transc</STRONG> (Thumb, 132 bytes, Stack size 24 bytes, usbd_transc.o(i.usbd_setup_transc))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_send
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_recev
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_vendor_request
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_standard_request
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enum_error
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_class_request
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
</UL>

<P><STRONG><a name="[12e]"></a>usbd_standard_request</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, usbd_enum.o(i.usbd_standard_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_standard_request
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>

<P><STRONG><a name="[130]"></a>usbd_vendor_request</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i.usbd_vendor_request))
<BR><BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[10b]"></a>system_clock_168m_25m_hxtal</STRONG> (Thumb, 240 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_168m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[d0]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_168m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[ef]"></a>resume_mcu_clk</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.resume_mcu_clk))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = resume_mcu_clk
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_system_clock_source_get
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_system_clock_source_config
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[10c]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, systick.o(i.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[cd]"></a>AddByteToBuffer</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, usart.o(i.AddByteToBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AddByteToBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RecvDataHandler
</UL>

<P><STRONG><a name="[c1]"></a>ReadBytesToBuffer</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, usart.o(i.ReadBytesToBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
</UL>

<P><STRONG><a name="[cc]"></a>RecvDataHandler</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, usart.o(i.RecvDataHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RecvDataHandler &rArr; AddByteToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AddByteToBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
</UL>

<P><STRONG><a name="[11b]"></a>usbd_emptytxfifo_write</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_emptytxfifo_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = usbd_emptytxfifo_write &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_write
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[12d]"></a>usbd_int_enumfinish</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, drv_usbd_int.o(i.usbd_int_enumfinish))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[121]"></a>usbd_int_epin</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_int_epin))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = usbd_int_epin &rArr; usbd_in_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_emptytxfifo_write
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_iepintr_read
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[123]"></a>usbd_int_epout</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_int_epout))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = usbd_int_epout &rArr; usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_out_transc
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[126]"></a>usbd_int_reset</STRONG> (Thumb, 208 bytes, Stack size 72 bytes, drv_usbd_int.o(i.usbd_int_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = usbd_int_reset &rArr; usb_txfifo_flush &rArr; usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_flush
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_active
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[129]"></a>usbd_int_rxfifo</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, drv_usbd_int.o(i.usbd_int_rxfifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usbd_int_rxfifo &rArr; usb_rxfifo_read
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_rxfifo_read
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[12b]"></a>usbd_int_suspend</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_int_suspend))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usbd_int_suspend &rArr; pmu_to_deepsleepmode
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_to_deepsleepmode
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[f8]"></a>_usb_bos_desc_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_bos_desc_get))
<BR><BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_std_getdescriptor
</UL>

<P><STRONG><a name="[d]"></a>_usb_config_desc_get</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_config_desc_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_config_desc_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[c]"></a>_usb_dev_desc_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_dev_desc_get))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[1]"></a>_usb_std_clearfeature</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_clearfeature))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _usb_std_clearfeature &rArr; usbd_ep_stall_clear &rArr; usb_transc_clrstall
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[7]"></a>_usb_std_getconfiguration</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_std_getconfiguration))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_std_getconfiguration
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[5]"></a>_usb_std_getdescriptor</STRONG> (Thumb, 272 bytes, Stack size 32 bytes, usbd_enum.o(i._usb_std_getdescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _usb_std_getdescriptor
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_bos_desc_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[9]"></a>_usb_std_getinterface</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_getinterface))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[0]"></a>_usb_std_getstatus</STRONG> (Thumb, 192 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_getstatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _usb_std_getstatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[2]"></a>_usb_std_reserved</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_reserved))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[4]"></a>_usb_std_setaddress</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_std_setaddress))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_std_setaddress
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[8]"></a>_usb_std_setconfiguration</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_setconfiguration))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _usb_std_setconfiguration
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[6]"></a>_usb_std_setdescriptor</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_setdescriptor))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[3]"></a>_usb_std_setfeature</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_setfeature))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _usb_std_setfeature &rArr; usbd_ep_stall &rArr; usb_transc_stall
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[a]"></a>_usb_std_setinterface</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_setinterface))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _usb_std_setinterface
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[b]"></a>_usb_std_synchframe</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_synchframe))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[e]"></a>_usb_str_desc_get</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_str_desc_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_str_desc_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[fe]"></a>hw_delay</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, gd32f4xx_hw.o(i.hw_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_disable
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_udelay
</UL>

<P><STRONG><a name="[ff]"></a>hw_time_set</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, gd32f4xx_hw.o(i.hw_time_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_disable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_disable
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_auto_reload_shadow_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_delay
</UL>

<P><STRONG><a name="[f4]"></a>_printf_core</STRONG> (Thumb, 984 bytes, Stack size 104 bytes, printf8.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf$8
</UL>

<P><STRONG><a name="[f6]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printf8.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[f5]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printf8.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
