<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\boot.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\boot.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Thu Aug 07 18:30:23 2025
<BR><P>
<H3>Maximum Stack Usage =       2112 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
USART2_IRQHandler &rArr; UART_IDLECallBack &rArr; FML_USART_RecvTask &rArr; ReadBytesToBuffer
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[33]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[33]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[33]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">CAN0_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">CAN1_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6f]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">DMA1_Channel0_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">DMA1_Channel1_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">DMA1_Channel2_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[66]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[67]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1e]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">EXTI10_15_IRQHandler</a> from gd32f4xx_it.o(i.EXTI10_15_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">EXTI5_9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[71]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6a]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[69]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[79]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">PendSV_Handler</a> from gd32f4xx_it.o(i.PendSV_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">SDIO_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[74]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[75]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[76]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1d]">SVC_Handler</a> from gd32f4xx_it.o(i.SVC_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7a]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[23]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">TIMER1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">TIMER2_IRQHandler</a> from gd32f4xx_it.o(i.TIMER2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">TIMER3_IRQHandler</a> from gd32f4xx_it.o(i.TIMER3_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">TIMER6_IRQHandler</a> from gd32f4xx_it.o(i.TIMER6_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[78]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[77]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[70]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[72]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[73]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">USART0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">USART2_IRQHandler</a> from gd32f4xx_it.o(i.USART2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[68]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">USBFS_IRQHandler</a> from gd32f4xx_it.o(i.USBFS_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">USBFS_WKUP_IRQHandler</a> from gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6c]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6b]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6e]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6d]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1c]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7e]">__main</a> from __main.o(!!!main) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[7c]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[15]">_usb_config_desc_get</a> from usbd_enum.o(i._usb_config_desc_get) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[14]">_usb_dev_desc_get</a> from usbd_enum.o(i._usb_dev_desc_get) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[9]">_usb_std_clearfeature</a> from usbd_enum.o(i._usb_std_clearfeature) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[f]">_usb_std_getconfiguration</a> from usbd_enum.o(i._usb_std_getconfiguration) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[d]">_usb_std_getdescriptor</a> from usbd_enum.o(i._usb_std_getdescriptor) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[11]">_usb_std_getinterface</a> from usbd_enum.o(i._usb_std_getinterface) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[8]">_usb_std_getstatus</a> from usbd_enum.o(i._usb_std_getstatus) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[a]">_usb_std_reserved</a> from usbd_enum.o(i._usb_std_reserved) referenced 4 times from usbd_enum.o(.data)
 <LI><a href="#[c]">_usb_std_setaddress</a> from usbd_enum.o(i._usb_std_setaddress) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[10]">_usb_std_setconfiguration</a> from usbd_enum.o(i._usb_std_setconfiguration) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[e]">_usb_std_setdescriptor</a> from usbd_enum.o(i._usb_std_setdescriptor) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[b]">_usb_std_setfeature</a> from usbd_enum.o(i._usb_std_setfeature) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[12]">_usb_std_setinterface</a> from usbd_enum.o(i._usb_std_setinterface) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[13]">_usb_std_synchframe</a> from usbd_enum.o(i._usb_std_synchframe) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[16]">_usb_str_desc_get</a> from usbd_enum.o(i._usb_str_desc_get) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[7d]">fputc</a> from usart.o(i.fputc) referenced from _printf_char_file.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[7e]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[7f]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[81]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[17b]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[17c]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[17d]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[17e]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[82]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[17f]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[83]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[a9]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[85]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[86]"></a>_printf_x</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_x &rArr; _printf_int_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[88]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
</UL>

<P><STRONG><a name="[180]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[95]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[8a]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[8c]"></a>__rt_lib_init_heap_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_heap_2 &rArr; _init_alloc &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[181]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[182]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[183]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[184]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[185]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[186]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[187]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[188]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[189]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[18a]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[18b]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[18c]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[18d]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[8e]"></a>__rt_lib_init_stdio_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000024))
<BR><BR>[Stack]<UL><LI>Max Depth = 136 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_stdio_2 &rArr; _initio &rArr; freopen &rArr; _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_initio
</UL>

<P><STRONG><a name="[18e]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[18f]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[190]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[191]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[192]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[193]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[194]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[9a]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[195]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[196]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[90]"></a>__rt_lib_shutdown_stdio_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = __rt_lib_shutdown_stdio_2 &rArr; _terminateio &rArr; _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_terminateio
</UL>

<P><STRONG><a name="[197]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[198]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[199]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[19a]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[19b]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[19c]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[80]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[19d]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[92]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[94]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[19e]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[96]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 200 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; API_Init_LAN &rArr; API_Init_Net_Parameters &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[19f]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[c3]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[99]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[1a0]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[9b]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[17]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a1]"></a>_maybe_terminate_alloc</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, maybetermalloc1.o(.emb_text), UNUSED)

<P><STRONG><a name="[33]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[be]"></a>__user_initial_stackheap</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[9d]"></a>malloc</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, h1_alloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Full
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fopen
</UL>

<P><STRONG><a name="[a0]"></a>free</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, h1_free.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = free
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_terminateio
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
</UL>

<P><STRONG><a name="[a1]"></a>__2printf</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, noretval__2printf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Send_Data_S0
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_ReciveDATA_Handle
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Socket_Set
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Process_Socket_Data
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_Net_Parameters
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
</UL>

<P><STRONG><a name="[a4]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>

<P><STRONG><a name="[a5]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>

<P><STRONG><a name="[a3]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[84]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[87]"></a>_printf_int_hex</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, _printf_hex_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_int_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_x
</UL>

<P><STRONG><a name="[1a2]"></a>_printf_longlong_hex</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, _printf_hex_int.o(.text), UNUSED)

<P><STRONG><a name="[a7]"></a>__printf</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, __printf_flags_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[bc]"></a>strlen</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, strlen.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_open
</UL>

<P><STRONG><a name="[111]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadBytesToBuffer
</UL>

<P><STRONG><a name="[aa]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[1a3]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[ab]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[1a4]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1a5]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1a6]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[b4]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fopen
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_initio
</UL>

<P><STRONG><a name="[1a7]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1a8]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1a9]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1aa]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1]"></a>__rt_heap_escrow</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[0]"></a>__rt_heap_expand</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[9e]"></a>__rt_heap_descriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_heap_descriptor_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[cf]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ftell_internal
</UL>

<P><STRONG><a name="[1ab]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[1ac]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[1ad]"></a>__use_no_heap</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hguard.o(.text), UNUSED)

<P><STRONG><a name="[1ae]"></a>__heap$guard</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hguard.o(.text), UNUSED)

<P><STRONG><a name="[6]"></a>_terminate_user_alloc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)

<P><STRONG><a name="[3]"></a>_init_user_alloc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)

<P><STRONG><a name="[9f]"></a>__Heap_Full</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, init_alloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>

<P><STRONG><a name="[ad]"></a>__Heap_Broken</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[8d]"></a>_init_alloc</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, init_alloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _init_alloc &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Initialize
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_heap_2
</UL>

<P><STRONG><a name="[af]"></a>__Heap_Initialize</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, h1_init.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[2]"></a>__Heap_DescSize</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, h1_init.o(.text), UNUSED)

<P><STRONG><a name="[a6]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[b0]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[b1]"></a>_printf_cs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[b2]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[89]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[a2]"></a>_printf_char_file</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, _printf_char_file.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ferror
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[1af]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[bd]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1b0]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[ac]"></a>__Heap_ProvideMemory</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, h1_extend.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Full
</UL>

<P><STRONG><a name="[b3]"></a>ferror</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ferror.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[8f]"></a>_initio</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, initio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = _initio &rArr; freopen &rArr; _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTRED
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freopen
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setvbuf
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_stdio_2
</UL>

<P><STRONG><a name="[91]"></a>_terminateio</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, initio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = _terminateio &rArr; _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown_stdio_2
</UL>

<P><STRONG><a name="[ae]"></a>__rt_SIGRTMEM</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_rtmem_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Broken
</UL>

<P><STRONG><a name="[bb]"></a>_sys_open</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _sys_open &rArr; strlen
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freopen
</UL>

<P><STRONG><a name="[c2]"></a>_sys_close</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _sys_close
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
</UL>

<P><STRONG><a name="[cc]"></a>_sys_write</STRONG> (Thumb, 16 bytes, Stack size 24 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _sys_write
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_writebuf
</UL>

<P><STRONG><a name="[1b1]"></a>_sys_read</STRONG> (Thumb, 14 bytes, Stack size 24 bytes, sys_io.o(.text), UNUSED)

<P><STRONG><a name="[c6]"></a>_sys_istty</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _sys_istty
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fseek
</UL>

<P><STRONG><a name="[cb]"></a>_sys_seek</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _sys_seek
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_writebuf
</UL>

<P><STRONG><a name="[1b2]"></a>_sys_ensure</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, sys_io.o(.text), UNUSED)

<P><STRONG><a name="[c8]"></a>_sys_flen</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _sys_flen
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fseek
</UL>

<P><STRONG><a name="[1b3]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1b4]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1b5]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[93]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[b7]"></a>setvbuf</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, setvbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = setvbuf
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_initio
</UL>

<P><STRONG><a name="[b5]"></a>freopen</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, fopen.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = freopen &rArr; _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fseek
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_open
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fopen
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_initio
</UL>

<P><STRONG><a name="[c0]"></a>fopen</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, fopen.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freopen
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>

<P><STRONG><a name="[b8]"></a>_fclose_internal</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, fclose.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fflush
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_close
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freopen
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_terminateio
</UL>

<P><STRONG><a name="[1b6]"></a>fclose</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, fclose.o(.text), UNUSED)

<P><STRONG><a name="[98]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[ba]"></a>__sig_exit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, defsig_exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTRED
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[b6]"></a>__rt_SIGRTRED</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_rtred_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __rt_SIGRTRED &rArr; __rt_SIGRTRED_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTRED_inner
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_initio
</UL>

<P><STRONG><a name="[b9]"></a>__rt_SIGRTMEM_inner</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, defsig_rtmem_inner.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[9c]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>

<P><STRONG><a name="[bf]"></a>_fseek</STRONG> (Thumb, 242 bytes, Stack size 24 bytes, fseek.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _fseek &rArr; _ftell_internal
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ftell_internal
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_seterr
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_flen
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_istty
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freopen
</UL>

<P><STRONG><a name="[1b7]"></a>fseek</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, fseek.o(.text), UNUSED)

<P><STRONG><a name="[c9]"></a>_seterr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stdio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_writebuf
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fseek
</UL>

<P><STRONG><a name="[ca]"></a>_writebuf</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, stdio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_seterr
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_seek
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_write
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fflush
</UL>

<P><STRONG><a name="[c1]"></a>_fflush</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stdio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_writebuf
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_deferredlazyseek
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
</UL>

<P><STRONG><a name="[cd]"></a>_deferredlazyseek</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fflush
</UL>

<P><STRONG><a name="[c5]"></a>__default_signal_display</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, defsig_general.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTRED_inner
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
</UL>

<P><STRONG><a name="[c4]"></a>__rt_SIGRTRED_inner</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_rtred_inner.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __rt_SIGRTRED_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTRED
</UL>

<P><STRONG><a name="[ce]"></a>_ttywrch</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, sys_wrch.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>

<P><STRONG><a name="[c7]"></a>_ftell_internal</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, ftell.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _ftell_internal
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fseek
</UL>

<P><STRONG><a name="[1b8]"></a>ftell</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, ftell.o(.text), UNUSED)

<P><STRONG><a name="[d0]"></a>ADC_ConvCpltCallback</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, adc.o(i.ADC_ConvCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_ConvCpltCallback &rArr; dma_interrupt_flag_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel2_IRQHandler
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel0_IRQHandler
</UL>

<P><STRONG><a name="[d2]"></a>ADC_ConvHalfCpltCallback</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, adc.o(i.ADC_ConvHalfCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_ConvHalfCpltCallback &rArr; dma_interrupt_flag_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel2_IRQHandler
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel0_IRQHandler
</UL>

<P><STRONG><a name="[d3]"></a>API_Chose_TS5A3359_GAIN</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, gpio.o(i.API_Chose_TS5A3359_GAIN))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = API_Chose_TS5A3359_GAIN
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[d6]"></a>API_Detect_Gateway</STRONG> (Thumb, 212 bytes, Stack size 16 bytes, api_w5500.o(i.API_Detect_Gateway))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = API_Detect_Gateway &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[db]"></a>API_Init_LAN</STRONG> (Thumb, 234 bytes, Stack size 16 bytes, api_w5500.o(i.API_Init_LAN))
<BR><BR>[Stack]<UL><LI>Max Depth = 200 + Unknown Stack Size
<LI>Call Chain = API_Init_LAN &rArr; API_Init_Net_Parameters &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Socket_Set
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_Net_Parameters
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[de]"></a>API_Init_Net_Parameters</STRONG> (Thumb, 516 bytes, Stack size 40 bytes, api_w5500.o(i.API_Init_Net_Parameters))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = API_Init_Net_Parameters &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[e4]"></a>API_Printf_Hex</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, usart.o(i.API_Printf_Hex))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = API_Printf_Hex &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Send_Data_S0
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Process_Socket_Data
</UL>

<P><STRONG><a name="[e5]"></a>API_Process_Socket_Data</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, api_w5500.o(i.API_Process_Socket_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = API_Process_Socket_Data &rArr; API_Printf_Hex &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_ReciveDATA_Handle
</UL>

<P><STRONG><a name="[e7]"></a>API_RNG_Init</STRONG> (Thumb, 288 bytes, Stack size 32 bytes, api_tnrg.o(i.API_RNG_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 192 + Unknown Stack Size
<LI>Call Chain = API_RNG_Init &rArr; API_Printf_Hex &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_flag_get
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_enable
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_hard_rand_data
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e6]"></a>API_Read_SOCK_Data_Buffer</STRONG> (Thumb, 238 bytes, Stack size 32 bytes, api_w5500.o(i.API_Read_SOCK_Data_Buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = API_Read_SOCK_Data_Buffer &rArr; API_Write_W5500_SOCK_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Process_Socket_Data
</UL>

<P><STRONG><a name="[f3]"></a>API_Read_W5500_1Byte</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, api_w5500.o(i.API_Read_W5500_1Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Read_W5500_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
</UL>

<P><STRONG><a name="[da]"></a>API_Read_W5500_SOCK_1Byte</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, api_w5500.o(i.API_Read_W5500_SOCK_1Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Read_W5500_SOCK_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Send_Data_S0
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_ReciveDATA_Handle
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_UDP
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Listen
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Connect
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
</UL>

<P><STRONG><a name="[ee]"></a>API_Read_W5500_SOCK_2Byte</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, api_w5500.o(i.API_Read_W5500_SOCK_2Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Read_W5500_SOCK_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
</UL>

<P><STRONG><a name="[f1]"></a>API_SPI0_Read_Byte</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, api_w5500.o(i.API_SPI0_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = API_SPI0_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
</UL>

<P><STRONG><a name="[f0]"></a>API_SPI0_Send_Byte</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, api_w5500.o(i.API_SPI0_Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_nByte
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_2Byte
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_1Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
</UL>

<P><STRONG><a name="[ef]"></a>API_SPI0_Send_Short</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, api_w5500.o(i.API_SPI0_Send_Short))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_nByte
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_2Byte
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_1Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
</UL>

<P><STRONG><a name="[f7]"></a>API_Socket_Connect</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, api_w5500.o(i.API_Socket_Connect))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = API_Socket_Connect &rArr; API_Write_W5500_SOCK_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Socket_Set
</UL>

<P><STRONG><a name="[e1]"></a>API_Socket_Init</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, api_w5500.o(i.API_Socket_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = API_Socket_Init &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
</UL>

<P><STRONG><a name="[f8]"></a>API_Socket_Listen</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, api_w5500.o(i.API_Socket_Listen))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = API_Socket_Listen &rArr; API_Write_W5500_SOCK_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Socket_Set
</UL>

<P><STRONG><a name="[f9]"></a>API_Socket_UDP</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, api_w5500.o(i.API_Socket_UDP))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = API_Socket_UDP &rArr; API_Write_W5500_SOCK_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Socket_Set
</UL>

<P><STRONG><a name="[130]"></a>API_W5500_1MS_RunTask</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, api_w5500.o(i.API_W5500_1MS_RunTask))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[dd]"></a>API_W5500_GPIO_Init</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = API_W5500_GPIO_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[df]"></a>API_W5500_HardWare_Rest</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_HardWare_Rest))
<BR><BR>[Stack]<UL><LI>Max Depth = 152 + Unknown Stack Size
<LI>Call Chain = API_W5500_HardWare_Rest &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[fc]"></a>API_W5500_Interrupt_Process</STRONG> (Thumb, 246 bytes, Stack size 16 bytes, api_w5500.o(i.API_W5500_Interrupt_Process))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = API_W5500_Interrupt_Process &rArr; API_Socket_Init &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_ReciveDATA_Handle
</UL>

<P><STRONG><a name="[fd]"></a>API_W5500_ReciveDATA_Handle</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_ReciveDATA_Handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 192 + Unknown Stack Size
<LI>Call Chain = API_W5500_ReciveDATA_Handle &rArr; API_W5500_Interrupt_Process &rArr; API_Socket_Init &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Process_Socket_Data
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e0]"></a>API_W5500_Register_Init</STRONG> (Thumb, 114 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_Register_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = API_W5500_Register_Init &rArr; API_Write_W5500_nByte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_nByte
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_2Byte
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_1Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[dc]"></a>API_W5500_SPI0_Init</STRONG> (Thumb, 184 bytes, Stack size 32 bytes, api_w5500.o(i.API_W5500_SPI0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = API_W5500_SPI0_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[104]"></a>API_W5500_Send_Data_S0</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, api_w5500.o(i.API_W5500_Send_Data_S0))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = API_W5500_Send_Data_S0 &rArr; API_Printf_Hex &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e2]"></a>API_W5500_Socket_Set</STRONG> (Thumb, 192 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_Socket_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 152 + Unknown Stack Size
<LI>Call Chain = API_W5500_Socket_Set &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_UDP
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Listen
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Connect
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[105]"></a>API_Write_SOCK_Data_Buffer</STRONG> (Thumb, 278 bytes, Stack size 32 bytes, api_w5500.o(i.API_Write_SOCK_Data_Buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = API_Write_SOCK_Data_Buffer &rArr; API_Write_W5500_SOCK_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Send_Data_S0
</UL>

<P><STRONG><a name="[fe]"></a>API_Write_W5500_1Byte</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, api_w5500.o(i.API_Write_W5500_1Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Write_W5500_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
</UL>

<P><STRONG><a name="[100]"></a>API_Write_W5500_2Byte</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, api_w5500.o(i.API_Write_W5500_2Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Write_W5500_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
</UL>

<P><STRONG><a name="[d8]"></a>API_Write_W5500_SOCK_1Byte</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, api_w5500.o(i.API_Write_W5500_SOCK_1Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Write_W5500_SOCK_1Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Interrupt_Process
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_UDP
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Listen
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Connect
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
</UL>

<P><STRONG><a name="[f2]"></a>API_Write_W5500_SOCK_2Byte</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, api_w5500.o(i.API_Write_W5500_SOCK_2Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Write_W5500_SOCK_2Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
</UL>

<P><STRONG><a name="[d7]"></a>API_Write_W5500_SOCK_4Byte</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, api_w5500.o(i.API_Write_W5500_SOCK_4Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Write_W5500_SOCK_4Byte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
</UL>

<P><STRONG><a name="[ff]"></a>API_Write_W5500_nByte</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, api_w5500.o(i.API_Write_W5500_nByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = API_Write_W5500_nByte &rArr; API_SPI0_Send_Short &rArr; API_SPI0_Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Short
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
</UL>

<P><STRONG><a name="[1b]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DMA1_Channel0_IRQHandler &rArr; dma_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_get
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DMA1_Channel1_IRQHandler &rArr; dma_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_get
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DMA1_Channel2_IRQHandler &rArr; dma_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_get
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[107]"></a>DRV_SPI_SwapByte</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, spi.o(i.DRV_SPI_SwapByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
</UL>

<P><STRONG><a name="[1e]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e3]"></a>EEPROM_SPI_ReadBuffer</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, eeprom_spi.o(i.EEPROM_SPI_ReadBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_Net_Parameters
</UL>

<P><STRONG><a name="[108]"></a>EEPROM_SPI_SendInstruction</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, eeprom_spi.o(i.EEPROM_SPI_SendInstruction))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
</UL>

<P><STRONG><a name="[109]"></a>EEPROM_SPI_WaitStandbyState</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[ed]"></a>EEPROM_SPI_WriteBuffer</STRONG> (Thumb, 394 bytes, Stack size 40 bytes, eeprom_spi.o(i.EEPROM_SPI_WriteBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
</UL>

<P><STRONG><a name="[10a]"></a>EEPROM_SPI_WritePage</STRONG> (Thumb, 170 bytes, Stack size 32 bytes, eeprom_spi.o(i.EEPROM_SPI_WritePage))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
</UL>

<P><STRONG><a name="[10c]"></a>EEPROM_WriteDisable</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, eeprom_spi.o(i.EEPROM_WriteDisable))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = EEPROM_WriteDisable &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[10b]"></a>EEPROM_WriteEnable</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, eeprom_spi.o(i.EEPROM_WriteEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = EEPROM_WriteEnable &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[49]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI10_15_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI10_15_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10f]"></a>FML_USART_RecvTask</STRONG> (Thumb, 174 bytes, Stack size 2072 bytes, usart.o(i.FML_USART_RecvTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadBytesToBuffer
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[112]"></a>GPIO_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = GPIO_Init &rArr; Init_GPIO_TS5A339 &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[19]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[113]"></a>Init_GPIO_TS5A339</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, gpio.o(i.Init_GPIO_TS5A339))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Init_GPIO_TS5A339 &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[1a]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[114]"></a>RTC_Init</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, rtc.o(i.RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = RTC_Init &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_rtc_clock_config
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[11d]"></a>SPI1_Init</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, spi.o(i.SPI1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SPI1_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1d]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>SystemInit</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[120]"></a>TIMER1_Init</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, time.o(i.TIMER1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TIMER1_Init &rArr; timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_shadow_config
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_pulse_value_config
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_mode_config
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3e]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIMER2_IRQHandler &rArr; usb_timer_irq
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIMER3_IRQHandler &rArr; TIM_PeriodElapsedCallback &rArr; gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12a]"></a>TIMER3_Init</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, time.o(i.TIMER3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TIMER3_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[58]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIMER6_IRQHandler &rArr; TIM_PeriodElapsedCallback &rArr; gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12e]"></a>TIMER6_Init</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, time.o(i.TIMER6_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TIMER6_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[129]"></a>TIM_PeriodElapsedCallback</STRONG> (Thumb, 446 bytes, Stack size 8 bytes, time.o(i.TIM_PeriodElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_PeriodElapsedCallback &rArr; gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_key_state_get
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_1MS_RunTask
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_IRQHandler
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_IRQHandler
</UL>

<P><STRONG><a name="[132]"></a>UART_IDLECallBack</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usart.o(i.UART_IDLECallBack))
<BR><BR>[Stack]<UL><LI>Max Depth = 2104<LI>Call Chain = UART_IDLECallBack &rArr; FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_clear
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[135]"></a>UART_RxCpltCallback</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usart.o(i.UART_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_RxCpltCallback &rArr; RecvDataHandler &rArr; AddByteToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RecvDataHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[136]"></a>USART0_Init</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, usart.o(i.USART0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = USART0_Init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[48]"></a>USART2_IRQHandler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2112<LI>Call Chain = USART2_IRQHandler &rArr; UART_IDLECallBack &rArr; FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>USBFS_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USBFS_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = USBFS_IRQHandler &rArr; usbd_isr &rArr; usbd_int_epout &rArr; usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USBFS_WKUP_IRQHandler &rArr; resume_mcu_clk
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_pll48m_clock_config
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_ck48m_clock_config
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_clock_active
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a8]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[d9]"></a>delay_1ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_1ms))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Register_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_UDP
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Listen
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Socket_Connect
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Detect_Gateway
</UL>

<P><STRONG><a name="[11e]"></a>delay_decrement</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[d1]"></a>dma_interrupt_flag_clear</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_interrupt_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>

<P><STRONG><a name="[106]"></a>dma_interrupt_flag_get</STRONG> (Thumb, 516 bytes, Stack size 20 bytes, gd32f4xx_dma.o(i.dma_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = dma_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel2_IRQHandler
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel0_IRQHandler
</UL>

<P><STRONG><a name="[10e]"></a>exti_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
</UL>

<P><STRONG><a name="[10d]"></a>exti_interrupt_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
</UL>

<P><STRONG><a name="[7d]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, usart.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_file.o(.text)
</UL>
<P><STRONG><a name="[131]"></a>gd_eval_key_state_get</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, gd32f470v_start.o(i.gd_eval_key_state_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[ec]"></a>get_hard_rand_data</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, api_tnrg.o(i.get_hard_rand_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_hard_rand_data
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_get_true_random_data
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
</UL>

<P><STRONG><a name="[101]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[d4]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_nByte
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_2Byte
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_1Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
</UL>

<P><STRONG><a name="[d5]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_nByte
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_4Byte
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_2Byte
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_SOCK_1Byte
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_2Byte
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_W5500_1Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SOCK_Data_Buffer
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_HardWare_Rest
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_2Byte
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_SOCK_1Byte
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_W5500_1Byte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Read_SOCK_Data_Buffer
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
</UL>

<P><STRONG><a name="[147]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_key_state_get
</UL>

<P><STRONG><a name="[fa]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
</UL>

<P><STRONG><a name="[fb]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
</UL>

<P><STRONG><a name="[97]"></a>main</STRONG> (Thumb, 238 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 200 + Unknown Stack Size
<LI>Call Chain = main &rArr; API_Init_LAN &rArr; API_Init_Net_Parameters &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_vector_table_set
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Send_Data_S0
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_ReciveDATA_Handle
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[12b]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
</UL>

<P><STRONG><a name="[150]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>

<P><STRONG><a name="[14e]"></a>nvic_vector_table_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_vector_table_set))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[115]"></a>pmu_backup_write_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_pmu.o(i.pmu_backup_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[176]"></a>pmu_to_deepsleepmode</STRONG> (Thumb, 204 bytes, Stack size 8 bytes, gd32f4xx_pmu.o(i.pmu_to_deepsleepmode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = pmu_to_deepsleepmode
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_suspend
</UL>

<P><STRONG><a name="[140]"></a>rcu_ck48m_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_ck48m_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[15a]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[151]"></a>rcu_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[116]"></a>rcu_osci_on</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_osci_on))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[117]"></a>rcu_osci_stab_wait</STRONG> (Thumb, 342 bytes, Stack size 20 bytes, gd32f4xx_rcu.o(i.rcu_osci_stab_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[e8]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
</UL>

<P><STRONG><a name="[159]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>

<P><STRONG><a name="[158]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>

<P><STRONG><a name="[13f]"></a>rcu_pll48m_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_pll48m_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[118]"></a>rcu_rtc_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_rtc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[152]"></a>rcu_system_clock_source_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_system_clock_source_config))
<BR><BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[153]"></a>rcu_system_clock_source_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_system_clock_source_get))
<BR><BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[11a]"></a>rtc_init</STRONG> (Thumb, 190 bytes, Stack size 20 bytes, gd32f4xx_rtc.o(i.rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_exit
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[154]"></a>rtc_init_mode_enter</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[155]"></a>rtc_init_mode_exit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_exit))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[119]"></a>rtc_register_sync_wait</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_register_sync_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_register_sync_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[103]"></a>spi_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[f6]"></a>spi_i2s_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>

<P><STRONG><a name="[f5]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>

<P><STRONG><a name="[f4]"></a>spi_i2s_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Byte
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Read_Byte
</UL>

<P><STRONG><a name="[102]"></a>spi_init</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[14f]"></a>systick_config</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, systick.o(i.systick_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = systick_config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14d]"></a>timer_auto_reload_shadow_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[123]"></a>timer_channel_output_config</STRONG> (Thumb, 484 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_channel_output_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[125]"></a>timer_channel_output_mode_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[124]"></a>timer_channel_output_pulse_value_config</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config))
<BR><BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[126]"></a>timer_channel_output_shadow_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_shadow_config))
<BR><BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[121]"></a>timer_deinit</STRONG> (Thumb, 374 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[14b]"></a>timer_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_disable))
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_delay
</UL>

<P><STRONG><a name="[12d]"></a>timer_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[122]"></a>timer_init</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_init))
<BR><BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[14c]"></a>timer_interrupt_disable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_disable))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[12c]"></a>timer_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[12f]"></a>timer_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[128]"></a>timer_interrupt_flag_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_IRQHandler
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_IRQHandler
</UL>

<P><STRONG><a name="[e9]"></a>trng_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_trng.o(i.trng_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = trng_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
</UL>

<P><STRONG><a name="[ea]"></a>trng_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
</UL>

<P><STRONG><a name="[eb]"></a>trng_flag_get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_hard_rand_data
</UL>

<P><STRONG><a name="[148]"></a>trng_get_true_random_data</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_get_true_random_data))
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_hard_rand_data
</UL>

<P><STRONG><a name="[138]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[134]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[145]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[137]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[13b]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[146]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[133]"></a>usart_interrupt_flag_clear</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[13c]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[139]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[13a]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[141]"></a>usb_clock_active</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_usb_dev.o(i.usb_clock_active))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[164]"></a>usb_ctlep_startout</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, drv_usb_dev.o(i.usb_ctlep_startout))
<BR><BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_recev
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enum_error
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[16d]"></a>usb_iepintr_read</STRONG> (Thumb, 42 bytes, Stack size 12 bytes, drv_usb_dev.o(i.usb_iepintr_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = usb_iepintr_read
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[174]"></a>usb_rxfifo_read</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, drv_usb_core.o(i.usb_rxfifo_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usb_rxfifo_read
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_rxfifo
</UL>

<P><STRONG><a name="[127]"></a>usb_timer_irq</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, gd32f4xx_hw.o(i.usb_timer_irq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_timer_irq
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER2_IRQHandler
</UL>

<P><STRONG><a name="[172]"></a>usb_transc_active</STRONG> (Thumb, 142 bytes, Stack size 20 bytes, drv_usb_dev.o(i.usb_transc_active))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usb_transc_active
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
</UL>

<P><STRONG><a name="[16a]"></a>usb_transc_clrstall</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, drv_usb_dev.o(i.usb_transc_clrstall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_transc_clrstall
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall_clear
</UL>

<P><STRONG><a name="[15b]"></a>usb_transc_inxfer</STRONG> (Thumb, 268 bytes, Stack size 32 bytes, drv_usb_dev.o(i.usb_transc_inxfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_write
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
</UL>

<P><STRONG><a name="[168]"></a>usb_transc_outxfer</STRONG> (Thumb, 144 bytes, Stack size 20 bytes, drv_usb_dev.o(i.usb_transc_outxfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usb_transc_outxfer
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_recev
</UL>

<P><STRONG><a name="[169]"></a>usb_transc_stall</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, drv_usb_dev.o(i.usb_transc_stall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_transc_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall
</UL>

<P><STRONG><a name="[15d]"></a>usb_txfifo_flush</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, drv_usb_core.o(i.usb_txfifo_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = usb_txfifo_flush &rArr; usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_udelay
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
</UL>

<P><STRONG><a name="[15c]"></a>usb_txfifo_write</STRONG> (Thumb, 34 bytes, Stack size 20 bytes, drv_usb_core.o(i.usb_txfifo_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usb_txfifo_write
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_emptytxfifo_write
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_inxfer
</UL>

<P><STRONG><a name="[15e]"></a>usb_udelay</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, gd32f4xx_hw.o(i.usb_udelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_flush
</UL>

<P><STRONG><a name="[179]"></a>usbd_class_request</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, usbd_enum.o(i.usbd_class_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_class_request
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>

<P><STRONG><a name="[15f]"></a>usbd_ctl_recev</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_ctl_recev))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = usbd_ctl_recev &rArr; usbd_ep_recev &rArr; usb_transc_outxfer
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_recev
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_out_transc
</UL>

<P><STRONG><a name="[161]"></a>usbd_ctl_send</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_ctl_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
</UL>

<P><STRONG><a name="[163]"></a>usbd_ctl_status_recev</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_transc.o(i.usbd_ctl_status_recev))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = usbd_ctl_status_recev &rArr; usbd_ep_recev &rArr; usb_transc_outxfer
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_recev
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
</UL>

<P><STRONG><a name="[165]"></a>usbd_ctl_status_send</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_transc.o(i.usbd_ctl_status_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = usbd_ctl_status_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_out_transc
</UL>

<P><STRONG><a name="[167]"></a>usbd_enum_error</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, usbd_enum.o(i.usbd_enum_error))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = usbd_enum_error &rArr; usbd_ep_stall &rArr; usb_transc_stall
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>

<P><STRONG><a name="[160]"></a>usbd_ep_recev</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, usbd_core.o(i.usbd_ep_recev))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = usbd_ep_recev &rArr; usb_transc_outxfer
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_outxfer
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_recev
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_recev
</UL>

<P><STRONG><a name="[162]"></a>usbd_ep_send</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, usbd_core.o(i.usbd_ep_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_inxfer
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_send
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
</UL>

<P><STRONG><a name="[144]"></a>usbd_ep_stall</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, usbd_core.o(i.usbd_ep_stall))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_ep_stall &rArr; usb_transc_stall
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enum_error
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_std_setfeature
</UL>

<P><STRONG><a name="[142]"></a>usbd_ep_stall_clear</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, usbd_core.o(i.usbd_ep_stall_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_ep_stall_clear &rArr; usb_transc_clrstall
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_clrstall
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_std_clearfeature
</UL>

<P><STRONG><a name="[16b]"></a>usbd_in_transc</STRONG> (Thumb, 160 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_in_transc))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = usbd_in_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_recev
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_send
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[13d]"></a>usbd_isr</STRONG> (Thumb, 222 bytes, Stack size 16 bytes, drv_usbd_int.o(i.usbd_isr))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = usbd_isr &rArr; usbd_int_epout &rArr; usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_suspend
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_rxfifo
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_enumfinish
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_IRQHandler
</UL>

<P><STRONG><a name="[16f]"></a>usbd_out_transc</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_out_transc))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = usbd_out_transc &rArr; usbd_ctl_status_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_recev
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
</UL>

<P><STRONG><a name="[170]"></a>usbd_setup_transc</STRONG> (Thumb, 132 bytes, Stack size 24 bytes, usbd_transc.o(i.usbd_setup_transc))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_send
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_recev
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_vendor_request
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_standard_request
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enum_error
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_class_request
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
</UL>

<P><STRONG><a name="[178]"></a>usbd_standard_request</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, usbd_enum.o(i.usbd_standard_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_standard_request
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>

<P><STRONG><a name="[17a]"></a>usbd_vendor_request</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i.usbd_vendor_request))
<BR><BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>

<P><STRONG><a name="[8b]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[1b9]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[1ba]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[156]"></a>system_clock_168m_25m_hxtal</STRONG> (Thumb, 240 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_168m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[11f]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_168m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[13e]"></a>resume_mcu_clk</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.resume_mcu_clk))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = resume_mcu_clk
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_system_clock_source_get
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_system_clock_source_config
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[157]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, systick.o(i.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[11c]"></a>AddByteToBuffer</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, usart.o(i.AddByteToBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AddByteToBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RecvDataHandler
</UL>

<P><STRONG><a name="[110]"></a>ReadBytesToBuffer</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, usart.o(i.ReadBytesToBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
</UL>

<P><STRONG><a name="[11b]"></a>RecvDataHandler</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, usart.o(i.RecvDataHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RecvDataHandler &rArr; AddByteToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AddByteToBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
</UL>

<P><STRONG><a name="[166]"></a>usbd_emptytxfifo_write</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_emptytxfifo_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = usbd_emptytxfifo_write &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_write
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[177]"></a>usbd_int_enumfinish</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, drv_usbd_int.o(i.usbd_int_enumfinish))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[16c]"></a>usbd_int_epin</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_int_epin))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = usbd_int_epin &rArr; usbd_in_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_emptytxfifo_write
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_iepintr_read
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[16e]"></a>usbd_int_epout</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_int_epout))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = usbd_int_epout &rArr; usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_out_transc
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[171]"></a>usbd_int_reset</STRONG> (Thumb, 208 bytes, Stack size 72 bytes, drv_usbd_int.o(i.usbd_int_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = usbd_int_reset &rArr; usb_txfifo_flush &rArr; usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_flush
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_active
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[173]"></a>usbd_int_rxfifo</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, drv_usbd_int.o(i.usbd_int_rxfifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usbd_int_rxfifo &rArr; usb_rxfifo_read
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_rxfifo_read
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[175]"></a>usbd_int_suspend</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_int_suspend))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usbd_int_suspend &rArr; pmu_to_deepsleepmode
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_to_deepsleepmode
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[143]"></a>_usb_bos_desc_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_bos_desc_get))
<BR><BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_std_getdescriptor
</UL>

<P><STRONG><a name="[15]"></a>_usb_config_desc_get</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_config_desc_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_config_desc_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[14]"></a>_usb_dev_desc_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_dev_desc_get))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[9]"></a>_usb_std_clearfeature</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_clearfeature))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _usb_std_clearfeature &rArr; usbd_ep_stall_clear &rArr; usb_transc_clrstall
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[f]"></a>_usb_std_getconfiguration</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_std_getconfiguration))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_std_getconfiguration
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[d]"></a>_usb_std_getdescriptor</STRONG> (Thumb, 272 bytes, Stack size 32 bytes, usbd_enum.o(i._usb_std_getdescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _usb_std_getdescriptor
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_bos_desc_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[11]"></a>_usb_std_getinterface</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_getinterface))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[8]"></a>_usb_std_getstatus</STRONG> (Thumb, 192 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_getstatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _usb_std_getstatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[a]"></a>_usb_std_reserved</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_reserved))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[c]"></a>_usb_std_setaddress</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_std_setaddress))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_std_setaddress
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[10]"></a>_usb_std_setconfiguration</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_setconfiguration))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _usb_std_setconfiguration
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[e]"></a>_usb_std_setdescriptor</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_setdescriptor))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[b]"></a>_usb_std_setfeature</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_setfeature))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _usb_std_setfeature &rArr; usbd_ep_stall &rArr; usb_transc_stall
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[12]"></a>_usb_std_setinterface</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_setinterface))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _usb_std_setinterface
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[13]"></a>_usb_std_synchframe</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_synchframe))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[16]"></a>_usb_str_desc_get</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_str_desc_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_str_desc_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[149]"></a>hw_delay</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, gd32f4xx_hw.o(i.hw_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_disable
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_udelay
</UL>

<P><STRONG><a name="[14a]"></a>hw_time_set</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, gd32f4xx_hw.o(i.hw_time_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_disable
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_disable
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_auto_reload_shadow_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_delay
</UL>

<P><STRONG><a name="[7c]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
