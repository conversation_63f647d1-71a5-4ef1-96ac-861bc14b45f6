#ifndef __GPIO_H__
#define __GPIO_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "gd32f4xx.h"

#define EN_ESP32_PIN                         GPIO_PIN_13
#define EN_ESP32_GPIO_PORT                   GPIOC
#define EN_ESP32_GPIO_CLK                    RCU_GPIOC

#define ESP32_ENABLE	    GPIO_BOP(EN_ESP32_GPIO_PORT) = EN_ESP32_PIN;
#define ESP32_DISENABLE   GPIO_BC(EN_ESP32_GPIO_PORT) = EN_ESP32_PIN;//OFF


void GPIO_Init(void);



#define  SW_GAIN1       GPIO_PIN_0
#define  SW_GAIN2       GPIO_PIN_1
#define  SW_GAIN_PORT                   GPIOB
#define  SW_GAIN_GPIO_CLK                    RCU_GPIOB

#define  SW_GAIN1_Low()    gpio_bit_reset(SW_GAIN_PORT,SW_GAIN1);
#define  SW_GAIN1_Hi()     gpio_bit_set(SW_GAIN_PORT,SW_GAIN1);

#define  SW_GAIN2_Low()    gpio_bit_reset(SW_GAIN_PORT,SW_GAIN2);
#define  SW_GAIN2_Hi()     gpio_bit_set(SW_GAIN_PORT,SW_GAIN2);

void Init_GPIO_TS5A339(void);
void API_Chose_TS5A3359_GAIN(uint8_t Type);


#ifdef __cplusplus
}
#endif

#endif /* __GPIO_H__ */
