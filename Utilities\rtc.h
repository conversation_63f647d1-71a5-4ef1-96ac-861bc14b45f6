#ifndef __RTC_H__
#define __RTC_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "gd32f4xx.h"
extern uint8_t name_hour;
extern uint8_t name_min; 	

typedef struct
{
    uint8_t year;                                                               /*!< RTC year value: 0x0 - 0x99(BCD format) */
    uint8_t month;                                                              /*!< RTC month value */
    uint8_t date;                                                               /*!< RTC date value: 0x1 - 0x31(BCD format) */
    uint8_t day_of_week;                                                        /*!< RTC weekday value */
    uint8_t hour;                                                               /*!< RTC hour value */
    uint8_t minute;                                                             /*!< RTC minute value: 0x0 - 0x59(BCD format) */
    uint8_t second;                                                             /*!< RTC second value: 0x0 - 0x59(BCD format) */
	
}rtc_struct;

extern rtc_struct daytimestructure;

void RTC_Init(void);
void rtc_register_set(uint8_t year,uint8_t mon,uint8_t day,uint8_t hour,uint8_t min,uint8_t sec );

extern rtc_parameter_struct rtc_initpara;

#ifdef __cplusplus
}
#endif

#endif /* __RTC_H__ */

