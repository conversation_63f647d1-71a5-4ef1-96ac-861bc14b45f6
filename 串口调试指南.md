# GD32F470 串口调试指南

## 🔧 串口配置修改

### 修改内容
已将串口从PB6/PB7修改为PA9/PA10：

```c
// 修改前：PB6/PB7
gpio_af_set(GPIOB, GPIO_AF_7, GPIO_PIN_6);   // PB6 - TX
gpio_af_set(GPIOB, GPIO_AF_7, GPIO_PIN_7);   // PB7 - RX

// 修改后：PA9/PA10  
gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_9);   // PA9 - TX
gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_10);  // PA10 - RX
```

### 串口参数
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1  
- **校验位**: 无
- **流控**: 无

## 🔍 故障排除步骤

### 1. 硬件连接检查
```
GD32F470        USB转串口模块
PA9 (TX)   -->  RX
PA10 (RX)  -->  TX  
GND        -->  GND
VCC        -->  3.3V (可选)
```

### 2. 串口工具设置
推荐使用以下串口工具：
- **SecureCRT**
- **PuTTY** 
- **串口助手**
- **Tera Term**

设置参数：
- 波特率：115200
- 数据位：8
- 停止位：1
- 校验：无

### 3. 测试步骤

#### 步骤1：基础连接测试
程序启动时会发送"UART"字符，如果看到这个说明串口基本工作。

#### 步骤2：完整启动日志
正常情况下应该看到：
```
UART
USART0 PA9/PA10 初始化完成

========================================
  GD32F470 + W5500 网络通信系统启动
  编译时间: Dec  7 2024 16:30:25
========================================
系统初始化开始...
✓ 系统时钟配置完成
✓ GPIO初始化完成
✓ 串口0初始化完成
...
```

### 4. 常见问题及解决方案

#### 问题1：完全没有输出
**可能原因**：
- 串口线接错（TX/RX接反）
- 波特率不匹配
- 串口工具未正确连接

**解决方案**：
1. 检查TX/RX接线
2. 确认波特率115200
3. 检查串口工具端口选择

#### 问题2：有输出但乱码
**可能原因**：
- 波特率不匹配
- 时钟配置问题
- 串口参数设置错误

**解决方案**：
1. 确认波特率115200
2. 检查数据位、停止位、校验位设置
3. 尝试其他常用波特率（9600, 38400）

#### 问题3：部分字符丢失
**可能原因**：
- 串口缓冲区溢出
- 发送速度过快
- 硬件连接不稳定

**解决方案**：
1. 检查连接线质量
2. 增加发送延时
3. 检查电源稳定性

#### 问题4：只有启动信息，无后续输出
**可能原因**：
- 程序卡死在某个初始化步骤
- 网络初始化失败
- 中断配置问题

**解决方案**：
1. 检查W5500硬件连接
2. 检查网络配置
3. 使用调试器单步调试

## 🛠️ 调试技巧

### 1. 添加调试点
在关键位置添加printf输出：
```c
printf("DEBUG: 进入函数 %s\r\n", __FUNCTION__);
printf("DEBUG: 变量值 = %d\r\n", variable);
```

### 2. 使用LED指示
如果串口完全不工作，可以用LED指示程序运行状态：
```c
// 在关键位置切换LED状态
gpio_bit_toggle(GPIOC, GPIO_PIN_13);
```

### 3. 检查时钟配置
确保系统时钟正确配置，影响串口波特率：
```c
// 检查系统时钟频率
uint32_t sys_clk = rcu_clock_freq_get(CK_SYS);
printf("系统时钟: %lu Hz\r\n", sys_clk);
```

## 📋 测试清单

- [ ] 硬件连接正确（PA9-TX, PA10-RX）
- [ ] 串口工具参数正确（115200,8,N,1）
- [ ] 能看到"UART"启动字符
- [ ] 能看到完整的系统启动日志
- [ ] 网络初始化信息正常显示
- [ ] 数据收发日志正常显示

## 🔄 如果仍无输出

如果按照以上步骤仍然没有串口输出，请检查：

1. **确认引脚复用正确**：PA9/PA10确实支持USART0
2. **检查时钟使能**：GPIOA和USART0时钟已使能
3. **确认程序正常运行**：使用调试器或LED确认程序在运行
4. **尝试其他串口**：可以尝试使用USART2（PB10/PB11）

如果问题持续存在，建议使用示波器或逻辑分析仪检查PA9引脚是否有信号输出。
