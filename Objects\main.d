.\objects\main.o: USER\main.c
.\objects\main.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\main.o: .\Firmware\CMSIS\core_cm4.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\main.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h
.\objects\main.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\main.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\main.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h
.\objects\main.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\main.o: .\USER\gd32f4xx_libopt.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\main.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\main.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\main.o: .\Utilities\gd32f470v_start.h
.\objects\main.o: USER\systick.h
.\objects\main.o: .\Utilities\usart.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\main.o: .\Utilities\time.h
.\objects\main.o: .\Utilities\esp32_wifi.h
.\objects\main.o: .\Utilities\gpio.h
.\objects\main.o: .\Utilities\user_step.h
.\objects\main.o: .\Utilities\25LC080A.h
.\objects\main.o: .\Utilities\My_CRC.h
.\objects\main.o: .\Utilities\eeprom_spi.h
.\objects\main.o: .\Utilities\spi.h
.\objects\main.o: .\Utilities\esp32_wifi.h
.\objects\main.o: .\Utilities\dac.h
.\objects\main.o: .\Utilities\adc.h
.\objects\main.o: .\Utilities\mymath.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\math.h
.\objects\main.o: .\Utilities\whut_math.h
.\objects\main.o: E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\float.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\limits.h
.\objects\main.o: .\Utilities\flash.h
.\objects\main.o: .\Utilities\rtc.h
.\objects\main.o: .\Utilities\dma.h
.\objects\main.o: .\USBFS-object\drv_usb_hw.h
.\objects\main.o: .\USBFS-object\usb_conf.h
.\objects\main.o: .\USBFS-object\cdc_acm_core.h
.\objects\main.o: .\USBFS-object\usbd_enum.h
.\objects\main.o: .\USBFS-object\usbd_core.h
.\objects\main.o: .\USBFS-object\drv_usb_core.h
.\objects\main.o: .\USBFS-object\drv_usb_regs.h
.\objects\main.o: .\USBFS-object\usb_ch9_std.h
.\objects\main.o: .\USBFS-object\usbd_conf.h
.\objects\main.o: .\USBFS-object\drv_usb_dev.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\wchar.h
.\objects\main.o: .\USBFS-object\usb_cdc.h
.\objects\main.o: .\Utilities\usb.h
.\objects\main.o: .\Utilities\API_TNRG.h
.\objects\main.o: .\Utilities\API_W5500.h
