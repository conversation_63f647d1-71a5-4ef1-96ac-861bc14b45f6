#include "dma.h"
#include "adc.h"

void DMA1_CH0_Init(void)
{	  
	  /* ADC_DMA_channel configuration */
    dma_single_data_parameter_struct dma_single_data_parameter;
   
  	/* enable DMA clock */
    rcu_periph_clock_enable(RCU_DMA1);

    /* ADC DMA_channel configuration */
    dma_deinit(DMA1, DMA_CH0);
    
    /* initialize DMA single data mode */
    dma_single_data_parameter.periph_addr = (uint32_t)(&ADC_RDATA(ADC0));
    dma_single_data_parameter.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_single_data_parameter.memory0_addr = (uint32_t)(&adc_data[heart]);
    dma_single_data_parameter.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_single_data_parameter.periph_memory_width = DMA_PERIPH_WIDTH_16BIT;
    dma_single_data_parameter.direction = DMA_PERIPH_TO_MEMORY;
    dma_single_data_parameter.number = 4*sampling_rate;
    dma_single_data_parameter.priority = DMA_PRIORITY_ULTRA_HIGH;
	
//	  dma_single_data_parameter.circular_mode = ENABLE;
	
    dma_single_data_mode_init(DMA1, DMA_CH0, &dma_single_data_parameter);
    dma_channel_subperipheral_select(DMA1, DMA_CH0, DMA_SUBPERI0);

    /* enable DMA circulation mode */
    dma_circulation_enable(DMA1, DMA_CH0);
		
//    nvic_priority_group_set(NVIC_PRIGROUP_PRE1_SUB3);
    nvic_irq_enable(DMA1_Channel0_IRQn, 0, 0);
		
//		dma_interrupt_flag_clear(DMA1, DMA_CH0,DMA_CHXCTL_FTFIE);
//  	dma_interrupt_flag_clear(DMA1,DMA_CH0, DMA_CHXCTL_HTFIE);	  
		
		dma_interrupt_enable(DMA1,DMA_CH0,DMA_CHXCTL_FTFIE); 
		dma_interrupt_enable(DMA1,DMA_CH0,DMA_CHXCTL_HTFIE);  
   
    /* enable DMA channel */
    dma_channel_enable(DMA1, DMA_CH0);
	
}


void DMA1_CH1_Init(void)
{	  
	  /* ADC_DMA_channel configuration */
    dma_single_data_parameter_struct dma_single_data_parameter;
   
  	/* enable DMA clock */
    rcu_periph_clock_enable(RCU_DMA1);

    /* ADC DMA_channel configuration */
    dma_deinit(DMA1, DMA_CH1);
    
    /* initialize DMA single data mode */
    dma_single_data_parameter.periph_addr = (uint32_t)(&ADC_RDATA(ADC2));
    dma_single_data_parameter.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_single_data_parameter.memory0_addr = (uint32_t)(&adc_data[pd]);
    dma_single_data_parameter.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_single_data_parameter.periph_memory_width = DMA_PERIPH_WIDTH_16BIT;
    dma_single_data_parameter.direction = DMA_PERIPH_TO_MEMORY;
    dma_single_data_parameter.number = 4*sampling_rate;
    dma_single_data_parameter.priority = DMA_PRIORITY_ULTRA_HIGH;
	
//	  dma_single_data_parameter.circular_mode = ENABLE;
	
    dma_single_data_mode_init(DMA1, DMA_CH1, &dma_single_data_parameter);
    dma_channel_subperipheral_select(DMA1, DMA_CH1, DMA_SUBPERI2);

    /* enable DMA circulation mode */
    dma_circulation_enable(DMA1, DMA_CH1);
		
//    nvic_priority_group_set(NVIC_PRIGROUP_PRE1_SUB3);
    nvic_irq_enable(DMA1_Channel1_IRQn, 0, 0);
		
		dma_interrupt_enable(DMA1,DMA_CH1,DMA_CHXCTL_FTFIE); 
		dma_interrupt_enable(DMA1,DMA_CH1,DMA_CHXCTL_HTFIE);  
   
    /* enable DMA channel */
    dma_channel_enable(DMA1, DMA_CH1);
	
}

void DMA1_CH2_Init(void)
{	  
	  /* ADC_DMA_channel configuration */
    dma_single_data_parameter_struct dma_single_data_parameter;
   
  	/* enable DMA clock */
    rcu_periph_clock_enable(RCU_DMA1);

    /* ADC DMA_channel configuration */
    dma_deinit(DMA1, DMA_CH2);
    
    /* initialize DMA single data mode */
    dma_single_data_parameter.periph_addr = (uint32_t)(&ADC_RDATA(ADC1));
    dma_single_data_parameter.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_single_data_parameter.memory0_addr = (uint32_t)(adc_data[breath]);
    dma_single_data_parameter.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_single_data_parameter.periph_memory_width = DMA_PERIPH_WIDTH_16BIT;
    dma_single_data_parameter.direction = DMA_PERIPH_TO_MEMORY;
    dma_single_data_parameter.number = 4*sampling_rate;
    dma_single_data_parameter.priority = DMA_PRIORITY_ULTRA_HIGH;
	
//	  dma_single_data_parameter.circular_mode = ENABLE;
	
    dma_single_data_mode_init(DMA1, DMA_CH2, &dma_single_data_parameter);
    dma_channel_subperipheral_select(DMA1, DMA_CH2, DMA_SUBPERI1);

    /* enable DMA circulation mode */
    dma_circulation_enable(DMA1, DMA_CH2);
		
//    nvic_priority_group_set(NVIC_PRIGROUP_PRE1_SUB3);
    nvic_irq_enable(DMA1_Channel2_IRQn, 0, 0);
		
//		dma_interrupt_flag_clear(DMA1, DMA_CH0,DMA_CHXCTL_FTFIE);
//  	dma_interrupt_flag_clear(DMA1,DMA_CH0, DMA_CHXCTL_HTFIE);	  
		
		dma_interrupt_enable(DMA1,DMA_CH2,DMA_CHXCTL_FTFIE); 
		dma_interrupt_enable(DMA1,DMA_CH2,DMA_CHXCTL_HTFIE);  
   
    /* enable DMA channel */
    dma_channel_enable(DMA1, DMA_CH2);
	
}
