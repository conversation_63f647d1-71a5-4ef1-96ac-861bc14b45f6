#ifndef EEPROM_SPI_H
#define EEPROM_SPI_H

/* Includes ------------------------------------------------------------------*/
// #include "main.h"
#include "gd32f4xx.h"
#include "systick.h"


/* 25LC1024 SPI EEPROM defines */
/* SPI EEPROM支持下表6条命令，即READ（读内存）、WRITE（写内存）、WREN（写使能）、WRDI（写禁止）、RDSR（读状态寄存器）、WRSR（写状态寄存器） */
#define EEPROM_READ  0x03  /*!< 从存储阵列的指定地址开始读取数据 */
#define EEPROM_WRITE 0x02  /*!< 往存储阵列的指定地址开始写数据 */
#define EEPROM_WREN  0x06  /*!< 设置写使能锁存位（使能写操作） */
#define EEPROM_WRDI  0x04  /*!< 复位写使能锁存位（失能写操作） */
#define EEPROM_RDSR  0x05  /*!< 读状态寄存器 */
#define EEPROM_WRSR  0x01  /*!< 写状态寄存器 */



/*RDSR（读状态寄存器 8bits）*/
#define EEPROM_WIP_RDY         0x00  /*!< no write is in progress 只读*/
#define EEPROM_WIP_BUSY        0x01  /*!< busy with a write operation 只读*/
#define EEPROM_WEL_EN          0x02  /*!< the latch allows writes to the array只读 */
#define EEPROM_WEL_DIS         0x00  /*!< the latch prohibits writes to the array 只读*/
#define EEPROM_BP00            0x00  /*没有被保护的阵列*/
#define EEPROM_BP01            0x04  /*被保护的阵列高1/4地址（扇区3）*/
#define EEPROM_BP10            0x08  /*被保护的阵列高1/2地址（扇区2,3）*/
#define EEPROM_BP11            0x0C  /*被保护的阵列全部扇区（扇区0,1,2,3）*/
#define EEPROM_WPEN            0x80  /*写保护*/


//#define EEPROM_PAGESIZE        256   /*!< Pagesize according to documentation :256Byte*/
///*25LC1024：1Mbit= 1024Kbit =128KB  = 512*256B = 131072 X8bit
//第1页地址：  00000H-000FFH  长度256B
//                  0-255
//第2页地址：  00100H-001FFH  长度256B
//                256-511
//第3页地址：  00200H-002FFH  长度256B
//                512-1278
//第4页地址：  00300H-003FFH  长度256B
//`
//`
//`
//第510页地址：1FD00H-1FDFFH  长度256B
//第511页地址：1FE00H-1FEFFH  长度256B
//第512页地址：1FF00H-1FFFFH  长度256B
//*/
#define EEPROM_PAGESIZE        16   /*!< Pagesize according to documentation :256Byte*/
/*25LC080A： 8Kbit =1KB  = 64*16B = 1024 X8bit
第1页地址：  0000H-000FH  长度16B
                  0-15
第2页地址：  0010H-001FH  长度16B
                16-31
第3页地址：  0020H-002FH  长度16B
                32-63
第4页地址：  0030H-003FH  长度16B
`
`
`
第62页地址：03D0H-03DFH   长度16B
第63页地址：03E0H-03EFH   长度16B
第64页地址：03F0H-03FFH   长度16B
*/

//#define EEPROM_CS_HIGH()    HAL_GPIO_WritePin(SPI2_CS_GPIO_Port, SPI2_CS_Pin, GPIO_PIN_SET)
//#define EEPROM_CS_LOW()     HAL_GPIO_WritePin(SPI2_CS_GPIO_Port, SPI2_CS_Pin, GPIO_PIN_RESET)
//#define SPI2_CS(n)		(n?HAL_GPIO_WritePin(SPI_CS_GPIO_Port,SPI_CS_Pin,GPIO_PIN_SET):HAL_GPIO_WritePin(SPI_CS_GPIO_Port,SPI_CS_Pin,GPIO_PIN_RESET))

/**
 * @brief EEPROM Operations statuses
 */
typedef enum {
    EEPROM_STATUS_PENDING,
    EEPROM_STATUS_COMPLETE,
    EEPROM_STATUS_ERROR
} EEPROMStatus;


uint8_t EEPROM_SPI_WaitStandbyState(void);//等待操作完成函数
//在指定地址开始读取指定长度的数据
EEPROMStatus EEPROM_SPI_ReadBuffer (uint8_t* pBuffer, uint32_t ReadAddr,  uint16_t NumByteToRead);

//写入一个字节数据，未实现类似EEPROM_SendByte()
EEPROMStatus EEPROM_SPI_WriteByte  (uint8_t* pBuffer, uint32_t WriteAddr);
//在指定地址开始写入指定长度的数据
EEPROMStatus EEPROM_SPI_WriteBuffer(uint8_t* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite);
//在指定地址开始写入不大于整页数据
EEPROMStatus EEPROM_SPI_WritePage  (uint8_t* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite);



/* Low layer functions 底层操作函数*/
uint8_t EEPROM_SendByte(uint8_t byte);//发送一个字节，返回一个字节

void EEPROM_WriteEnable(void);//写使能
void EEPROM_WriteDisable(void);//写失能

void    EEPROM_WriteStatusRegister(uint8_t regval);//写状态寄存器
uint8_t EEPROM_ReadStatusRegister (void);          //读状态寄存器

void EEPROM_SPI_SendInstruction(uint8_t *instruction, uint8_t size);//发送操作命令和数据地址

#ifdef __cplusplus
}
#endif

#endif

