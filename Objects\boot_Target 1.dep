Dependencies for Project 'boot', Target 'Target 1': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (.\Firmware\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s)(0x68947B9C)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

--pd "__UVISION_VERSION SETA 533" --pd "_RTE_ SETA 1" --pd "GD32F470 SETA 1" --pd "_RTE_ SETA 1"

--list .\listings\startup_gd32f450_470.lst --xref -o .\objects\startup_gd32f450_470.o --depend .\objects\startup_gd32f450_470.d)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Firmware\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x68947B9C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\USER\gd32f4xx_it.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_it.o --omf_browse .\objects\gd32f4xx_it.crf --depend .\objects\gd32f4xx_it.d)
I (USER\main.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (USER\gd32f4xx_it.h)(0x68947B9F)
I (.\Utilities\gd32f470v_start.h)(0x68947B9F)
I (USER\systick.h)(0x68947B9F)
I (.\Utilities\usart.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Utilities\time.h)(0x68947B9F)
I (.\Utilities\esp32_wifi.h)(0x68947B9F)
I (.\Utilities\gpio.h)(0x68947B9F)
I (.\Utilities\user_step.h)(0x68947B9F)
I (.\Utilities\25LC080A.h)(0x68947B9F)
I (.\Utilities\My_CRC.h)(0x68947B9F)
I (.\Utilities\eeprom_spi.h)(0x68947B9F)
I (.\Utilities\spi.h)(0x68947B9F)
I (.\Utilities\dac.h)(0x68947B9F)
I (.\Utilities\adc.h)(0x68947B9F)
I (.\Utilities\mymath.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (.\Utilities\whut_math.h)(0x68947B9F)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (.\Utilities\flash.h)(0x68947B9F)
I (.\Utilities\rtc.h)(0x68947B9F)
I (.\USBFS-object\drv_usbd_int.h)(0x68947B9F)
I (.\USBFS-object\drv_usb_core.h)(0x68947B9F)
I (.\USBFS-object\drv_usb_regs.h)(0x68947B9F)
I (.\USBFS-object\usb_conf.h)(0x68947B9F)
I (.\USBFS-object\usb_ch9_std.h)(0x68947B9F)
I (.\USBFS-object\usbd_conf.h)(0x68947B9F)
I (.\USBFS-object\drv_usb_dev.h)(0x68947B9F)
F (.\USER\main.c)(0x689482E0)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\Utilities\gd32f470v_start.h)(0x68947B9F)
I (USER\systick.h)(0x68947B9F)
I (.\Utilities\usart.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Utilities\time.h)(0x68947B9F)
I (.\Utilities\esp32_wifi.h)(0x68947B9F)
I (.\Utilities\gpio.h)(0x68947B9F)
I (.\Utilities\user_step.h)(0x68947B9F)
I (.\Utilities\25LC080A.h)(0x68947B9F)
I (.\Utilities\My_CRC.h)(0x68947B9F)
I (.\Utilities\eeprom_spi.h)(0x68947B9F)
I (.\Utilities\spi.h)(0x68947B9F)
I (.\Utilities\dac.h)(0x68947B9F)
I (.\Utilities\adc.h)(0x68947B9F)
I (.\Utilities\mymath.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (.\Utilities\whut_math.h)(0x68947B9F)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (.\Utilities\flash.h)(0x68947B9F)
I (.\Utilities\rtc.h)(0x68947B9F)
I (.\Utilities\dma.h)(0x68947B9F)
I (.\USBFS-object\drv_usb_hw.h)(0x68947B9F)
I (.\USBFS-object\usb_conf.h)(0x68947B9F)
I (.\USBFS-object\cdc_acm_core.h)(0x68947B9F)
I (.\USBFS-object\usbd_enum.h)(0x68947B9F)
I (.\USBFS-object\usbd_core.h)(0x68947B9F)
I (.\USBFS-object\drv_usb_core.h)(0x68947B9F)
I (.\USBFS-object\drv_usb_regs.h)(0x68947B9F)
I (.\USBFS-object\usb_ch9_std.h)(0x68947B9F)
I (.\USBFS-object\usbd_conf.h)(0x68947B9F)
I (.\USBFS-object\drv_usb_dev.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\wchar.h)(0x5E8E2EB2)
I (.\USBFS-object\usb_cdc.h)(0x68947B9F)
I (.\Utilities\usb.h)(0x68947B9F)
I (.\Utilities\API_TNRG.h)(0x68947B9F)
I (.\Utilities\API_W5500.h)(0x6894803B)
F (.\USER\systick.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\systick.o --omf_browse .\objects\systick.crf --depend .\objects\systick.d)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (USER\systick.h)(0x68947B9F)
F (.\Utilities\25LC080A.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\25lc080a.o --omf_browse .\objects\25lc080a.crf --depend .\objects\25lc080a.d)
I (Utilities\25LC080A.h)(0x68947B9F)
I (Utilities\My_CRC.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (Utilities\eeprom_spi.h)(0x68947B9F)
I (.\USER\systick.h)(0x68947B9F)
I (Utilities\usart.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (.\Utilities\adc.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\adc.o --omf_browse .\objects\adc.crf --depend .\objects\adc.d)
I (Utilities\adc.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (Utilities\gd32f470v_start.h)(0x68947B9F)
I (Utilities\usart.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\USER\systick.h)(0x68947B9F)
F (.\Utilities\dac.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\dac.o --omf_browse .\objects\dac.crf --depend .\objects\dac.d)
I (Utilities\dac.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (Utilities\gd32f470v_start.h)(0x68947B9F)
F (.\Utilities\dma.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\dma.o --omf_browse .\objects\dma.crf --depend .\objects\dma.d)
I (Utilities\dma.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (Utilities\gd32f470v_start.h)(0x68947B9F)
I (Utilities\adc.h)(0x68947B9F)
I (Utilities\usart.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (.\Utilities\eeprom_spi.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\eeprom_spi.o --omf_browse .\objects\eeprom_spi.crf --depend .\objects\eeprom_spi.d)
I (Utilities\eeprom_spi.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\USER\systick.h)(0x68947B9F)
I (Utilities\spi.h)(0x68947B9F)
F (.\Utilities\esp32_wifi.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\esp32_wifi.o --omf_browse .\objects\esp32_wifi.crf --depend .\objects\esp32_wifi.d)
I (Utilities\esp32_wifi.h)(0x68947B9F)
I (Utilities\usart.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (Utilities\gpio.h)(0x68947B9F)
I (Utilities\user_step.h)(0x68947B9F)
I (Utilities\25LC080A.h)(0x68947B9F)
I (Utilities\My_CRC.h)(0x68947B9F)
I (Utilities\eeprom_spi.h)(0x68947B9F)
I (.\USER\systick.h)(0x68947B9F)
I (Utilities\spi.h)(0x68947B9F)
I (Utilities\dac.h)(0x68947B9F)
I (Utilities\gd32f470v_start.h)(0x68947B9F)
I (Utilities\adc.h)(0x68947B9F)
I (Utilities\mymath.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (Utilities\whut_math.h)(0x68947B9F)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (Utilities\flash.h)(0x68947B9F)
I (Utilities\rtc.h)(0x68947B9F)
F (.\Utilities\flash.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\flash.o --omf_browse .\objects\flash.crf --depend .\objects\flash.d)
I (Utilities\flash.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (.\Utilities\gpio.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gpio.o --omf_browse .\objects\gpio.crf --depend .\objects\gpio.d)
I (Utilities\gpio.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Utilities\My_CRC.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\my_crc.o --omf_browse .\objects\my_crc.crf --depend .\objects\my_crc.d)
I (Utilities\My_CRC.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Utilities\rtc.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\rtc.o --omf_browse .\objects\rtc.crf --depend .\objects\rtc.d)
I (Utilities\rtc.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (.\Utilities\spi.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\spi.o --omf_browse .\objects\spi.crf --depend .\objects\spi.d)
I (Utilities\spi.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (.\Utilities\time.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\time.o --omf_browse .\objects\time.crf --depend .\objects\time.d)
I (Utilities\time.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (Utilities\gd32f470v_start.h)(0x68947B9F)
I (Utilities\esp32_wifi.h)(0x68947B9F)
I (Utilities\usart.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (Utilities\gpio.h)(0x68947B9F)
I (Utilities\user_step.h)(0x68947B9F)
I (Utilities\25LC080A.h)(0x68947B9F)
I (Utilities\My_CRC.h)(0x68947B9F)
I (Utilities\eeprom_spi.h)(0x68947B9F)
I (.\USER\systick.h)(0x68947B9F)
I (Utilities\spi.h)(0x68947B9F)
I (Utilities\dac.h)(0x68947B9F)
I (Utilities\adc.h)(0x68947B9F)
I (Utilities\mymath.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (Utilities\whut_math.h)(0x68947B9F)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (Utilities\flash.h)(0x68947B9F)
I (Utilities\rtc.h)(0x68947B9F)
I (Utilities\API_W5500.h)(0x6894803B)
F (.\Utilities\usart.c)(0x68948281)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\usart.o --omf_browse .\objects\usart.crf --depend .\objects\usart.d)
I (Utilities\usart.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (.\Utilities\usb.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\usb.o --omf_browse .\objects\usb.crf --depend .\objects\usb.d)
I (Utilities\usb.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\USBFS-object\usbd_core.h)(0x68947B9F)
I (.\USBFS-object\drv_usb_core.h)(0x68947B9F)
I (.\USBFS-object\drv_usb_regs.h)(0x68947B9F)
I (.\USBFS-object\usb_conf.h)(0x68947B9F)
I (.\Utilities\gd32f470v_start.h)(0x68947B9F)
I (.\USBFS-object\usb_ch9_std.h)(0x68947B9F)
I (.\USBFS-object\usbd_conf.h)(0x68947B9F)
I (.\USBFS-object\drv_usb_dev.h)(0x68947B9F)
I (.\USBFS-object\cdc_acm_core.h)(0x68947B9F)
I (.\USBFS-object\usbd_enum.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\wchar.h)(0x5E8E2EB2)
I (.\USBFS-object\usb_cdc.h)(0x68947B9F)
I (Utilities\user_step.h)(0x68947B9F)
I (Utilities\25LC080A.h)(0x68947B9F)
I (Utilities\My_CRC.h)(0x68947B9F)
I (Utilities\eeprom_spi.h)(0x68947B9F)
I (.\USER\systick.h)(0x68947B9F)
I (Utilities\usart.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (Utilities\spi.h)(0x68947B9F)
I (Utilities\esp32_wifi.h)(0x68947B9F)
I (Utilities\gpio.h)(0x68947B9F)
I (Utilities\dac.h)(0x68947B9F)
I (Utilities\adc.h)(0x68947B9F)
I (Utilities\mymath.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (Utilities\whut_math.h)(0x68947B9F)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (Utilities\flash.h)(0x68947B9F)
I (Utilities\rtc.h)(0x68947B9F)
F (.\Utilities\user_step.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\user_step.o --omf_browse .\objects\user_step.crf --depend .\objects\user_step.d)
I (Utilities\user_step.h)(0x68947B9F)
I (Utilities\25LC080A.h)(0x68947B9F)
I (Utilities\My_CRC.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (Utilities\eeprom_spi.h)(0x68947B9F)
I (.\USER\systick.h)(0x68947B9F)
I (Utilities\usart.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (Utilities\spi.h)(0x68947B9F)
I (Utilities\esp32_wifi.h)(0x68947B9F)
I (Utilities\gpio.h)(0x68947B9F)
I (Utilities\dac.h)(0x68947B9F)
I (Utilities\gd32f470v_start.h)(0x68947B9F)
I (Utilities\adc.h)(0x68947B9F)
I (Utilities\mymath.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (Utilities\whut_math.h)(0x68947B9F)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (Utilities\flash.h)(0x68947B9F)
I (Utilities\rtc.h)(0x68947B9F)
F (.\Utilities\gd32f470v_start.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f470v_start.o --omf_browse .\objects\gd32f470v_start.crf --depend .\objects\gd32f470v_start.d)
I (Utilities\gd32f470v_start.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\USER\readme.txt)(0x68947B9F)()
F (.\Utilities\API_LAN_DATA_Process .c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o ".\objects\api_lan_data_process .o" --omf_browse ".\objects\api_lan_data_process .crf" --depend ".\objects\api_lan_data_process .d")
I (Utilities\API_LAN_DATA_Process.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
F (.\Utilities\API_TNRG.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\api_tnrg.o --omf_browse .\objects\api_tnrg.crf --depend .\objects\api_tnrg.d)
I (Utilities\API_TNRG.h)(0x68947B9F)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (Utilities\usart.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (Utilities\25LC080A.h)(0x68947B9F)
I (Utilities\My_CRC.h)(0x68947B9F)
I (Utilities\eeprom_spi.h)(0x68947B9F)
I (.\USER\systick.h)(0x68947B9F)
I (Utilities\spi.h)(0x68947B9F)
I (Utilities\API_W5500.h)(0x6894803B)
F (.\Utilities\API_W5500.c)(0x6894802C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\api_w5500.o --omf_browse .\objects\api_w5500.crf --depend .\objects\api_w5500.d)
I (Utilities\API_W5500.h)(0x6894803B)
I (Utilities\spi.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\USER\systick.h)(0x68947B9F)
I (Utilities\25LC080A.h)(0x68947B9F)
I (Utilities\My_CRC.h)(0x68947B9F)
I (Utilities\eeprom_spi.h)(0x68947B9F)
I (Utilities\usart.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (.\USBFS-object\drv_usb_core.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\drv_usb_core.o --omf_browse .\objects\drv_usb_core.crf --depend .\objects\drv_usb_core.d)
I (USBFS-object\drv_usb_core.h)(0x68947B9F)
I (USBFS-object\drv_usb_regs.h)(0x68947B9F)
I (USBFS-object\usb_conf.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\Utilities\gd32f470v_start.h)(0x68947B9F)
I (USBFS-object\usb_ch9_std.h)(0x68947B9F)
I (USBFS-object\usbd_conf.h)(0x68947B9F)
I (USBFS-object\drv_usb_hw.h)(0x68947B9F)
F (.\USBFS-object\drv_usb_dev.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\drv_usb_dev.o --omf_browse .\objects\drv_usb_dev.crf --depend .\objects\drv_usb_dev.d)
I (USBFS-object\drv_usb_hw.h)(0x68947B9F)
I (USBFS-object\usb_conf.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\Utilities\gd32f470v_start.h)(0x68947B9F)
I (USBFS-object\drv_usb_core.h)(0x68947B9F)
I (USBFS-object\drv_usb_regs.h)(0x68947B9F)
I (USBFS-object\usb_ch9_std.h)(0x68947B9F)
I (USBFS-object\usbd_conf.h)(0x68947B9F)
I (USBFS-object\drv_usb_dev.h)(0x68947B9F)
F (.\USBFS-object\drv_usbd_int.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\drv_usbd_int.o --omf_browse .\objects\drv_usbd_int.crf --depend .\objects\drv_usbd_int.d)
I (USBFS-object\usbd_conf.h)(0x68947B9F)
I (USBFS-object\usb_conf.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\Utilities\gd32f470v_start.h)(0x68947B9F)
I (USBFS-object\drv_usbd_int.h)(0x68947B9F)
I (USBFS-object\drv_usb_core.h)(0x68947B9F)
I (USBFS-object\drv_usb_regs.h)(0x68947B9F)
I (USBFS-object\usb_ch9_std.h)(0x68947B9F)
I (USBFS-object\drv_usb_dev.h)(0x68947B9F)
I (USBFS-object\usbd_transc.h)(0x68947B9F)
I (USBFS-object\usbd_core.h)(0x68947B9F)
I (.\Utilities\usb.h)(0x68947B9F)
I (.\USBFS-object\cdc_acm_core.h)(0x68947B9F)
I (.\USBFS-object\usbd_enum.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\wchar.h)(0x5E8E2EB2)
I (.\USBFS-object\usb_cdc.h)(0x68947B9F)
I (.\Utilities\user_step.h)(0x68947B9F)
I (.\Utilities\25LC080A.h)(0x68947B9F)
I (.\Utilities\My_CRC.h)(0x68947B9F)
I (.\Utilities\eeprom_spi.h)(0x68947B9F)
I (.\USER\systick.h)(0x68947B9F)
I (.\Utilities\usart.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Utilities\spi.h)(0x68947B9F)
I (.\Utilities\esp32_wifi.h)(0x68947B9F)
I (.\Utilities\gpio.h)(0x68947B9F)
I (.\Utilities\dac.h)(0x68947B9F)
I (.\Utilities\adc.h)(0x68947B9F)
I (.\Utilities\mymath.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (.\Utilities\whut_math.h)(0x68947B9F)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (.\Utilities\flash.h)(0x68947B9F)
I (.\Utilities\rtc.h)(0x68947B9F)
F (.\USBFS-object\usbd_core.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\usbd_core.o --omf_browse .\objects\usbd_core.crf --depend .\objects\usbd_core.d)
I (USBFS-object\usbd_core.h)(0x68947B9F)
I (USBFS-object\drv_usb_core.h)(0x68947B9F)
I (USBFS-object\drv_usb_regs.h)(0x68947B9F)
I (USBFS-object\usb_conf.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\Utilities\gd32f470v_start.h)(0x68947B9F)
I (USBFS-object\usb_ch9_std.h)(0x68947B9F)
I (USBFS-object\usbd_conf.h)(0x68947B9F)
I (USBFS-object\drv_usb_dev.h)(0x68947B9F)
I (USBFS-object\usbd_enum.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\wchar.h)(0x5E8E2EB2)
I (USBFS-object\drv_usb_hw.h)(0x68947B9F)
F (.\USBFS-object\usbd_enum.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\usbd_enum.o --omf_browse .\objects\usbd_enum.crf --depend .\objects\usbd_enum.d)
I (USBFS-object\usbd_enum.h)(0x68947B9F)
I (USBFS-object\usbd_core.h)(0x68947B9F)
I (USBFS-object\drv_usb_core.h)(0x68947B9F)
I (USBFS-object\drv_usb_regs.h)(0x68947B9F)
I (USBFS-object\usb_conf.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\Utilities\gd32f470v_start.h)(0x68947B9F)
I (USBFS-object\usb_ch9_std.h)(0x68947B9F)
I (USBFS-object\usbd_conf.h)(0x68947B9F)
I (USBFS-object\drv_usb_dev.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\wchar.h)(0x5E8E2EB2)
F (.\USBFS-object\usbd_transc.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\usbd_transc.o --omf_browse .\objects\usbd_transc.crf --depend .\objects\usbd_transc.d)
I (USBFS-object\usbd_enum.h)(0x68947B9F)
I (USBFS-object\usbd_core.h)(0x68947B9F)
I (USBFS-object\drv_usb_core.h)(0x68947B9F)
I (USBFS-object\drv_usb_regs.h)(0x68947B9F)
I (USBFS-object\usb_conf.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\Utilities\gd32f470v_start.h)(0x68947B9F)
I (USBFS-object\usb_ch9_std.h)(0x68947B9F)
I (USBFS-object\usbd_conf.h)(0x68947B9F)
I (USBFS-object\drv_usb_dev.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\wchar.h)(0x5E8E2EB2)
I (USBFS-object\usbd_transc.h)(0x68947B9F)
F (.\USBFS-object\gd32f4xx_hw.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_hw.o --omf_browse .\objects\gd32f4xx_hw.crf --depend .\objects\gd32f4xx_hw.d)
I (USBFS-object\drv_usb_hw.h)(0x68947B9F)
I (USBFS-object\usb_conf.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\Utilities\gd32f470v_start.h)(0x68947B9F)
F (.\USBFS-object\cdc_acm_core.c)(0x68947B9F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\cdc_acm_core.o --omf_browse .\objects\cdc_acm_core.crf --depend .\objects\cdc_acm_core.d)
I (USBFS-object\cdc_acm_core.h)(0x68947B9F)
I (USBFS-object\usbd_enum.h)(0x68947B9F)
I (USBFS-object\usbd_core.h)(0x68947B9F)
I (USBFS-object\drv_usb_core.h)(0x68947B9F)
I (USBFS-object\drv_usb_regs.h)(0x68947B9F)
I (USBFS-object\usb_conf.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947B9C)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947B9C)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947B9C)
I (.\USER\gd32f4xx_libopt.h)(0x68947B9F)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947B9C)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947B9C)
I (.\Utilities\gd32f470v_start.h)(0x68947B9F)
I (USBFS-object\usb_ch9_std.h)(0x68947B9F)
I (USBFS-object\usbd_conf.h)(0x68947B9F)
I (USBFS-object\drv_usb_dev.h)(0x68947B9F)
I (E:\MDK533\ARM\ARMCC\include\wchar.h)(0x5E8E2EB2)
I (USBFS-object\usb_cdc.h)(0x68947B9F)
F (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Lib\ARM\arm_cortexM4lf_math.lib)(0x5E8ED124)()
