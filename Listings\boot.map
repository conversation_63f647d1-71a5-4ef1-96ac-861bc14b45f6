Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_gd32f450_470.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER2_IRQHandler) for TIMER2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER3_IRQHandler) for TIMER3_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.EXTI10_15_IRQHandler) for EXTI10_15_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) for USBFS_WKUP_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER6_IRQHandler) for TIMER6_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) for DMA1_Channel0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) for DMA1_Channel2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USBFS_IRQHandler) for USBFS_IRQHandler
    startup_gd32f450_470.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to __main.o(!!!main) for __main
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(HEAP) for Heap_Mem
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(STACK) for Stack_Mem
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_168m_25m_hxtal) for system_clock_168m_25m_hxtal
    gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_interrupt_flag_get) for dma_interrupt_flag_get
    gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) refers to adc.o(i.ADC_ConvHalfCpltCallback) for ADC_ConvHalfCpltCallback
    gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) refers to adc.o(i.ADC_ConvCpltCallback) for ADC_ConvCpltCallback
    gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to gd32f4xx_dma.o(i.dma_interrupt_flag_get) for dma_interrupt_flag_get
    gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to adc.o(i.ADC_ConvHalfCpltCallback) for ADC_ConvHalfCpltCallback
    gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to adc.o(i.ADC_ConvCpltCallback) for ADC_ConvCpltCallback
    gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) refers to gd32f4xx_dma.o(i.dma_interrupt_flag_get) for dma_interrupt_flag_get
    gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) refers to adc.o(i.ADC_ConvHalfCpltCallback) for ADC_ConvHalfCpltCallback
    gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) refers to adc.o(i.ADC_ConvCpltCallback) for ADC_ConvCpltCallback
    gd32f4xx_it.o(i.EXTI10_15_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_get) for exti_interrupt_flag_get
    gd32f4xx_it.o(i.EXTI10_15_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f4xx_it.o(i.EXTI10_15_IRQHandler) refers to time.o(.bss) for my_key
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to gd32f4xx_hw.o(i.usb_timer_irq) for usb_timer_irq
    gd32f4xx_it.o(i.TIMER3_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER3_IRQHandler) refers to time.o(i.TIM_PeriodElapsedCallback) for TIM_PeriodElapsedCallback
    gd32f4xx_it.o(i.TIMER6_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER6_IRQHandler) refers to time.o(i.TIM_PeriodElapsedCallback) for TIM_PeriodElapsedCallback
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(i.UART_RxCpltCallback) for UART_RxCpltCallback
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(i.UART_IDLECallBack) for UART_IDLECallBack
    gd32f4xx_it.o(i.USBFS_IRQHandler) refers to drv_usbd_int.o(i.usbd_isr) for usbd_isr
    gd32f4xx_it.o(i.USBFS_IRQHandler) refers to cdc_acm_core.o(.bss) for cdc_acm
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to gd32f4xx_it.o(i.resume_mcu_clk) for resume_mcu_clk
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to gd32f4xx_rcu.o(i.rcu_pll48m_clock_config) for rcu_pll48m_clock_config
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to gd32f4xx_rcu.o(i.rcu_ck48m_clock_config) for rcu_ck48m_clock_config
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to drv_usb_dev.o(i.usb_clock_active) for usb_clock_active
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to cdc_acm_core.o(.bss) for cdc_acm
    gd32f4xx_it.o(i.resume_mcu_clk) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_it.o(i.resume_mcu_clk) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_it.o(i.resume_mcu_clk) refers to gd32f4xx_rcu.o(i.rcu_system_clock_source_config) for rcu_system_clock_source_config
    gd32f4xx_it.o(i.resume_mcu_clk) refers to gd32f4xx_rcu.o(i.rcu_system_clock_source_get) for rcu_system_clock_source_get
    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.main) refers to _printf_str.o(.text) for _printf_str
    main.o(i.main) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.main) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    main.o(i.main) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    main.o(i.main) refers to usart.o(i.USART0_Init) for USART0_Init
    main.o(i.main) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.main) refers to systick.o(i.systick_config) for systick_config
    main.o(i.main) refers to gpio.o(i.GPIO_Init) for GPIO_Init
    main.o(i.main) refers to time.o(i.TIMER6_Init) for TIMER6_Init
    main.o(i.main) refers to time.o(i.TIMER3_Init) for TIMER3_Init
    main.o(i.main) refers to rtc.o(i.RTC_Init) for RTC_Init
    main.o(i.main) refers to time.o(i.TIMER1_Init) for TIMER1_Init
    main.o(i.main) refers to spi.o(i.SPI1_Init) for SPI1_Init
    main.o(i.main) refers to api_tnrg.o(i.API_RNG_Init) for API_RNG_Init
    main.o(i.main) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    main.o(i.main) refers to api_w5500.o(i.API_Init_LAN) for API_Init_LAN
    main.o(i.main) refers to api_w5500.o(i.API_W5500_ReciveDATA_Handle) for API_W5500_ReciveDATA_Handle
    main.o(i.main) refers to api_w5500.o(i.API_W5500_Send_Data_S0) for API_W5500_Send_Data_S0
    main.o(i.main) refers to api_w5500.o(i.API_W5500_Check_Connection) for API_W5500_Check_Connection
    main.o(i.main) refers to main.o(.data) for TeseBuff
    main.o(i.main) refers to time.o(.bss) for g_tTimeSign
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.systick_config) refers to systick.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    25lc080a.o(i.EEPROM_WritePage) refers to h1_alloc.o(.text) for malloc
    25lc080a.o(i.EEPROM_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    25lc080a.o(i.EEPROM_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.EEPROM_WritePage) refers to h1_free.o(.text) for free
    25lc080a.o(i.EEPROM_WritePage) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.EEPROM_WritePage) refers to 25lc080a.o(i.USER_EEPROM_WriteByte) for USER_EEPROM_WriteByte
    25lc080a.o(i.EEPROM_WritePage) refers to 25lc080a.o(i.USER_EEPROM_RedeByte) for USER_EEPROM_RedeByte
    25lc080a.o(i.USER_EEPROM_RedeByte) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_RedePage) refers to h1_alloc.o(.text) for malloc
    25lc080a.o(i.USER_EEPROM_RedePage) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_RedePage) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.USER_EEPROM_RedePage) refers to h1_free.o(.text) for free
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to h1_alloc.o(.text) for malloc
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to 25lc080a.o(i.USER_EEPROM_RedeByte) for USER_EEPROM_RedeByte
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to h1_free.o(.text) for free
    25lc080a.o(i.USER_EEPROM_WritePage) refers to 25lc080a.o(i.EEPROM_WritePage) for EEPROM_WritePage
    adc.o(i.ADC0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_regular_channel_config) for adc_regular_channel_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_source_config) for adc_external_trigger_source_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_resolution_config) for adc_resolution_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_dma_request_after_last_enable) for adc_dma_request_after_last_enable
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_dma_mode_enable) for adc_dma_mode_enable
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_config) for adc_oversample_mode_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_enable) for adc_oversample_mode_enable
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    adc.o(i.ADC0_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    adc.o(i.ADC1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_regular_channel_config) for adc_regular_channel_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_source_config) for adc_external_trigger_source_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_resolution_config) for adc_resolution_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_dma_request_after_last_enable) for adc_dma_request_after_last_enable
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_dma_mode_enable) for adc_dma_mode_enable
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_config) for adc_oversample_mode_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_enable) for adc_oversample_mode_enable
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    adc.o(i.ADC1_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    adc.o(i.ADC2_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_regular_channel_config) for adc_regular_channel_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_source_config) for adc_external_trigger_source_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_resolution_config) for adc_resolution_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_dma_request_after_last_enable) for adc_dma_request_after_last_enable
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_dma_mode_enable) for adc_dma_mode_enable
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_config) for adc_oversample_mode_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_enable) for adc_oversample_mode_enable
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    adc.o(i.ADC2_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    adc.o(i.ADC_ConvCpltCallback) refers to gd32f4xx_dma.o(i.dma_interrupt_flag_clear) for dma_interrupt_flag_clear
    adc.o(i.ADC_ConvCpltCallback) refers to adc.o(.data) for my_adcdma
    adc.o(i.ADC_ConvHalfCpltCallback) refers to gd32f4xx_dma.o(i.dma_interrupt_flag_clear) for dma_interrupt_flag_clear
    adc.o(i.ADC_ConvHalfCpltCallback) refers to adc.o(.data) for my_adcdma
    dac.o(i.DAC1_Set_Vol) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    dac.o(i.DAC1_Set_Vol) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dac.o(i.DAC1_Set_Vol) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dac.o(i.DAC1_Set_Vol) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    dac.o(i.DAC1_Set_Vol) refers to gd32f4xx_dac.o(i.dac_data_set) for dac_data_set
    dac.o(i.DAC2_Set_Vol) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    dac.o(i.DAC2_Set_Vol) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dac.o(i.DAC2_Set_Vol) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dac.o(i.DAC2_Set_Vol) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    dac.o(i.DAC2_Set_Vol) refers to gd32f4xx_dac.o(i.dac_data_set) for dac_data_set
    dac.o(i.DAC_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    dac.o(i.DAC_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_deinit) for dac_deinit
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_trigger_disable) for dac_trigger_disable
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_wave_mode_config) for dac_wave_mode_config
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_output_buffer_disable) for dac_output_buffer_disable
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_concurrent_enable) for dac_concurrent_enable
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_concurrent_data_set) for dac_concurrent_data_set
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_circulation_enable) for dma_circulation_enable
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_interrupt_enable) for dma_interrupt_enable
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    dma.o(i.DMA1_CH0_Init) refers to adc.o(.RAM_D3) for adc_data
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_circulation_enable) for dma_circulation_enable
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_interrupt_enable) for dma_interrupt_enable
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    dma.o(i.DMA1_CH1_Init) refers to adc.o(.RAM_D3) for adc_data
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_circulation_enable) for dma_circulation_enable
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_interrupt_enable) for dma_interrupt_enable
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    dma.o(i.DMA1_CH2_Init) refers to adc.o(.RAM_D3) for adc_data
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to eeprom_spi.o(i.EEPROM_SendByte) for EEPROM_SendByte
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) refers to eeprom_spi.o(i.EEPROM_SPI_WritePage) for EEPROM_SPI_WritePage
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_WriteEnable) for EEPROM_WriteEnable
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) for EEPROM_SPI_WaitStandbyState
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_WriteDisable) for EEPROM_WriteDisable
    eeprom_spi.o(i.EEPROM_SendByte) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_WriteEnable) for EEPROM_WriteEnable
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_WriteDisable) for EEPROM_WriteDisable
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) for EEPROM_SPI_WaitStandbyState
    esp32_wifi.o(i.CheckCmdRepeatInList) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP32BLE_SendData_Add) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.ESP32BLE_SendData_Add) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP32BLE_SendData_Read) refers to esp32_wifi.o(i.ESP_SendBleData) for ESP_SendBleData
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(i.ESP_QueryNetConnectStatus) for ESP_QueryNetConnectStatus
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(i.ESP_QueryWifiInfo) for ESP_QueryWifiInfo
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(i.ESP_SetWifiConnect) for ESP_SetWifiConnect
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(i.ESP_BuildNetConnect) for ESP_BuildNetConnect
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(i.ESP_GetSNTP) for ESP_GetSNTP
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to user_step.o(.bss) for my_device_info
    esp32_wifi.o(i.ESP32_SendData_Add) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.ESP32_SendData_Add) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP32_SendData_Add_Plus) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.ESP32_SendData_Add_Plus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP32_SendData_Read) refers to esp32_wifi.o(i.ESP_SendNetData) for ESP_SendNetData
    esp32_wifi.o(i.ESP32_SendData_Read_Plus) refers to esp32_wifi.o(i.ESP_SendNetData) for ESP_SendNetData
    esp32_wifi.o(i.ESP_ATE0) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_ATE0) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_ATE0) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_ATE0) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_ATE1) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_ATE1) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_ATE1) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_ATE1) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_AT_TEST) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_AT_TEST) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_AT_TEST) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_AT_TEST) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEADVDATA) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEADVDATA) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEADVDATA) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEADVDATA) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEADVPARAM) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEADVPARAM) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEADVPARAM) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEADVPARAM) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEADVSTART) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEADVSTART) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEADVSTART) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEADVSTART) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEADVSTOP) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEADVSTOP) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEADVSTOP) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEADVSTOP) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEGATTSSRVCRE) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEGATTSSRVCRE) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEGATTSSRVCRE) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEGATTSSRVCRE) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEGATTSSRVSTART) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEGATTSSRVSTART) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEGATTSSRVSTART) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEGATTSSRVSTART) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEINIT) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEINIT) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEINIT) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEINIT) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLENAME) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLENAME) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLENAME) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLENAME) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_CloseNetConnect) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_CloseNetConnect) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_CloseNetConnect) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_CloseNetConnect) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_CloseWifiConnect) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_CloseWifiConnect) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_CloseWifiConnect) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_CloseWifiConnect) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_GetSNTP) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_GetSNTP) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_GetSNTP) refers to user_step.o(.bss) for my_net_time
    esp32_wifi.o(i.ESP_GetSNTP) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_GetSNTP) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to esp32_wifi.o(i.week_str2num) for week_str2num
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to esp32_wifi.o(i.month_str2num) for month_str2num
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to rtc.o(i.rtc_register_set) for rtc_register_set
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to user_step.o(.bss) for my_net_time
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to esp32_wifi.o(.data) for daytimestructure
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to rtc.o(.bss) for rtc_initpara
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to rtc.o(.data) for name_hour
    esp32_wifi.o(i.ESP_GetTcpConnectStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_GetWifiConnectStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_Get_DomainIP) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_Get_DomainIP) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_Get_DomainIP) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_Get_DomainIP) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    esp32_wifi.o(i.ESP_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.InitCmdTaskToList) for InitCmdTaskToList
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.InitSendDataRingBuf) for InitSendDataRingBuf
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.InitSendDataRingBufPlus) for InitSendDataRingBufPlus
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.InitBLESendDataRingBuf) for InitBLESendDataRingBuf
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_SetNetDataRecv) for ESP_SetNetDataRecv
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_QueryVersionInfo) for ESP_QueryVersionInfo
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_SetWorkMode) for ESP_SetWorkMode
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_SET_FindApListRAM) for ESP_SET_FindApListRAM
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_SetSNTP_CFG) for ESP_SetSNTP_CFG
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_RFPOWER) for ESP_RFPOWER
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_XRFPOWER) for ESP_XRFPOWER
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLEINIT) for ESP_BLEINIT
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLEGATTSSRVCRE) for ESP_BLEGATTSSRVCRE
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLEGATTSSRVSTART) for ESP_BLEGATTSSRVSTART
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLENAME) for ESP_BLENAME
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLEADVDATA) for ESP_BLEADVDATA
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLEADVPARAM) for ESP_BLEADVPARAM
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_SetWifiConnect) for ESP_SetWifiConnect
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_QueryNetConnectStatus) for ESP_QueryNetConnectStatus
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.User_Netdata_process) for User_Netdata_process
    esp32_wifi.o(i.ESP_Init) refers to user_step.o(.bss) for my_device_info
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.OnUnpackGeneralAck) for OnUnpackGeneralAck
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.ProcessingNetworkData) for ProcessingNetworkData
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.ProcessingBLEData) for ProcessingBLEData
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.JudgeWifiConnectionStatus) for JudgeWifiConnectionStatus
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.JudgeBLEConnectionStatus) for JudgeBLEConnectionStatus
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.JudgeNetConnectionStatus) for JudgeNetConnectionStatus
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.ESP_QueryNetConnectStatus) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_QueryNetConnectStatus) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_QueryNetConnectStatus) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_QueryNetConnectStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_QueryVersionInfo) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_QueryVersionInfo) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_QueryVersionInfo) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_QueryVersionInfo) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_QueryWifiInfo) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_QueryWifiInfo) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_QueryWifiInfo) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_QueryWifiInfo) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_QueryWifiInfoLists) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_QueryWifiInfoLists) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_QueryWifiInfoLists) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_QueryWifiInfoLists) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_QueryWorkMode) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_QueryWorkMode) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_QueryWorkMode) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_QueryWorkMode) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_RESET) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_RESET) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_RESET) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_RESET) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_RESTORE) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_RESTORE) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_RESTORE) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_RESTORE) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_RFPOWER) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_RFPOWER) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_RFPOWER) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_RFPOWER) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_RunTask) refers to esp32_wifi.o(i.ProcessAckOverTime) for ProcessAckOverTime
    esp32_wifi.o(i.ESP_RunTask) refers to esp32_wifi.o(i.SendCmdByTaskList) for SendCmdByTaskList
    esp32_wifi.o(i.ESP_RunTask) refers to esp32_wifi.o(i.ESP_SendData) for ESP_SendData
    esp32_wifi.o(i.ESP_RunTask) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SET_FindApListRAM) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SET_FindApListRAM) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SET_FindApListRAM) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_SET_FindApListRAM) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SendBleData) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.ESP_SendBleData) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SendBleData) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SendData) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SendData) refers to esp32_wifi.o(i.ESP32_SendData_Read) for ESP32_SendData_Read
    esp32_wifi.o(i.ESP_SendData) refers to esp32_wifi.o(i.ESP32_SendData_Read_Plus) for ESP32_SendData_Read_Plus
    esp32_wifi.o(i.ESP_SendData) refers to esp32_wifi.o(i.ESP32BLE_SendData_Read) for ESP32BLE_SendData_Read
    esp32_wifi.o(i.ESP_SendData) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SendNetData) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.ESP_SendNetData) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SendNetData) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetAutoConn) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SetAutoConn) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SetAutoConn) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_SetAutoConn) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetNetDataRecv) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetSNTP_CFG) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SetSNTP_CFG) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SetSNTP_CFG) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_SetSNTP_CFG) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetWorkMode) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SetWorkMode) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SetWorkMode) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetWorkMode) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_StatusTask) refers to usart.o(i.ESP32_IO_Init) for ESP32_IO_Init
    esp32_wifi.o(i.ESP_StatusTask) refers to usart.o(i.FML_USART_Register) for FML_USART_Register
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_Init) for ESP_Init
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_BLEADVSTOP) for ESP_BLEADVSTOP
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_SetWifiConnect) for ESP_SetWifiConnect
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(i.SetWifiInFo) for SetWifiInFo
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(i.GetWifiInFo) for GetWifiInFo
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(i.ESP32BLE_Command_Add) for ESP32BLE_Command_Add
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_CloseNetConnect) for ESP_CloseNetConnect
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_BuildNetConnect) for ESP_BuildNetConnect
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(i.Set_SeverInfo) for Set_SeverInfo
    esp32_wifi.o(i.ESP_StatusTask) refers to rt_memclr.o(.text) for __aeabi_memclr
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(i.Get_SeverInfo) for Get_SeverInfo
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_QueryWifiInfo) for ESP_QueryWifiInfo
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_GetSNTP) for ESP_GetSNTP
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_OnReciveParseUsartData) for ESP_OnReciveParseUsartData
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.UserProcess_JAPCmd) for UserProcess_JAPCmd
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(.bss) for my_device_info
    esp32_wifi.o(i.ESP_XRFPOWER) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_XRFPOWER) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_XRFPOWER) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_XRFPOWER) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.InitCmdTaskToList) refers to rt_memclr.o(.text) for __aeabi_memclr
    esp32_wifi.o(i.JudgeBLEConnectionStatus) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.JudgeBLEConnectionStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.JudgeNetConnectionStatus) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.JudgeNetConnectionStatus) refers to atoi.o(.text) for atoi
    esp32_wifi.o(i.JudgeNetConnectionStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.JudgeWifiConnectionStatus) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.JudgeWifiConnectionStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.JudgeWifiConnectionStatus) refers to user_step.o(.bss) for my_net_time
    esp32_wifi.o(i.OnPackBLEADVDATA_Cmd) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.OnPackBLEADVDATA_Cmd) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.OnPackBLEADVDATA_Cmd) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.OnPackBLEADVDATA_Cmd) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.OnPackBLENAME_Cmd) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.OnPackBLENAME_Cmd) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.OnPackBLENAME_Cmd) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.OnPackBLENAME_Cmd) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) refers to _printf_dec.o(.text) for _printf_int_dec
    esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) refers to esp32_wifi.o(.constdata) for .constdata
    esp32_wifi.o(i.OnPackCWModeCmd) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.OnPackCWModeCmd) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.OnPackCWModeCmd) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.OnPackCWModeCmd) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp32_wifi.o(i.OnPackCWModeCmd) refers to _printf_dec.o(.text) for _printf_int_dec
    esp32_wifi.o(i.OnPackCWModeCmd) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.OnPackJAPCmd) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.OnPackJAPCmd) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.OnPackJAPCmd) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.OnPackJAPCmd) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.OnPackRFPOWERCmd) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.OnPackRFPOWERCmd) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp32_wifi.o(i.OnPackRFPOWERCmd) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.OnPackRFPOWERCmd) refers to _printf_dec.o(.text) for _printf_int_dec
    esp32_wifi.o(i.OnPackRFPOWERCmd) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.OnPackRFPOWERCmd) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.OnPackRFPOWERCmd) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd) refers to _printf_dec.o(.text) for _printf_int_dec
    esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.OnPackTCPOrUDPDataCmd) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.OnPackTCPOrUDPDataCmd) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp32_wifi.o(i.OnPackTCPOrUDPDataCmd) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.OnPackTCPOrUDPDataCmd) refers to _printf_dec.o(.text) for _printf_int_dec
    esp32_wifi.o(i.OnPackTCPOrUDPDataCmd) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.OnPackTCPOrUDPDataCmd) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackCWModeAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackCWModeAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackCWModeAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackCWModeAck) refers to atoi.o(.text) for atoi
    esp32_wifi.o(i.OnUnpackCWModeAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackGeneralAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackGeneralAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackGeneralAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackGeneralAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackJAPAck) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.OnUnpackJAPAck) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.OnUnpackJAPAck) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.OnUnpackJAPAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackJAPAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackJAPAck) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp32_wifi.o(i.OnUnpackJAPAck) refers to esp32_wifi.o(i.split) for split
    esp32_wifi.o(i.OnUnpackJAPAck) refers to atoi.o(.text) for atoi
    esp32_wifi.o(i.OnUnpackJAPAck) refers to esp32_wifi.o(i.ConversionSignalIntensity) for ConversionSignalIntensity
    esp32_wifi.o(i.OnUnpackJAPAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackJAPAck) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.OnUnpackJAPAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackJAPAck) refers to user_step.o(.bss) for my_net_time
    esp32_wifi.o(i.OnUnpackRSTAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackRSTAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackRSTAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackRSTAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to esp32_wifi.o(i.ESP_GetSntpTimeInfo) for ESP_GetSntpTimeInfo
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to user_step.o(.bss) for my_net_time
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to rt_memclr.o(.text) for __aeabi_memclr
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to user_step.o(.bss) for my_device_info
    esp32_wifi.o(i.OnUnpackyNetConnectStatusAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackyNetConnectStatusAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackyNetConnectStatusAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackyNetConnectStatusAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ProcessAckOverTime) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.ProcessAckOverTime) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.ProcessAckOverTime) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ProcessingBLEData) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.ProcessingBLEData) refers to user_step.o(i.ESP32BLE_Command_Add) for ESP32BLE_Command_Add
    esp32_wifi.o(i.ProcessingBLEData) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.ProcessingNetworkData) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.ProcessingNetworkData) refers to atoi.o(.text) for atoi
    esp32_wifi.o(i.ProcessingNetworkData) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.SendCmdByTaskList) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.SendCmdByTaskList) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    esp32_wifi.o(i.SendCmdByTaskList) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.SendCmdByTaskList) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.SendCmdByTaskList) refers to esp32_wifi.o(i.ReadCmdInfoFromList) for ReadCmdInfoFromList
    esp32_wifi.o(i.SendCmdByTaskList) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    esp32_wifi.o(i.SendCmdByTaskList) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.SendCmdByTaskList) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.SendCmdByTaskList) refers to strlen.o(.text) for strlen
    esp32_wifi.o(i.SendCmdByTaskList) refers to usart.o(i.UARTx_SendBuffer) for UARTx_SendBuffer
    esp32_wifi.o(i.SendCmdByTaskList) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp32_wifi.o(i.SendCmdByTaskList) refers to noretval__2sprintf.o(.text) for __2sprintf
    esp32_wifi.o(i.SendCmdByTaskList) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.SendCmdByTaskList) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.UserProcess_BLEADVSTARCmd) refers to esp32_wifi.o(i.ESP_RESET) for ESP_RESET
    esp32_wifi.o(i.UserProcess_BLEADVSTARCmd) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    esp32_wifi.o(i.UserProcess_BLEADVSTARCmd) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.UserProcess_BLEADVSTOPCmd) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.UserProcess_JAPCmd) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.UserProcess_UpDataOkCmd) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    esp32_wifi.o(i.User_Netdata_process) refers to user_step.o(i.ESP32_Command_Add) for ESP32_Command_Add
    esp32_wifi.o(i.User_Netdata_process) refers to user_step.o(i.ESP32_Command_Add_Plus) for ESP32_Command_Add_Plus
    esp32_wifi.o(i.XOnPackRFPOWERCmd) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.XOnPackRFPOWERCmd) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.XOnPackRFPOWERCmd) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.XOnPackRFPOWERCmd) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.month_str2num) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.month_str2num) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.month_str2num) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.month_str2num) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.month_str2num) refers to strcmpv7m.o(.text) for strcmp
    esp32_wifi.o(i.month_str2num) refers to esp32_wifi.o(.data) for Month_name
    esp32_wifi.o(i.split) refers to strlen.o(.text) for strlen
    esp32_wifi.o(i.split) refers to strtok.o(.text) for strtok
    esp32_wifi.o(i.week_str2num) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp32_wifi.o(i.week_str2num) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp32_wifi.o(i.week_str2num) refers to _printf_str.o(.text) for _printf_str
    esp32_wifi.o(i.week_str2num) refers to noretval__2snprintf.o(.text) for __2snprintf
    esp32_wifi.o(i.week_str2num) refers to strcmpv7m.o(.text) for strcmp
    esp32_wifi.o(i.week_str2num) refers to esp32_wifi.o(.data) for Week_name
    esp32_wifi.o(.constdata) refers to esp32_wifi.o(.conststring) for .conststring
    esp32_wifi.o(.data) refers to esp32_wifi.o(.conststring) for .conststring
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackRSTAck) for OnUnpackRSTAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackVERSIONAck) for OnUnpackVERSIONAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackRFPOWERCmd) for OnPackRFPOWERCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.XOnPackRFPOWERCmd) for XOnPackRFPOWERCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackCWModeCmd) for OnPackCWModeCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackCWModeAck) for OnUnpackCWModeAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackJAPCmd) for OnPackJAPCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackJAPAck) for OnUnpackJAPAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackyNetConnectStatusAck) for OnUnpackyNetConnectStatusAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) for OnPackBuildTCPOrUDPCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck) for OnUnpackBuildTCPOrUDPAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackTCPOrUDPDataCmd) for OnPackTCPOrUDPDataCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck) for OnUnpackTCPOrUDPDataCmdAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackTCPOrUDPData) for OnPackTCPOrUDPData
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck) for OnUnpackTCPOrUDPDataSendAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd) for OnPackSetSNTPCFG_Cmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackSntpTimeAck) for OnUnpackSntpTimeAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackDomainIPAck) for OnUnpackDomainIPAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackBLENAME_Cmd) for OnPackBLENAME_Cmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackBLEADVDATA_Cmd) for OnPackBLEADVDATA_Cmd
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    flash.o(i.bsp_EraseCpuFlash) refers to flash.o(i.FLASH_If_Init) for FLASH_If_Init
    flash.o(i.bsp_EraseCpuFlash) refers to flash.o(i.bsp_GetSector) for bsp_GetSector
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    flash.o(i.bsp_WriteCpuFlash) refers to flash.o(i.bsp_CmpCpuFlash) for bsp_CmpCpuFlash
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_word_program) for fmc_word_program
    flash.o(i.bsp_WriteCpuFlash) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    gpio.o(i.API_Chose_TS5A3359_GAIN) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gpio.o(i.API_Chose_TS5A3359_GAIN) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gpio.o(i.GPIO_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gpio.o(i.GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gpio.o(i.GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gpio.o(i.GPIO_Init) refers to gpio.o(i.Init_GPIO_TS5A339) for Init_GPIO_TS5A339
    gpio.o(i.GPIO_Init) refers to gpio.o(i.API_Chose_TS5A3359_GAIN) for API_Chose_TS5A3359_GAIN
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    my_crc.o(i.CRC16_USB) refers to my_crc.o(i.InvertUint8) for InvertUint8
    my_crc.o(i.CRC16_USB) refers to my_crc.o(i.InvertUint16) for InvertUint16
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc.o(i.RTC_Init) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    rtc.o(i.RTC_Init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    rtc.o(i.RTC_Init) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.RTC_Init) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_register_set) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.rtc_register_set) refers to rtc.o(.bss) for rtc_initpara
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    spi.o(i.SPI1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    spi.o(i.SPI1_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    time.o(i.TIMER1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_config) for timer_channel_output_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config) for timer_channel_output_pulse_value_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_mode_config) for timer_channel_output_mode_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_shadow_config) for timer_channel_output_shadow_config
    time.o(i.TIMER3_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER3_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER6_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    time.o(i.TIM_PeriodElapsedCallback) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    time.o(i.TIM_PeriodElapsedCallback) refers to api_w5500.o(i.API_W5500_1MS_RunTask) for API_W5500_1MS_RunTask
    time.o(i.TIM_PeriodElapsedCallback) refers to gd32f470v_start.o(i.gd_eval_key_state_get) for gd_eval_key_state_get
    time.o(i.TIM_PeriodElapsedCallback) refers to time.o(.data) for tim6_msTic
    time.o(i.TIM_PeriodElapsedCallback) refers to time.o(.bss) for g_tTimeSign
    usart.o(i.API_Printf_Hex) refers to _printf_pad.o(.text) for _printf_pre_padding
    usart.o(i.API_Printf_Hex) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(i.API_Printf_Hex) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    usart.o(i.API_Printf_Hex) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    usart.o(i.API_Printf_Hex) refers to noretval__2printf.o(.text) for __2printf
    usart.o(i.ESP32_IO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.ESP32_IO_Init) refers to usart.o(i.InitBuffer) for InitBuffer
    usart.o(i.ESP32_IO_Init) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.FML_USART_RecvTask) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.FML_USART_RecvTask) refers to usart.o(i.ReadBytesToBuffer) for ReadBytesToBuffer
    usart.o(i.FML_USART_RecvTask) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    usart.o(i.FML_USART_RecvTask) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.FML_USART_Register) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.InitBuffer) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart.o(i.ReadBytesToBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    usart.o(i.RecvDataHandler) refers to usart.o(i.AddByteToBuffer) for AddByteToBuffer
    usart.o(i.UART_IDLECallBack) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_clear) for usart_interrupt_flag_clear
    usart.o(i.UART_IDLECallBack) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.UART_IDLECallBack) refers to usart.o(i.FML_USART_RecvTask) for FML_USART_RecvTask
    usart.o(i.UART_RxCpltCallback) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.UART_RxCpltCallback) refers to usart.o(i.RecvDataHandler) for RecvDataHandler
    usart.o(i.UART_RxCpltCallback) refers to usart.o(.RAM_D3) for usart2_buf
    usart.o(i.UARTx_SendBuffer) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart.o(i.UARTx_SendBuffer) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.USART0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    user_step.o(i.SysReset_Condition) refers to user_step.o(.NoInit) for g_JumpInit
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f470v_start.o(.data) for BEEP_CLK
    gd32f470v_start.o(i.gd_eval_BEEP_off) refers to gd32f470v_start.o(.data) for BEEP_GPIO_PIN
    gd32f470v_start.o(i.gd_eval_BEEP_on) refers to gd32f470v_start.o(.data) for BEEP_GPIO_PIN
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_syscfg.o(i.syscfg_exti_line_config) for syscfg_exti_line_config
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_exti.o(i.exti_init) for exti_init
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f470v_start.o(.data) for KEY_CLK
    gd32f470v_start.o(i.gd_eval_key_state_get) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    gd32f470v_start.o(i.gd_eval_key_state_get) refers to gd32f470v_start.o(.data) for KEY_PIN
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f470v_start.o(.data) for GPIO_CLK
    gd32f470v_start.o(i.gd_eval_led_off) refers to gd32f470v_start.o(.data) for GPIO_PIN
    gd32f470v_start.o(i.gd_eval_led_on) refers to gd32f470v_start.o(.data) for GPIO_PIN
    gd32f470v_start.o(i.gd_eval_led_toggle) refers to gd32f470v_start.o(.data) for GPIO_PIN
    api_tnrg.o(i.API_RNG_Init) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    api_tnrg.o(i.API_RNG_Init) refers to noretval__2printf.o(.text) for __2printf
    api_tnrg.o(i.API_RNG_Init) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_deinit) for trng_deinit
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_enable) for trng_enable
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_flag_get) for trng_flag_get
    api_tnrg.o(i.API_RNG_Init) refers to api_tnrg.o(i.get_hard_rand_data) for get_hard_rand_data
    api_tnrg.o(i.API_RNG_Init) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    api_tnrg.o(i.API_RNG_Init) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_tnrg.o(i.get_hard_rand_data) refers to gd32f4xx_trng.o(i.trng_flag_get) for trng_flag_get
    api_tnrg.o(i.get_hard_rand_data) refers to gd32f4xx_trng.o(i.trng_get_true_random_data) for trng_get_true_random_data
    api_w5500.o(i.API_Detect_Gateway) refers to api_w5500.o(i.API_Write_W5500_SOCK_4Byte) for API_Write_W5500_SOCK_4Byte
    api_w5500.o(i.API_Detect_Gateway) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Detect_Gateway) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_Detect_Gateway) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_Detect_Gateway) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_Detect_Gateway) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_Init_LAN) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    api_w5500.o(i.API_Init_LAN) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    api_w5500.o(i.API_Init_LAN) refers to _printf_dec.o(.text) for _printf_int_dec
    api_w5500.o(i.API_Init_LAN) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_SPI0_Init) for API_W5500_SPI0_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_GPIO_Init) for API_W5500_GPIO_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_Init_Net_Parameters) for API_Init_Net_Parameters
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_HardWare_Rest) for API_W5500_HardWare_Rest
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_Register_Init) for API_W5500_Register_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_Detect_Gateway) for API_Detect_Gateway
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_Socket_Init) for API_Socket_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_Socket_Set) for API_W5500_Socket_Set
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_Init_Net_Parameters) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    api_w5500.o(i.API_Init_Net_Parameters) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_Init_Net_Parameters) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_Process_Socket_Data) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    api_w5500.o(i.API_Process_Socket_Data) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    api_w5500.o(i.API_Process_Socket_Data) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    api_w5500.o(i.API_Process_Socket_Data) refers to _printf_dec.o(.text) for _printf_int_dec
    api_w5500.o(i.API_Process_Socket_Data) refers to _printf_pad.o(.text) for _printf_pre_padding
    api_w5500.o(i.API_Process_Socket_Data) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    api_w5500.o(i.API_Process_Socket_Data) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    api_w5500.o(i.API_Process_Socket_Data) refers to api_w5500.o(i.API_Read_SOCK_Data_Buffer) for API_Read_SOCK_Data_Buffer
    api_w5500.o(i.API_Process_Socket_Data) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_Process_Socket_Data) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_w5500.o(i.API_Process_Socket_Data) refers to api_w5500.o(.data) for rx_cnt
    api_w5500.o(i.API_Process_Socket_Data) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Read_W5500_SOCK_2Byte) for API_Read_W5500_SOCK_2Byte
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_SPI0_Read_Byte) for API_SPI0_Read_Byte
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Write_W5500_SOCK_2Byte) for API_Write_W5500_SOCK_2Byte
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Read_W5500_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Read_W5500_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Read_W5500_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Read_W5500_1Byte) refers to api_w5500.o(i.API_SPI0_Read_Byte) for API_SPI0_Read_Byte
    api_w5500.o(i.API_Read_W5500_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Read_W5500_SOCK_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Read_W5500_SOCK_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Read_W5500_SOCK_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Read_W5500_SOCK_1Byte) refers to api_w5500.o(i.API_SPI0_Read_Byte) for API_SPI0_Read_Byte
    api_w5500.o(i.API_Read_W5500_SOCK_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Read_W5500_SOCK_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Read_W5500_SOCK_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Read_W5500_SOCK_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Read_W5500_SOCK_2Byte) refers to api_w5500.o(i.API_SPI0_Read_Byte) for API_SPI0_Read_Byte
    api_w5500.o(i.API_Read_W5500_SOCK_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_SPI0_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    api_w5500.o(i.API_SPI0_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    api_w5500.o(i.API_SPI0_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    api_w5500.o(i.API_SPI0_Send_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    api_w5500.o(i.API_SPI0_Send_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    api_w5500.o(i.API_SPI0_Send_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    api_w5500.o(i.API_SPI0_Send_Short) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    api_w5500.o(i.API_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    api_w5500.o(i.API_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    api_w5500.o(i.API_Socket_Connect) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Socket_Connect) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_Socket_Connect) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_Socket_Init) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    api_w5500.o(i.API_Socket_Init) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    api_w5500.o(i.API_Socket_Init) refers to _printf_dec.o(.text) for _printf_int_dec
    api_w5500.o(i.API_Socket_Init) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_Socket_Init) refers to api_w5500.o(i.API_Write_W5500_SOCK_2Byte) for API_Write_W5500_SOCK_2Byte
    api_w5500.o(i.API_Socket_Init) refers to api_w5500.o(i.API_Write_W5500_SOCK_4Byte) for API_Write_W5500_SOCK_4Byte
    api_w5500.o(i.API_Socket_Init) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_Socket_Listen) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Socket_Listen) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_Socket_Listen) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_Socket_UDP) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Socket_UDP) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_Socket_UDP) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_1MS_RunTask) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_1MS_RunTask) refers to api_w5500.o(.data) for Temp_Cnt
    api_w5500.o(i.API_W5500_Check_Connection) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_Check_Connection) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_W5500_Check_Connection) refers to api_w5500.o(i.API_W5500_Socket_Set) for API_W5500_Socket_Set
    api_w5500.o(i.API_W5500_Check_Connection) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Check_Connection) refers to api_w5500.o(.data) for reconnect_cnt
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to _printf_dec.o(.text) for _printf_int_dec
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to api_w5500.o(i.API_Read_W5500_1Byte) for API_Read_W5500_1Byte
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to _printf_dec.o(.text) for _printf_int_dec
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to api_w5500.o(i.API_Read_W5500_1Byte) for API_Read_W5500_1Byte
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to api_w5500.o(i.API_Socket_Init) for API_Socket_Init
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Print_Status) refers to _printf_pad.o(.text) for _printf_pre_padding
    api_w5500.o(i.API_W5500_Print_Status) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    api_w5500.o(i.API_W5500_Print_Status) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    api_w5500.o(i.API_W5500_Print_Status) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    api_w5500.o(i.API_W5500_Print_Status) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    api_w5500.o(i.API_W5500_Print_Status) refers to _printf_dec.o(.text) for _printf_int_dec
    api_w5500.o(i.API_W5500_Print_Status) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_Print_Status) refers to api_w5500.o(i.API_Read_W5500_1Byte) for API_Read_W5500_1Byte
    api_w5500.o(i.API_W5500_Print_Status) refers to api_w5500.o(i.API_Read_W5500_SOCK_2Byte) for API_Read_W5500_SOCK_2Byte
    api_w5500.o(i.API_W5500_Print_Status) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_W5500_Print_Status) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to _printf_pad.o(.text) for _printf_pre_padding
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to _printf_dec.o(.text) for _printf_int_dec
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to _printf_str.o(.text) for _printf_str
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to api_w5500.o(i.API_W5500_Interrupt_Process) for API_W5500_Interrupt_Process
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to api_w5500.o(i.API_Process_Socket_Data) for API_Process_Socket_Data
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to api_w5500.o(.data) for debug_cnt
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Register_Init) refers to api_w5500.o(i.API_Write_W5500_1Byte) for API_Write_W5500_1Byte
    api_w5500.o(i.API_W5500_Register_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_W5500_Register_Init) refers to api_w5500.o(i.API_Write_W5500_nByte) for API_Write_W5500_nByte
    api_w5500.o(i.API_W5500_Register_Init) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_Register_Init) refers to api_w5500.o(i.API_Write_W5500_2Byte) for API_Write_W5500_2Byte
    api_w5500.o(i.API_W5500_Register_Init) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    api_w5500.o(i.API_W5500_SPI0_Init) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to _printf_dec.o(.text) for _printf_int_dec
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to _printf_pad.o(.text) for _printf_pre_padding
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to api_w5500.o(i.API_Write_SOCK_Data_Buffer) for API_Write_SOCK_Data_Buffer
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to api_w5500.o(.data) for send_cnt
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Socket_Set) refers to _printf_pad.o(.text) for _printf_pre_padding
    api_w5500.o(i.API_W5500_Socket_Set) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    api_w5500.o(i.API_W5500_Socket_Set) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    api_w5500.o(i.API_W5500_Socket_Set) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    api_w5500.o(i.API_W5500_Socket_Set) refers to noretval__2printf.o(.text) for __2printf
    api_w5500.o(i.API_W5500_Socket_Set) refers to api_w5500.o(i.API_Socket_Listen) for API_Socket_Listen
    api_w5500.o(i.API_W5500_Socket_Set) refers to api_w5500.o(i.API_Socket_Connect) for API_Socket_Connect
    api_w5500.o(i.API_W5500_Socket_Set) refers to api_w5500.o(i.API_Socket_UDP) for API_Socket_UDP
    api_w5500.o(i.API_W5500_Socket_Set) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Read_W5500_SOCK_2Byte) for API_Read_W5500_SOCK_2Byte
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Write_W5500_SOCK_2Byte) for API_Write_W5500_SOCK_2Byte
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Write_W5500_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_W5500_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_W5500_SOCK_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_SOCK_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_SOCK_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_SOCK_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_W5500_SOCK_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_SOCK_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_SOCK_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_SOCK_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_W5500_SOCK_4Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_SOCK_4Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_SOCK_4Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_SOCK_4Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_W5500_nByte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_nByte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_nByte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_nByte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    drv_usb_core.o(i.usb_basic_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    drv_usb_core.o(i.usb_basic_init) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    drv_usb_core.o(i.usb_core_init) refers to drv_usb_core.o(i.usb_core_reset) for usb_core_reset
    drv_usb_core.o(i.usb_core_init) refers to gd32f4xx_hw.o(i.usb_mdelay) for usb_mdelay
    drv_usb_core.o(i.usb_core_reset) refers to gd32f4xx_hw.o(i.usb_udelay) for usb_udelay
    drv_usb_core.o(i.usb_rxfifo_flush) refers to gd32f4xx_hw.o(i.usb_udelay) for usb_udelay
    drv_usb_core.o(i.usb_txfifo_flush) refers to gd32f4xx_hw.o(i.usb_udelay) for usb_udelay
    drv_usb_dev.o(i.usb_dev_stop) refers to drv_usb_core.o(i.usb_rxfifo_flush) for usb_rxfifo_flush
    drv_usb_dev.o(i.usb_dev_stop) refers to drv_usb_core.o(i.usb_txfifo_flush) for usb_txfifo_flush
    drv_usb_dev.o(i.usb_dev_suspend) refers to gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) for pmu_to_deepsleepmode
    drv_usb_dev.o(i.usb_devcore_init) refers to drv_usb_core.o(i.usb_set_txfifo) for usb_set_txfifo
    drv_usb_dev.o(i.usb_devcore_init) refers to drv_usb_core.o(i.usb_txfifo_flush) for usb_txfifo_flush
    drv_usb_dev.o(i.usb_devcore_init) refers to drv_usb_core.o(i.usb_rxfifo_flush) for usb_rxfifo_flush
    drv_usb_dev.o(i.usb_devcore_init) refers to drv_usb_dev.o(i.usb_devint_enable) for usb_devint_enable
    drv_usb_dev.o(i.usb_devcore_init) refers to drv_usb_dev.o(.data) for USBFS_TX_FIFO_SIZE
    drv_usb_dev.o(i.usb_rwkup_active) refers to gd32f4xx_hw.o(i.usb_mdelay) for usb_mdelay
    drv_usb_dev.o(i.usb_transc0_active) refers to drv_usb_dev.o(.constdata) for EP0_MAXLEN
    drv_usb_dev.o(i.usb_transc_active) refers to drv_usb_dev.o(.constdata) for EP0_MAXLEN
    drv_usb_dev.o(i.usb_transc_inxfer) refers to drv_usb_core.o(i.usb_txfifo_write) for usb_txfifo_write
    drv_usbd_int.o(i.usbd_emptytxfifo_write) refers to drv_usb_core.o(i.usb_txfifo_write) for usb_txfifo_write
    drv_usbd_int.o(i.usbd_int_enumfinish) refers to drv_usbd_int.o(.constdata) for USB_SPEED
    drv_usbd_int.o(i.usbd_int_epin) refers to drv_usb_dev.o(i.usb_iepintr_read) for usb_iepintr_read
    drv_usbd_int.o(i.usbd_int_epin) refers to usbd_transc.o(i.usbd_in_transc) for usbd_in_transc
    drv_usbd_int.o(i.usbd_int_epin) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    drv_usbd_int.o(i.usbd_int_epin) refers to drv_usbd_int.o(i.usbd_emptytxfifo_write) for usbd_emptytxfifo_write
    drv_usbd_int.o(i.usbd_int_epout) refers to usbd_transc.o(i.usbd_out_transc) for usbd_out_transc
    drv_usbd_int.o(i.usbd_int_epout) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    drv_usbd_int.o(i.usbd_int_epout) refers to usbd_transc.o(i.usbd_setup_transc) for usbd_setup_transc
    drv_usbd_int.o(i.usbd_int_reset) refers to drv_usb_core.o(i.usb_txfifo_flush) for usb_txfifo_flush
    drv_usbd_int.o(i.usbd_int_reset) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    drv_usbd_int.o(i.usbd_int_reset) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    drv_usbd_int.o(i.usbd_int_reset) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    drv_usbd_int.o(i.usbd_int_reset) refers to drv_usb_dev.o(i.usb_transc_active) for usb_transc_active
    drv_usbd_int.o(i.usbd_int_reset) refers to drv_usbd_int.o(.constdata) for <Data1>
    drv_usbd_int.o(i.usbd_int_rxfifo) refers to drv_usb_core.o(i.usb_rxfifo_read) for usb_rxfifo_read
    drv_usbd_int.o(i.usbd_int_rxfifo) refers to usb.o(.data) for flag_USB_Rx
    drv_usbd_int.o(i.usbd_int_suspend) refers to gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) for pmu_to_deepsleepmode
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_epout) for usbd_int_epout
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_epin) for usbd_int_epin
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_suspend) for usbd_int_suspend
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_rxfifo) for usbd_int_rxfifo
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_reset) for usbd_int_reset
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_enumfinish) for usbd_int_enumfinish
    usbd_core.o(i.usbd_connect) refers to gd32f4xx_hw.o(i.usb_mdelay) for usb_mdelay
    usbd_core.o(i.usbd_disconnect) refers to gd32f4xx_hw.o(i.usb_mdelay) for usb_mdelay
    usbd_core.o(i.usbd_ep_clear) refers to drv_usb_dev.o(i.usb_transc_deactivate) for usb_transc_deactivate
    usbd_core.o(i.usbd_ep_recev) refers to drv_usb_dev.o(i.usb_transc_outxfer) for usb_transc_outxfer
    usbd_core.o(i.usbd_ep_send) refers to drv_usb_dev.o(i.usb_transc_inxfer) for usb_transc_inxfer
    usbd_core.o(i.usbd_ep_setup) refers to drv_usb_dev.o(i.usb_transc_active) for usb_transc_active
    usbd_core.o(i.usbd_ep_setup) refers to usbd_core.o(.constdata) for ep_type
    usbd_core.o(i.usbd_ep_stall) refers to drv_usb_dev.o(i.usb_transc_stall) for usb_transc_stall
    usbd_core.o(i.usbd_ep_stall_clear) refers to drv_usb_dev.o(i.usb_transc_clrstall) for usb_transc_clrstall
    usbd_core.o(i.usbd_fifo_flush) refers to drv_usb_core.o(i.usb_txfifo_flush) for usb_txfifo_flush
    usbd_core.o(i.usbd_fifo_flush) refers to drv_usb_core.o(i.usb_rxfifo_flush) for usb_rxfifo_flush
    usbd_core.o(i.usbd_init) refers to usbd_enum.o(i.serial_string_get) for serial_string_get
    usbd_core.o(i.usbd_init) refers to drv_usb_core.o(i.usb_basic_init) for usb_basic_init
    usbd_core.o(i.usbd_init) refers to drv_usb_core.o(i.usb_core_init) for usb_core_init
    usbd_core.o(i.usbd_init) refers to usbd_core.o(i.usbd_disconnect) for usbd_disconnect
    usbd_core.o(i.usbd_init) refers to drv_usb_core.o(i.usb_curmode_set) for usb_curmode_set
    usbd_core.o(i.usbd_init) refers to drv_usb_dev.o(i.usb_devcore_init) for usb_devcore_init
    usbd_core.o(i.usbd_init) refers to usbd_core.o(i.usbd_connect) for usbd_connect
    usbd_enum.o(i._usb_std_clearfeature) refers to usbd_core.o(i.usbd_ep_stall_clear) for usbd_ep_stall_clear
    usbd_enum.o(i._usb_std_getdescriptor) refers to usbd_enum.o(i._usb_bos_desc_get) for _usb_bos_desc_get
    usbd_enum.o(i._usb_std_getdescriptor) refers to usbd_enum.o(.data) for std_desc_get
    usbd_enum.o(i._usb_std_getstatus) refers to usbd_enum.o(.data) for status
    usbd_enum.o(i._usb_std_setconfiguration) refers to usbd_enum.o(.data) for config
    usbd_enum.o(i._usb_std_setfeature) refers to usbd_core.o(i.usbd_ep_stall) for usbd_ep_stall
    usbd_enum.o(i.serial_string_get) refers to usbd_enum.o(i.int_to_unicode) for int_to_unicode
    usbd_enum.o(i.usbd_enum_error) refers to usbd_core.o(i.usbd_ep_stall) for usbd_ep_stall
    usbd_enum.o(i.usbd_enum_error) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    usbd_enum.o(i.usbd_standard_request) refers to usbd_enum.o(.data) for _std_dev_req
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_getstatus) for _usb_std_getstatus
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_clearfeature) for _usb_std_clearfeature
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_reserved) for _usb_std_reserved
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_setfeature) for _usb_std_setfeature
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_setaddress) for _usb_std_setaddress
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_getdescriptor) for _usb_std_getdescriptor
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_setdescriptor) for _usb_std_setdescriptor
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_getconfiguration) for _usb_std_getconfiguration
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_setconfiguration) for _usb_std_setconfiguration
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_getinterface) for _usb_std_getinterface
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_setinterface) for _usb_std_setinterface
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_synchframe) for _usb_std_synchframe
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_dev_desc_get) for _usb_dev_desc_get
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_config_desc_get) for _usb_config_desc_get
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_str_desc_get) for _usb_str_desc_get
    usbd_transc.o(i.usbd_ctl_recev) refers to usbd_core.o(i.usbd_ep_recev) for usbd_ep_recev
    usbd_transc.o(i.usbd_ctl_send) refers to usbd_core.o(i.usbd_ep_send) for usbd_ep_send
    usbd_transc.o(i.usbd_ctl_status_recev) refers to usbd_core.o(i.usbd_ep_recev) for usbd_ep_recev
    usbd_transc.o(i.usbd_ctl_status_recev) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    usbd_transc.o(i.usbd_ctl_status_send) refers to usbd_core.o(i.usbd_ep_send) for usbd_ep_send
    usbd_transc.o(i.usbd_ctl_status_send) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    usbd_transc.o(i.usbd_in_transc) refers to usbd_transc.o(i.usbd_ctl_send) for usbd_ctl_send
    usbd_transc.o(i.usbd_in_transc) refers to usbd_core.o(i.usbd_ep_send) for usbd_ep_send
    usbd_transc.o(i.usbd_in_transc) refers to usbd_transc.o(i.usbd_ctl_status_recev) for usbd_ctl_status_recev
    usbd_transc.o(i.usbd_out_transc) refers to usbd_transc.o(i.usbd_ctl_recev) for usbd_ctl_recev
    usbd_transc.o(i.usbd_out_transc) refers to usbd_transc.o(i.usbd_ctl_status_send) for usbd_ctl_status_send
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_enum.o(i.usbd_standard_request) for usbd_standard_request
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_enum.o(i.usbd_class_request) for usbd_class_request
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_enum.o(i.usbd_vendor_request) for usbd_vendor_request
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_transc.o(i.usbd_ctl_status_send) for usbd_ctl_status_send
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_transc.o(i.usbd_ctl_send) for usbd_ctl_send
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_transc.o(i.usbd_ctl_recev) for usbd_ctl_recev
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_enum.o(i.usbd_enum_error) for usbd_enum_error
    gd32f4xx_hw.o(i.hw_delay) refers to gd32f4xx_hw.o(i.hw_time_set) for hw_time_set
    gd32f4xx_hw.o(i.hw_delay) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    gd32f4xx_hw.o(i.hw_delay) refers to gd32f4xx_hw.o(.data) for delay_time
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_interrupt_disable) for timer_interrupt_disable
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable) for timer_auto_reload_shadow_enable
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_hw.o(.data) for timer_prescaler
    gd32f4xx_hw.o(i.usb_gpio_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f4xx_hw.o(i.usb_gpio_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f4xx_hw.o(i.usb_gpio_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f4xx_hw.o(i.usb_gpio_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd32f4xx_hw.o(i.usb_intr_config) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_hw.o(i.usb_intr_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f4xx_hw.o(i.usb_mdelay) refers to gd32f4xx_hw.o(i.hw_delay) for hw_delay
    gd32f4xx_hw.o(i.usb_rcu_config) refers to gd32f4xx_rcu.o(i.rcu_pll48m_clock_config) for rcu_pll48m_clock_config
    gd32f4xx_hw.o(i.usb_rcu_config) refers to gd32f4xx_rcu.o(i.rcu_ck48m_clock_config) for rcu_ck48m_clock_config
    gd32f4xx_hw.o(i.usb_rcu_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f4xx_hw.o(i.usb_timer_init) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_hw.o(i.usb_timer_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f4xx_hw.o(i.usb_timer_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f4xx_hw.o(i.usb_timer_irq) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_hw.o(i.usb_timer_irq) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    gd32f4xx_hw.o(i.usb_timer_irq) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    gd32f4xx_hw.o(i.usb_timer_irq) refers to gd32f4xx_hw.o(.data) for delay_time
    gd32f4xx_hw.o(i.usb_udelay) refers to gd32f4xx_hw.o(i.hw_delay) for hw_delay
    cdc_acm_core.o(i.cdc_acm_data_receive) refers to usbd_core.o(i.usbd_ep_recev) for usbd_ep_recev
    cdc_acm_core.o(i.cdc_acm_data_send) refers to usbd_core.o(i.usbd_ep_send) for usbd_ep_send
    cdc_acm_core.o(i.cdc_acm_deinit) refers to usbd_core.o(i.usbd_ep_clear) for usbd_ep_clear
    cdc_acm_core.o(i.cdc_acm_in) refers to usbd_core.o(i.usbd_ep_send) for usbd_ep_send
    cdc_acm_core.o(i.cdc_acm_init) refers to usbd_core.o(i.usbd_ep_setup) for usbd_ep_setup
    cdc_acm_core.o(i.cdc_acm_init) refers to cdc_acm_core.o(.constdata) for cdc_config_desc
    cdc_acm_core.o(i.cdc_acm_init) refers to cdc_acm_core.o(.bss) for cdc_handler
    cdc_acm_core.o(.constdata) refers to cdc_acm_core.o(.data) for serial_string
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(.constdata) for cdc_dev_desc
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_acm_init) for cdc_acm_init
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_acm_deinit) for cdc_acm_deinit
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_acm_req) for cdc_acm_req
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_ctlx_out) for cdc_ctlx_out
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_acm_in) for cdc_acm_in
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_acm_out) for cdc_acm_out
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    strtok.o(.text) refers to strtok_int.o(.text) for __strtok_internal
    strtok.o(.text) refers to strtok.o(.data) for .data
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    strtok_int.o(.text) refers to strspn.o(.text) for strspn
    strtok_int.o(.text) refers to strcspn.o(.text) for strcspn
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000024) refers (Weak) to initio.o(.text) for _initio
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    initio.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio.o(.text) refers to fopen.o(.text) for freopen
    initio.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio.o(.text) refers to h1_free.o(.text) for free
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    initio_locked.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio_locked.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio_locked.o(.text) refers to fopen.o(.text) for freopen
    initio_locked.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio_locked.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio_locked.o(.text) refers to h1_free.o(.text) for free
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_io.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.text) refers to strlen.o(.text) for strlen
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_gd32f450_470.o(.text) for __user_initial_stackheap
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    streamlock.o(.data) refers (Special) to initio.o(.text) for _initio
    fopen.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen.o(.text) refers to fseek.o(.text) for _fseek
    fopen.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fclose.o(.text) refers to stdio.o(.text) for _fflush
    fclose.o(.text) refers to sys_io.o(.text) for _sys_close
    fclose.o(.text) refers to h1_free.o(.text) for free
    fclose.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen_locked.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen_locked.o(.text) refers to fseek.o(.text) for _fseek
    fopen_locked.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fopen_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtred_outer.o(.text) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig_rtred_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtred_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) refers (Weak) to initio.o(.text) for _terminateio
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    fseek.o(.text) refers to sys_io.o(.text) for _sys_istty
    fseek.o(.text) refers to ftell.o(.text) for _ftell_internal
    fseek.o(.text) refers to stdio.o(.text) for _seterr
    stdio.o(.text) refers to sys_io.o(.text) for _sys_seek
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    ftell.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    defsig.o(CL$$defsig) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_adc.o(i.adc_calibration_enable), (42 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_length_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_clock_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_data_alignment_config), (22 bytes).
    Removing gd32f4xx_adc.o(i.adc_deinit), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_enable), (18 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_config), (52 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_source_config), (48 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_channel_config), (172 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_special_function_config), (90 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_mode_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_regular_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_group_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (286 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_usbsof_signal_select), (28 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_disable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (88 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (68 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_bit_width_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (52 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_subperipheral_select), (38 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_deinit), (164 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_get), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_flow_controller_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_config), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_mode_init), (356 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_mode_init), (344 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_para_struct_init), (34 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_get), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (104 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (280 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (104 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ack_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_clock_config), (228 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_deinit), (88 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_clear), (40 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_get), (30 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_master_addressing), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_mode_addr_config), (28 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (56 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_current_time_get), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_bus_mode_set), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_config), (52 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_index_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_response_config), (56 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_config), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_read), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_transfer_config), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_write), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_deinit), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_set), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_response_get), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_wait_type_set), (28 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.qspi_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_disable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_enable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_receive_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_enable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_stop_bit_set), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_word_length_set), (16 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (56 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(.rrx_text), (6 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_it.o(.rrx_text), (6 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.RAM_D3), (1680 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing 25lc080a.o(.rev16_text), (4 bytes).
    Removing 25lc080a.o(.revsh_text), (4 bytes).
    Removing 25lc080a.o(.rrx_text), (6 bytes).
    Removing 25lc080a.o(i.EEPROM_WritePage), (730 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_RedeByte), (72 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_RedePage), (680 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_WriteByte), (556 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_WritePage), (44 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.ADC0_Init), (164 bytes).
    Removing adc.o(i.ADC1_Init), (164 bytes).
    Removing adc.o(i.ADC2_Init), (164 bytes).
    Removing adc.o(.RAM_D1), (2400 bytes).
    Removing adc.o(.RAM_D3), (4800 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.rrx_text), (6 bytes).
    Removing dac.o(i.DAC1_Set_Vol), (144 bytes).
    Removing dac.o(i.DAC2_Set_Vol), (152 bytes).
    Removing dac.o(i.DAC_Init), (96 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing dma.o(i.DMA1_CH0_Init), (140 bytes).
    Removing dma.o(i.DMA1_CH1_Init), (140 bytes).
    Removing dma.o(i.DMA1_CH2_Init), (140 bytes).
    Removing eeprom_spi.o(.rev16_text), (4 bytes).
    Removing eeprom_spi.o(.revsh_text), (4 bytes).
    Removing eeprom_spi.o(.rrx_text), (6 bytes).
    Removing eeprom_spi.o(i.EEPROM_ReadStatusRegister), (40 bytes).
    Removing eeprom_spi.o(i.EEPROM_SendByte), (68 bytes).
    Removing eeprom_spi.o(i.EEPROM_WriteStatusRegister), (60 bytes).
    Removing esp32_wifi.o(.rev16_text), (4 bytes).
    Removing esp32_wifi.o(.revsh_text), (4 bytes).
    Removing esp32_wifi.o(.rrx_text), (6 bytes).
    Removing esp32_wifi.o(i.AddCmdTaskToList), (122 bytes).
    Removing esp32_wifi.o(i.CheckCmdRepeatInList), (152 bytes).
    Removing esp32_wifi.o(i.ConversionSignalIntensity), (170 bytes).
    Removing esp32_wifi.o(i.DeletCurrCmdTaskFromList), (76 bytes).
    Removing esp32_wifi.o(i.ESP32BLE_SendData_Add), (148 bytes).
    Removing esp32_wifi.o(i.ESP32BLE_SendData_Read), (100 bytes).
    Removing esp32_wifi.o(i.ESP32_Heart_Beat), (124 bytes).
    Removing esp32_wifi.o(i.ESP32_SendData_Add), (160 bytes).
    Removing esp32_wifi.o(i.ESP32_SendData_Add_Plus), (172 bytes).
    Removing esp32_wifi.o(i.ESP32_SendData_Read), (106 bytes).
    Removing esp32_wifi.o(i.ESP32_SendData_Read_Plus), (116 bytes).
    Removing esp32_wifi.o(i.ESP_ATE0), (48 bytes).
    Removing esp32_wifi.o(i.ESP_ATE1), (48 bytes).
    Removing esp32_wifi.o(i.ESP_AT_TEST), (44 bytes).
    Removing esp32_wifi.o(i.ESP_BLEADVDATA), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEADVPARAM), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEADVSTART), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEADVSTOP), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEGATTSSRVCRE), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEGATTSSRVSTART), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEINIT), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLENAME), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BuildNetConnect), (140 bytes).
    Removing esp32_wifi.o(i.ESP_CloseNetConnect), (64 bytes).
    Removing esp32_wifi.o(i.ESP_CloseWifiConnect), (48 bytes).
    Removing esp32_wifi.o(i.ESP_GetSNTP), (56 bytes).
    Removing esp32_wifi.o(i.ESP_GetSntpTimeInfo), (616 bytes).
    Removing esp32_wifi.o(i.ESP_GetTcpConnectStatus), (32 bytes).
    Removing esp32_wifi.o(i.ESP_GetWifiConnectStatus), (16 bytes).
    Removing esp32_wifi.o(i.ESP_Get_DomainIP), (48 bytes).
    Removing esp32_wifi.o(i.ESP_Init), (196 bytes).
    Removing esp32_wifi.o(i.ESP_OnReciveParseUsartData), (108 bytes).
    Removing esp32_wifi.o(i.ESP_QueryNetConnectStatus), (48 bytes).
    Removing esp32_wifi.o(i.ESP_QueryVersionInfo), (48 bytes).
    Removing esp32_wifi.o(i.ESP_QueryWifiInfo), (60 bytes).
    Removing esp32_wifi.o(i.ESP_QueryWifiInfoLists), (48 bytes).
    Removing esp32_wifi.o(i.ESP_QueryWorkMode), (48 bytes).
    Removing esp32_wifi.o(i.ESP_RESET), (48 bytes).
    Removing esp32_wifi.o(i.ESP_RESTORE), (48 bytes).
    Removing esp32_wifi.o(i.ESP_RFPOWER), (48 bytes).
    Removing esp32_wifi.o(i.ESP_RunTask), (32 bytes).
    Removing esp32_wifi.o(i.ESP_SET_FindApListRAM), (48 bytes).
    Removing esp32_wifi.o(i.ESP_SendBleData), (72 bytes).
    Removing esp32_wifi.o(i.ESP_SendData), (44 bytes).
    Removing esp32_wifi.o(i.ESP_SendNetData), (108 bytes).
    Removing esp32_wifi.o(i.ESP_SetAutoConn), (48 bytes).
    Removing esp32_wifi.o(i.ESP_SetNetDataRecv), (36 bytes).
    Removing esp32_wifi.o(i.ESP_SetSNTP_CFG), (48 bytes).
    Removing esp32_wifi.o(i.ESP_SetWifiConnect), (84 bytes).
    Removing esp32_wifi.o(i.ESP_SetWorkMode), (60 bytes).
    Removing esp32_wifi.o(i.ESP_StatusTask), (420 bytes).
    Removing esp32_wifi.o(i.ESP_XRFPOWER), (48 bytes).
    Removing esp32_wifi.o(i.InitBLESendDataRingBuf), (18 bytes).
    Removing esp32_wifi.o(i.InitCmdTaskToList), (32 bytes).
    Removing esp32_wifi.o(i.InitSendDataRingBuf), (18 bytes).
    Removing esp32_wifi.o(i.InitSendDataRingBufPlus), (18 bytes).
    Removing esp32_wifi.o(i.JudgeBLEConnectionStatus), (84 bytes).
    Removing esp32_wifi.o(i.JudgeNetConnectionStatus), (200 bytes).
    Removing esp32_wifi.o(i.JudgeWifiConnectionStatus), (104 bytes).
    Removing esp32_wifi.o(i.OnPackBLEADVDATA_Cmd), (104 bytes).
    Removing esp32_wifi.o(i.OnPackBLENAME_Cmd), (52 bytes).
    Removing esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd), (220 bytes).
    Removing esp32_wifi.o(i.OnPackCWModeCmd), (68 bytes).
    Removing esp32_wifi.o(i.OnPackJAPCmd), (92 bytes).
    Removing esp32_wifi.o(i.OnPackRFPOWERCmd), (96 bytes).
    Removing esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd), (164 bytes).
    Removing esp32_wifi.o(i.OnPackTCPOrUDPData), (10 bytes).
    Removing esp32_wifi.o(i.OnPackTCPOrUDPDataCmd), (88 bytes).
    Removing esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck), (228 bytes).
    Removing esp32_wifi.o(i.OnUnpackCWModeAck), (200 bytes).
    Removing esp32_wifi.o(i.OnUnpackDomainIPAck), (2 bytes).
    Removing esp32_wifi.o(i.OnUnpackGeneralAck), (156 bytes).
    Removing esp32_wifi.o(i.OnUnpackJAPAck), (364 bytes).
    Removing esp32_wifi.o(i.OnUnpackRSTAck), (164 bytes).
    Removing esp32_wifi.o(i.OnUnpackSntpTimeAck), (184 bytes).
    Removing esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck), (156 bytes).
    Removing esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck), (184 bytes).
    Removing esp32_wifi.o(i.OnUnpackVERSIONAck), (236 bytes).
    Removing esp32_wifi.o(i.OnUnpackyNetConnectStatusAck), (268 bytes).
    Removing esp32_wifi.o(i.ProcessAckOverTime), (76 bytes).
    Removing esp32_wifi.o(i.ProcessingBLEData), (76 bytes).
    Removing esp32_wifi.o(i.ProcessingNetworkData), (200 bytes).
    Removing esp32_wifi.o(i.ReadCmdInfoFromList), (52 bytes).
    Removing esp32_wifi.o(i.ReadCmdTaskCallFunFromList), (26 bytes).
    Removing esp32_wifi.o(i.SendCmdByTaskList), (236 bytes).
    Removing esp32_wifi.o(i.UserProcess_BLEADVSTARCmd), (64 bytes).
    Removing esp32_wifi.o(i.UserProcess_BLEADVSTOPCmd), (16 bytes).
    Removing esp32_wifi.o(i.UserProcess_JAPCmd), (24 bytes).
    Removing esp32_wifi.o(i.UserProcess_UpDataOkCmd), (20 bytes).
    Removing esp32_wifi.o(i.User_Netdata_process), (60 bytes).
    Removing esp32_wifi.o(i.XOnPackRFPOWERCmd), (32 bytes).
    Removing esp32_wifi.o(i.month_str2num), (60 bytes).
    Removing esp32_wifi.o(i.split), (82 bytes).
    Removing esp32_wifi.o(i.week_str2num), (60 bytes).
    Removing esp32_wifi.o(.RAM_D3), (36630 bytes).
    Removing esp32_wifi.o(.constdata), (8 bytes).
    Removing esp32_wifi.o(.conststring), (540 bytes).
    Removing esp32_wifi.o(.data), (960 bytes).
    Removing flash.o(.rev16_text), (4 bytes).
    Removing flash.o(.revsh_text), (4 bytes).
    Removing flash.o(.rrx_text), (6 bytes).
    Removing flash.o(i.FLASH_If_Init), (16 bytes).
    Removing flash.o(i.bsp_CmpCpuFlash), (68 bytes).
    Removing flash.o(i.bsp_EraseCpuFlash), (38 bytes).
    Removing flash.o(i.bsp_GetSector), (560 bytes).
    Removing flash.o(i.bsp_ReadCpuFlash), (44 bytes).
    Removing flash.o(i.bsp_ReadWordFlash), (6 bytes).
    Removing flash.o(i.bsp_WriteCpuFlash), (160 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing my_crc.o(.rev16_text), (4 bytes).
    Removing my_crc.o(.revsh_text), (4 bytes).
    Removing my_crc.o(.rrx_text), (6 bytes).
    Removing my_crc.o(i.CRC16_USB), (126 bytes).
    Removing my_crc.o(i.InvertUint16), (58 bytes).
    Removing my_crc.o(i.InvertUint8), (58 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(.rrx_text), (6 bytes).
    Removing rtc.o(i.rtc_register_set), (48 bytes).
    Removing rtc.o(.data), (2 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing time.o(.rev16_text), (4 bytes).
    Removing time.o(.revsh_text), (4 bytes).
    Removing time.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.ESP32_IO_Init), (36 bytes).
    Removing usart.o(i.FML_USART_Register), (80 bytes).
    Removing usart.o(i.InitBuffer), (46 bytes).
    Removing usart.o(i.UARTx_SendBuffer), (64 bytes).
    Removing usart.o(i.USART2_Init), (176 bytes).
    Removing usb.o(.rev16_text), (4 bytes).
    Removing usb.o(.revsh_text), (4 bytes).
    Removing usb.o(.rrx_text), (6 bytes).
    Removing usb.o(i.CDC_Command), (2 bytes).
    Removing usb.o(i.USB_RunTask), (2 bytes).
    Removing usb.o(.bss), (2444 bytes).
    Removing user_step.o(.rev16_text), (4 bytes).
    Removing user_step.o(.revsh_text), (4 bytes).
    Removing user_step.o(.rrx_text), (6 bytes).
    Removing user_step.o(i.ESP32BLE_Command_Add), (2 bytes).
    Removing user_step.o(i.ESP32BLE_Command_Read), (2 bytes).
    Removing user_step.o(i.ESP32_Command_Add), (2 bytes).
    Removing user_step.o(i.ESP32_Command_Add_Plus), (2 bytes).
    Removing user_step.o(i.ESP32_Command_Read), (2 bytes).
    Removing user_step.o(i.ESP32_Command_Read_Plus), (2 bytes).
    Removing user_step.o(i.Float2char), (2 bytes).
    Removing user_step.o(i.GetWifiInFo), (4 bytes).
    Removing user_step.o(i.Get_AlgorithmInFo), (4 bytes).
    Removing user_step.o(i.Get_DevID), (4 bytes).
    Removing user_step.o(i.Get_HardVer), (4 bytes).
    Removing user_step.o(i.Get_SeverInfo), (2 bytes).
    Removing user_step.o(i.Get_zhcdInFo), (4 bytes).
    Removing user_step.o(i.IPD_Command), (2 bytes).
    Removing user_step.o(i.SetWifiInFo), (4 bytes).
    Removing user_step.o(i.Set_AlgorithmInFo), (4 bytes).
    Removing user_step.o(i.Set_DevID), (4 bytes).
    Removing user_step.o(i.Set_SeverInfo), (6 bytes).
    Removing user_step.o(i.Sever_Buf2Info), (2 bytes).
    Removing user_step.o(i.Sever_Info2Buf), (2 bytes).
    Removing user_step.o(i.SysReset_Condition), (56 bytes).
    Removing user_step.o(i.User_Init), (2 bytes).
    Removing user_step.o(.NoInit), (4 bytes).
    Removing user_step.o(.bss), (9450 bytes).
    Removing user_step.o(.constdata), (5 bytes).
    Removing gd32f470v_start.o(.rev16_text), (4 bytes).
    Removing gd32f470v_start.o(.revsh_text), (4 bytes).
    Removing gd32f470v_start.o(.rrx_text), (6 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_init), (68 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_off), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_on), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_key_init), (128 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_init), (84 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_off), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_on), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_toggle), (24 bytes).
    Removing api_lan_data_process .o(.rev16_text), (4 bytes).
    Removing api_lan_data_process .o(.revsh_text), (4 bytes).
    Removing api_lan_data_process .o(.rrx_text), (6 bytes).
    Removing api_tnrg.o(.rev16_text), (4 bytes).
    Removing api_tnrg.o(.revsh_text), (4 bytes).
    Removing api_tnrg.o(.rrx_text), (6 bytes).
    Removing api_w5500.o(.rev16_text), (4 bytes).
    Removing api_w5500.o(.revsh_text), (4 bytes).
    Removing api_w5500.o(.rrx_text), (6 bytes).
    Removing api_w5500.o(i.API_SPI_SwapByte), (56 bytes).
    Removing api_w5500.o(i.API_W5500_Print_Status), (688 bytes).
    Removing drv_usb_core.o(.rev16_text), (4 bytes).
    Removing drv_usb_core.o(.revsh_text), (4 bytes).
    Removing drv_usb_core.o(.rrx_text), (6 bytes).
    Removing drv_usb_core.o(i.usb_basic_init), (244 bytes).
    Removing drv_usb_core.o(i.usb_core_init), (184 bytes).
    Removing drv_usb_core.o(i.usb_core_reset), (38 bytes).
    Removing drv_usb_core.o(i.usb_curmode_set), (46 bytes).
    Removing drv_usb_core.o(i.usb_rxfifo_flush), (34 bytes).
    Removing drv_usb_core.o(i.usb_set_txfifo), (72 bytes).
    Removing drv_usb_dev.o(.rev16_text), (4 bytes).
    Removing drv_usb_dev.o(.revsh_text), (4 bytes).
    Removing drv_usb_dev.o(.rrx_text), (6 bytes).
    Removing drv_usb_dev.o(i.usb_dev_stop), (84 bytes).
    Removing drv_usb_dev.o(i.usb_dev_suspend), (50 bytes).
    Removing drv_usb_dev.o(i.usb_devcore_init), (336 bytes).
    Removing drv_usb_dev.o(i.usb_devint_enable), (64 bytes).
    Removing drv_usb_dev.o(i.usb_rwkup_active), (72 bytes).
    Removing drv_usb_dev.o(i.usb_transc0_active), (92 bytes).
    Removing drv_usb_dev.o(i.usb_transc_deactivate), (100 bytes).
    Removing drv_usb_dev.o(.data), (8 bytes).
    Removing drv_usbd_int.o(.rev16_text), (4 bytes).
    Removing drv_usbd_int.o(.revsh_text), (4 bytes).
    Removing drv_usbd_int.o(.rrx_text), (6 bytes).
    Removing usbd_core.o(.rev16_text), (4 bytes).
    Removing usbd_core.o(.revsh_text), (4 bytes).
    Removing usbd_core.o(.rrx_text), (6 bytes).
    Removing usbd_core.o(i.usbd_connect), (28 bytes).
    Removing usbd_core.o(i.usbd_disconnect), (28 bytes).
    Removing usbd_core.o(i.usbd_ep_clear), (52 bytes).
    Removing usbd_core.o(i.usbd_ep_setup), (112 bytes).
    Removing usbd_core.o(i.usbd_fifo_flush), (36 bytes).
    Removing usbd_core.o(i.usbd_init), (124 bytes).
    Removing usbd_core.o(.constdata), (16 bytes).
    Removing usbd_enum.o(.rev16_text), (4 bytes).
    Removing usbd_enum.o(.revsh_text), (4 bytes).
    Removing usbd_enum.o(.rrx_text), (6 bytes).
    Removing usbd_enum.o(i.int_to_unicode), (60 bytes).
    Removing usbd_enum.o(i.serial_string_get), (76 bytes).
    Removing usbd_transc.o(.rev16_text), (4 bytes).
    Removing usbd_transc.o(.revsh_text), (4 bytes).
    Removing usbd_transc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_hw.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_hw.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_hw.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_hw.o(i.usb_gpio_config), (64 bytes).
    Removing gd32f4xx_hw.o(i.usb_intr_config), (22 bytes).
    Removing gd32f4xx_hw.o(i.usb_mdelay), (14 bytes).
    Removing gd32f4xx_hw.o(i.usb_rcu_config), (24 bytes).
    Removing gd32f4xx_hw.o(i.usb_timer_init), (30 bytes).
    Removing cdc_acm_core.o(.rev16_text), (4 bytes).
    Removing cdc_acm_core.o(.revsh_text), (4 bytes).
    Removing cdc_acm_core.o(.rrx_text), (6 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_check_ready), (34 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_data_receive), (28 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_data_send), (34 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_deinit), (34 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_in), (70 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_init), (96 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_out), (32 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_req), (176 bytes).
    Removing cdc_acm_core.o(i.cdc_ctlx_out), (82 bytes).
    Removing cdc_acm_core.o(.constdata), (375 bytes).
    Removing cdc_acm_core.o(.data), (196 bytes).

1180 unused section(s) (total 129074 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_io.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ftell.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio_streams.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  streamlock.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fclose.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fseek.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok_int.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strspn.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcspn.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    Firmware\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s 0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    Firmware\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    Firmware\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    USBFS-object\\cdc_acm_core.c             0x00000000   Number         0  cdc_acm_core.o ABSOLUTE
    USBFS-object\\drv_usb_core.c             0x00000000   Number         0  drv_usb_core.o ABSOLUTE
    USBFS-object\\drv_usb_dev.c              0x00000000   Number         0  drv_usb_dev.o ABSOLUTE
    USBFS-object\\drv_usbd_int.c             0x00000000   Number         0  drv_usbd_int.o ABSOLUTE
    USBFS-object\\gd32f4xx_hw.c              0x00000000   Number         0  gd32f4xx_hw.o ABSOLUTE
    USBFS-object\\usbd_core.c                0x00000000   Number         0  usbd_core.o ABSOLUTE
    USBFS-object\\usbd_enum.c                0x00000000   Number         0  usbd_enum.o ABSOLUTE
    USBFS-object\\usbd_transc.c              0x00000000   Number         0  usbd_transc.o ABSOLUTE
    USBFS-object\cdc_acm_core.c              0x00000000   Number         0  cdc_acm_core.o ABSOLUTE
    USBFS-object\drv_usb_core.c              0x00000000   Number         0  drv_usb_core.o ABSOLUTE
    USBFS-object\drv_usb_dev.c               0x00000000   Number         0  drv_usb_dev.o ABSOLUTE
    USBFS-object\drv_usbd_int.c              0x00000000   Number         0  drv_usbd_int.o ABSOLUTE
    USBFS-object\gd32f4xx_hw.c               0x00000000   Number         0  gd32f4xx_hw.o ABSOLUTE
    USBFS-object\usbd_core.c                 0x00000000   Number         0  usbd_core.o ABSOLUTE
    USBFS-object\usbd_enum.c                 0x00000000   Number         0  usbd_enum.o ABSOLUTE
    USBFS-object\usbd_transc.c               0x00000000   Number         0  usbd_transc.o ABSOLUTE
    USER\\gd32f4xx_it.c                      0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    USER\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    USER\\systick.c                          0x00000000   Number         0  systick.o ABSOLUTE
    USER\gd32f4xx_it.c                       0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    USER\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    USER\systick.c                           0x00000000   Number         0  systick.o ABSOLUTE
    Utilities\25LC080A.c                     0x00000000   Number         0  25lc080a.o ABSOLUTE
    Utilities\API_LAN_DATA_Process .c        0x00000000   Number         0  api_lan_data_process .o ABSOLUTE
    Utilities\API_TNRG.c                     0x00000000   Number         0  api_tnrg.o ABSOLUTE
    Utilities\API_W5500.c                    0x00000000   Number         0  api_w5500.o ABSOLUTE
    Utilities\My_CRC.c                       0x00000000   Number         0  my_crc.o ABSOLUTE
    Utilities\\25LC080A.c                    0x00000000   Number         0  25lc080a.o ABSOLUTE
    Utilities\\API_LAN_DATA_Process .c       0x00000000   Number         0  api_lan_data_process .o ABSOLUTE
    Utilities\\API_TNRG.c                    0x00000000   Number         0  api_tnrg.o ABSOLUTE
    Utilities\\API_W5500.c                   0x00000000   Number         0  api_w5500.o ABSOLUTE
    Utilities\\My_CRC.c                      0x00000000   Number         0  my_crc.o ABSOLUTE
    Utilities\\adc.c                         0x00000000   Number         0  adc.o ABSOLUTE
    Utilities\\dac.c                         0x00000000   Number         0  dac.o ABSOLUTE
    Utilities\\dma.c                         0x00000000   Number         0  dma.o ABSOLUTE
    Utilities\\eeprom_spi.c                  0x00000000   Number         0  eeprom_spi.o ABSOLUTE
    Utilities\\esp32_wifi.c                  0x00000000   Number         0  esp32_wifi.o ABSOLUTE
    Utilities\\flash.c                       0x00000000   Number         0  flash.o ABSOLUTE
    Utilities\\gd32f470v_start.c             0x00000000   Number         0  gd32f470v_start.o ABSOLUTE
    Utilities\\gpio.c                        0x00000000   Number         0  gpio.o ABSOLUTE
    Utilities\\rtc.c                         0x00000000   Number         0  rtc.o ABSOLUTE
    Utilities\\spi.c                         0x00000000   Number         0  spi.o ABSOLUTE
    Utilities\\time.c                        0x00000000   Number         0  time.o ABSOLUTE
    Utilities\\usart.c                       0x00000000   Number         0  usart.o ABSOLUTE
    Utilities\\usb.c                         0x00000000   Number         0  usb.o ABSOLUTE
    Utilities\\user_step.c                   0x00000000   Number         0  user_step.o ABSOLUTE
    Utilities\adc.c                          0x00000000   Number         0  adc.o ABSOLUTE
    Utilities\dac.c                          0x00000000   Number         0  dac.o ABSOLUTE
    Utilities\dma.c                          0x00000000   Number         0  dma.o ABSOLUTE
    Utilities\eeprom_spi.c                   0x00000000   Number         0  eeprom_spi.o ABSOLUTE
    Utilities\esp32_wifi.c                   0x00000000   Number         0  esp32_wifi.o ABSOLUTE
    Utilities\flash.c                        0x00000000   Number         0  flash.o ABSOLUTE
    Utilities\gd32f470v_start.c              0x00000000   Number         0  gd32f470v_start.o ABSOLUTE
    Utilities\gpio.c                         0x00000000   Number         0  gpio.o ABSOLUTE
    Utilities\rtc.c                          0x00000000   Number         0  rtc.o ABSOLUTE
    Utilities\spi.c                          0x00000000   Number         0  spi.o ABSOLUTE
    Utilities\time.c                         0x00000000   Number         0  time.o ABSOLUTE
    Utilities\usart.c                        0x00000000   Number         0  usart.o ABSOLUTE
    Utilities\usb.c                          0x00000000   Number         0  usb.o ABSOLUTE
    Utilities\user_step.c                    0x00000000   Number         0  user_step.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    !!!main                                  0x080001ac   Section        8  __main.o(!!!main)
    !!!scatter                               0x080001b4   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001e8   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_copy                           0x08000244   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000260   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800027c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x0800027c   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000282   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000288   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000014  0x0800028e   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x08000294   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000298   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800029a   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800029e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000005          0x0800029e   Section        8  libinit2.o(.ARM.Collect$$libinit$$00000005)
    .ARM.Collect$$libinit$$0000000A          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000024          0x080002a6   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000024)
    .ARM.Collect$$libinit$$00000025          0x080002aa   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002aa   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002aa   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002aa   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002aa   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002aa   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002ac   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002ae   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002ae   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000005      0x080002ae   Section        4  libshutdown2.o(.ARM.Collect$$libshutdown$$00000005)
    .ARM.Collect$$libshutdown$$00000006      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080002b2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080002b4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002b4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002b4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002ba   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002ba   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002be   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002be   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002c6   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002c8   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002c8   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002cc   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x080002d4   Section        0  maybetermalloc1.o(.emb_text)
    .text                                    0x080002d4   Section       64  startup_gd32f450_470.o(.text)
    $v0                                      0x080002d4   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x08000314   Section        0  h1_alloc.o(.text)
    .text                                    0x08000372   Section        0  h1_free.o(.text)
    .text                                    0x080003c0   Section        0  noretval__2printf.o(.text)
    .text                                    0x080003d8   Section        0  _printf_pad.o(.text)
    .text                                    0x08000426   Section        0  _printf_str.o(.text)
    .text                                    0x08000478   Section        0  _printf_dec.o(.text)
    .text                                    0x080004f0   Section        0  _printf_hex_int.o(.text)
    .text                                    0x08000548   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x080006d0   Section        0  strlen.o(.text)
    .text                                    0x0800070e   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08000798   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080007fc   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800084a   Section        0  heapauxi.o(.text)
    .text                                    0x08000850   Section        8  rt_heap_descriptor_intlibspace.o(.text)
    .text                                    0x08000858   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000860   Section        0  hguard.o(.text)
    .text                                    0x08000864   Section        0  init_alloc.o(.text)
    .text                                    0x080008ee   Section        0  h1_init.o(.text)
    .text                                    0x080008fc   Section        0  _printf_intcommon.o(.text)
    .text                                    0x080009b0   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x080009b1   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x080009e0   Section        0  _printf_char.o(.text)
    .text                                    0x08000a0c   Section        0  _printf_char_file.o(.text)
    .text                                    0x08000a30   Section        8  libspace.o(.text)
    .text                                    0x08000a38   Section        0  h1_extend.o(.text)
    .text                                    0x08000a6c   Section        0  ferror.o(.text)
    .text                                    0x08000a74   Section        0  initio.o(.text)
    .text                                    0x08000bac   Section        0  defsig_rtmem_outer.o(.text)
    .text                                    0x08000bba   Section        0  sys_io.o(.text)
    .text                                    0x08000c20   Section        2  use_no_semi.o(.text)
    .text                                    0x08000c22   Section        0  indicate_semi.o(.text)
    .text                                    0x08000c22   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000c6c   Section        0  setvbuf.o(.text)
    .text                                    0x08000cb4   Section        0  fopen.o(.text)
    _freopen_locked                          0x08000cb5   Thumb Code     0  fopen.o(.text)
    .text                                    0x08000da0   Section        0  fclose.o(.text)
    .text                                    0x08000dec   Section        0  exit.o(.text)
    .text                                    0x08000dfe   Section        0  defsig_exit.o(.text)
    .text                                    0x08000e08   Section        0  defsig_rtred_outer.o(.text)
    .text                                    0x08000e18   Section        0  defsig_rtmem_inner.o(.text)
    .text                                    0x08000e68   Section        0  sys_exit.o(.text)
    .text                                    0x08000e74   Section        0  fseek.o(.text)
    .text                                    0x08000f6c   Section        0  stdio.o(.text)
    .text                                    0x0800105c   Section        0  defsig_general.o(.text)
    .text                                    0x08001090   Section        0  defsig_rtred_inner.o(.text)
    .text                                    0x080010c4   Section        0  sys_wrch.o(.text)
    .text                                    0x080010d2   Section        0  ftell.o(.text)
    i.ADC_ConvCpltCallback                   0x08001114   Section        0  adc.o(i.ADC_ConvCpltCallback)
    i.ADC_ConvHalfCpltCallback               0x080011b4   Section        0  adc.o(i.ADC_ConvHalfCpltCallback)
    i.API_Chose_TS5A3359_GAIN                0x08001254   Section        0  gpio.o(i.API_Chose_TS5A3359_GAIN)
    i.API_Detect_Gateway                     0x080012bc   Section        0  api_w5500.o(i.API_Detect_Gateway)
    i.API_Init_LAN                           0x080013c8   Section        0  api_w5500.o(i.API_Init_LAN)
    i.API_Init_Net_Parameters                0x080016c0   Section        0  api_w5500.o(i.API_Init_Net_Parameters)
    i.API_Printf_Hex                         0x0800194c   Section        0  usart.o(i.API_Printf_Hex)
    i.API_Process_Socket_Data                0x0800197c   Section        0  api_w5500.o(i.API_Process_Socket_Data)
    i.API_RNG_Init                           0x08001a7c   Section        0  api_tnrg.o(i.API_RNG_Init)
    i.API_Read_SOCK_Data_Buffer              0x08001bec   Section        0  api_w5500.o(i.API_Read_SOCK_Data_Buffer)
    i.API_Read_W5500_1Byte                   0x08001ce0   Section        0  api_w5500.o(i.API_Read_W5500_1Byte)
    i.API_Read_W5500_SOCK_1Byte              0x08001d10   Section        0  api_w5500.o(i.API_Read_W5500_SOCK_1Byte)
    i.API_Read_W5500_SOCK_2Byte              0x08001d4c   Section        0  api_w5500.o(i.API_Read_W5500_SOCK_2Byte)
    i.API_SPI0_Read_Byte                     0x08001d98   Section        0  api_w5500.o(i.API_SPI0_Read_Byte)
    i.API_SPI0_Send_Byte                     0x08001dcc   Section        0  api_w5500.o(i.API_SPI0_Send_Byte)
    i.API_SPI0_Send_Short                    0x08001e00   Section        0  api_w5500.o(i.API_SPI0_Send_Short)
    i.API_Socket_Connect                     0x08001e1c   Section        0  api_w5500.o(i.API_Socket_Connect)
    i.API_Socket_Init                        0x08001e64   Section        0  api_w5500.o(i.API_Socket_Init)
    i.API_Socket_Listen                      0x08001f78   Section        0  api_w5500.o(i.API_Socket_Listen)
    i.API_Socket_UDP                         0x08001fde   Section        0  api_w5500.o(i.API_Socket_UDP)
    i.API_W5500_1MS_RunTask                  0x0800201c   Section        0  api_w5500.o(i.API_W5500_1MS_RunTask)
    i.API_W5500_Check_Connection             0x08002078   Section        0  api_w5500.o(i.API_W5500_Check_Connection)
    i.API_W5500_GPIO_Init                    0x080020fc   Section        0  api_w5500.o(i.API_W5500_GPIO_Init)
    i.API_W5500_HardWare_Rest                0x08002124   Section        0  api_w5500.o(i.API_W5500_HardWare_Rest)
    i.API_W5500_Interrupt_Process            0x080021bc   Section        0  api_w5500.o(i.API_W5500_Interrupt_Process)
    i.API_W5500_ReciveDATA_Handle            0x0800231c   Section        0  api_w5500.o(i.API_W5500_ReciveDATA_Handle)
    i.API_W5500_Register_Init                0x08002408   Section        0  api_w5500.o(i.API_W5500_Register_Init)
    i.API_W5500_SPI0_Init                    0x08002480   Section        0  api_w5500.o(i.API_W5500_SPI0_Init)
    i.API_W5500_Send_Data_S0                 0x08002548   Section        0  api_w5500.o(i.API_W5500_Send_Data_S0)
    i.API_W5500_Socket_Set                   0x0800264c   Section        0  api_w5500.o(i.API_W5500_Socket_Set)
    i.API_Write_SOCK_Data_Buffer             0x080027f8   Section        0  api_w5500.o(i.API_Write_SOCK_Data_Buffer)
    i.API_Write_W5500_1Byte                  0x08002914   Section        0  api_w5500.o(i.API_Write_W5500_1Byte)
    i.API_Write_W5500_2Byte                  0x08002944   Section        0  api_w5500.o(i.API_Write_W5500_2Byte)
    i.API_Write_W5500_SOCK_1Byte             0x08002974   Section        0  api_w5500.o(i.API_Write_W5500_SOCK_1Byte)
    i.API_Write_W5500_SOCK_2Byte             0x080029b0   Section        0  api_w5500.o(i.API_Write_W5500_SOCK_2Byte)
    i.API_Write_W5500_SOCK_4Byte             0x080029ec   Section        0  api_w5500.o(i.API_Write_W5500_SOCK_4Byte)
    i.API_Write_W5500_nByte                  0x08002a40   Section        0  api_w5500.o(i.API_Write_W5500_nByte)
    i.AddByteToBuffer                        0x08002a84   Section        0  usart.o(i.AddByteToBuffer)
    AddByteToBuffer                          0x08002a85   Thumb Code   110  usart.o(i.AddByteToBuffer)
    i.BusFault_Handler                       0x08002af2   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Channel0_IRQHandler               0x08002af8   Section        0  gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler)
    i.DMA1_Channel1_IRQHandler               0x08002b2c   Section        0  gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA1_Channel2_IRQHandler               0x08002b60   Section        0  gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler)
    i.DRV_SPI_SwapByte                       0x08002b94   Section        0  spi.o(i.DRV_SPI_SwapByte)
    i.DebugMon_Handler                       0x08002bcc   Section        0  gd32f4xx_it.o(i.DebugMon_Handler)
    i.EEPROM_SPI_ReadBuffer                  0x08002bd0   Section        0  eeprom_spi.o(i.EEPROM_SPI_ReadBuffer)
    i.EEPROM_SPI_SendInstruction             0x08002c3c   Section        0  eeprom_spi.o(i.EEPROM_SPI_SendInstruction)
    i.EEPROM_SPI_WaitStandbyState            0x08002c70   Section        0  eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState)
    i.EEPROM_SPI_WriteBuffer                 0x08002cbc   Section        0  eeprom_spi.o(i.EEPROM_SPI_WriteBuffer)
    i.EEPROM_SPI_WritePage                   0x08002e48   Section        0  eeprom_spi.o(i.EEPROM_SPI_WritePage)
    i.EEPROM_WriteDisable                    0x08002efc   Section        0  eeprom_spi.o(i.EEPROM_WriteDisable)
    i.EEPROM_WriteEnable                     0x08002f2c   Section        0  eeprom_spi.o(i.EEPROM_WriteEnable)
    i.EXTI10_15_IRQHandler                   0x08002f5c   Section        0  gd32f4xx_it.o(i.EXTI10_15_IRQHandler)
    i.FML_USART_RecvTask                     0x08002f7c   Section        0  usart.o(i.FML_USART_RecvTask)
    i.GPIO_Init                              0x08003034   Section        0  gpio.o(i.GPIO_Init)
    i.HardFault_Handler                      0x08003074   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.Init_GPIO_TS5A339                      0x08003078   Section        0  gpio.o(i.Init_GPIO_TS5A339)
    i.MemManage_Handler                      0x080030b0   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080030b4   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x080030b6   Section        0  gd32f4xx_it.o(i.PendSV_Handler)
    i.RTC_Init                               0x080030b8   Section        0  rtc.o(i.RTC_Init)
    i.ReadBytesToBuffer                      0x0800311c   Section        0  usart.o(i.ReadBytesToBuffer)
    ReadBytesToBuffer                        0x0800311d   Thumb Code   142  usart.o(i.ReadBytesToBuffer)
    i.RecvDataHandler                        0x080031aa   Section        0  usart.o(i.RecvDataHandler)
    RecvDataHandler                          0x080031ab   Thumb Code    30  usart.o(i.RecvDataHandler)
    i.SPI1_Init                              0x080031c8   Section        0  spi.o(i.SPI1_Init)
    i.SVC_Handler                            0x08003264   Section        0  gd32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08003266   Section        0  gd32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08003270   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.TIMER1_Init                            0x08003338   Section        0  time.o(i.TIMER1_Init)
    i.TIMER2_IRQHandler                      0x080033ae   Section        0  gd32f4xx_it.o(i.TIMER2_IRQHandler)
    i.TIMER3_IRQHandler                      0x080033b8   Section        0  gd32f4xx_it.o(i.TIMER3_IRQHandler)
    i.TIMER3_Init                            0x080033d0   Section        0  time.o(i.TIMER3_Init)
    i.TIMER6_IRQHandler                      0x08003424   Section        0  gd32f4xx_it.o(i.TIMER6_IRQHandler)
    i.TIMER6_Init                            0x0800343c   Section        0  time.o(i.TIMER6_Init)
    i.TIM_PeriodElapsedCallback              0x08003490   Section        0  time.o(i.TIM_PeriodElapsedCallback)
    i.UART_IDLECallBack                      0x08003664   Section        0  usart.o(i.UART_IDLECallBack)
    i.UART_RxCpltCallback                    0x0800368c   Section        0  usart.o(i.UART_RxCpltCallback)
    i.USART0_Init                            0x080036b8   Section        0  usart.o(i.USART0_Init)
    i.USART2_IRQHandler                      0x08003740   Section        0  gd32f4xx_it.o(i.USART2_IRQHandler)
    i.USBFS_IRQHandler                       0x08003770   Section        0  gd32f4xx_it.o(i.USBFS_IRQHandler)
    i.USBFS_WKUP_IRQHandler                  0x08003780   Section        0  gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler)
    i.UsageFault_Handler                     0x080037b4   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x080037b8   Section        0  systick.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080037b9   Thumb Code    32  systick.o(i.__NVIC_SetPriority)
    i._is_digit                              0x080037e0   Section        0  __printf_wp.o(i._is_digit)
    i._usb_bos_desc_get                      0x080037ee   Section        0  usbd_enum.o(i._usb_bos_desc_get)
    _usb_bos_desc_get                        0x080037ef   Thumb Code    20  usbd_enum.o(i._usb_bos_desc_get)
    i._usb_config_desc_get                   0x08003802   Section        0  usbd_enum.o(i._usb_config_desc_get)
    _usb_config_desc_get                     0x08003803   Thumb Code    34  usbd_enum.o(i._usb_config_desc_get)
    i._usb_dev_desc_get                      0x08003824   Section        0  usbd_enum.o(i._usb_dev_desc_get)
    _usb_dev_desc_get                        0x08003825   Thumb Code    20  usbd_enum.o(i._usb_dev_desc_get)
    i._usb_std_clearfeature                  0x08003838   Section        0  usbd_enum.o(i._usb_std_clearfeature)
    _usb_std_clearfeature                    0x08003839   Thumb Code   116  usbd_enum.o(i._usb_std_clearfeature)
    i._usb_std_getconfiguration              0x080038ac   Section        0  usbd_enum.o(i._usb_std_getconfiguration)
    _usb_std_getconfiguration                0x080038ad   Thumb Code    64  usbd_enum.o(i._usb_std_getconfiguration)
    i._usb_std_getdescriptor                 0x080038ec   Section        0  usbd_enum.o(i._usb_std_getdescriptor)
    _usb_std_getdescriptor                   0x080038ed   Thumb Code   272  usbd_enum.o(i._usb_std_getdescriptor)
    i._usb_std_getinterface                  0x08003a00   Section        0  usbd_enum.o(i._usb_std_getinterface)
    _usb_std_getinterface                    0x08003a01   Thumb Code    60  usbd_enum.o(i._usb_std_getinterface)
    i._usb_std_getstatus                     0x08003a3c   Section        0  usbd_enum.o(i._usb_std_getstatus)
    _usb_std_getstatus                       0x08003a3d   Thumb Code   192  usbd_enum.o(i._usb_std_getstatus)
    i._usb_std_reserved                      0x08003b00   Section        0  usbd_enum.o(i._usb_std_reserved)
    _usb_std_reserved                        0x08003b01   Thumb Code     6  usbd_enum.o(i._usb_std_reserved)
    i._usb_std_setaddress                    0x08003b06   Section        0  usbd_enum.o(i._usb_std_setaddress)
    _usb_std_setaddress                      0x08003b07   Thumb Code    92  usbd_enum.o(i._usb_std_setaddress)
    i._usb_std_setconfiguration              0x08003b64   Section        0  usbd_enum.o(i._usb_std_setconfiguration)
    _usb_std_setconfiguration                0x08003b65   Thumb Code   176  usbd_enum.o(i._usb_std_setconfiguration)
    i._usb_std_setdescriptor                 0x08003c18   Section        0  usbd_enum.o(i._usb_std_setdescriptor)
    _usb_std_setdescriptor                   0x08003c19   Thumb Code     6  usbd_enum.o(i._usb_std_setdescriptor)
    i._usb_std_setfeature                    0x08003c1e   Section        0  usbd_enum.o(i._usb_std_setfeature)
    _usb_std_setfeature                      0x08003c1f   Thumb Code   104  usbd_enum.o(i._usb_std_setfeature)
    i._usb_std_setinterface                  0x08003c86   Section        0  usbd_enum.o(i._usb_std_setinterface)
    _usb_std_setinterface                    0x08003c87   Thumb Code    68  usbd_enum.o(i._usb_std_setinterface)
    i._usb_std_synchframe                    0x08003cca   Section        0  usbd_enum.o(i._usb_std_synchframe)
    _usb_std_synchframe                      0x08003ccb   Thumb Code     6  usbd_enum.o(i._usb_std_synchframe)
    i._usb_str_desc_get                      0x08003cd0   Section        0  usbd_enum.o(i._usb_str_desc_get)
    _usb_str_desc_get                        0x08003cd1   Thumb Code    20  usbd_enum.o(i._usb_str_desc_get)
    i.delay_1ms                              0x08003ce4   Section        0  systick.o(i.delay_1ms)
    i.delay_decrement                        0x08003cf8   Section        0  systick.o(i.delay_decrement)
    i.dma_interrupt_flag_clear               0x08003d10   Section        0  gd32f4xx_dma.o(i.dma_interrupt_flag_clear)
    i.dma_interrupt_flag_get                 0x08003d4e   Section        0  gd32f4xx_dma.o(i.dma_interrupt_flag_get)
    i.exti_interrupt_flag_clear              0x08003f54   Section        0  gd32f4xx_exti.o(i.exti_interrupt_flag_clear)
    i.exti_interrupt_flag_get                0x08003f60   Section        0  gd32f4xx_exti.o(i.exti_interrupt_flag_get)
    i.fputc                                  0x08003f84   Section        0  usart.o(i.fputc)
    i.gd_eval_key_state_get                  0x08003fa8   Section        0  gd32f470v_start.o(i.gd_eval_key_state_get)
    i.get_hard_rand_data                     0x08003fc8   Section        0  api_tnrg.o(i.get_hard_rand_data)
    i.gpio_af_set                            0x08003fdc   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x0800403a   Section        0  gd32f4xx_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x0800403e   Section        0  gd32f4xx_gpio.o(i.gpio_bit_set)
    i.gpio_input_bit_get                     0x08004042   Section        0  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x08004052   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x080040a0   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.hw_delay                               0x080040e4   Section        0  gd32f4xx_hw.o(i.hw_delay)
    hw_delay                                 0x080040e5   Thumb Code    34  gd32f4xx_hw.o(i.hw_delay)
    i.hw_time_set                            0x08004110   Section        0  gd32f4xx_hw.o(i.hw_time_set)
    hw_time_set                              0x08004111   Thumb Code   104  gd32f4xx_hw.o(i.hw_time_set)
    i.main                                   0x08004180   Section        0  main.o(i.main)
    i.nvic_irq_enable                        0x080043f8   Section        0  gd32f4xx_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_set                0x080044bc   Section        0  gd32f4xx_misc.o(i.nvic_priority_group_set)
    i.pmu_backup_write_enable                0x080044d0   Section        0  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    i.pmu_to_deepsleepmode                   0x080044e4   Section        0  gd32f4xx_pmu.o(i.pmu_to_deepsleepmode)
    i.rcu_ck48m_clock_config                 0x080045d8   Section        0  gd32f4xx_rcu.o(i.rcu_ck48m_clock_config)
    i.rcu_clock_freq_get                     0x080045f0   Section        0  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    i.rcu_flag_get                           0x08004714   Section        0  gd32f4xx_rcu.o(i.rcu_flag_get)
    i.rcu_osci_on                            0x08004738   Section        0  gd32f4xx_rcu.o(i.rcu_osci_on)
    i.rcu_osci_stab_wait                     0x0800475c   Section        0  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    i.rcu_periph_clock_enable                0x080048b8   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x080048dc   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x08004900   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.rcu_pll48m_clock_config                0x08004924   Section        0  gd32f4xx_rcu.o(i.rcu_pll48m_clock_config)
    i.rcu_rtc_clock_config                   0x0800493c   Section        0  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    i.rcu_system_clock_source_config         0x08004954   Section        0  gd32f4xx_rcu.o(i.rcu_system_clock_source_config)
    i.rcu_system_clock_source_get            0x0800496c   Section        0  gd32f4xx_rcu.o(i.rcu_system_clock_source_get)
    i.resume_mcu_clk                         0x0800497c   Section        0  gd32f4xx_it.o(i.resume_mcu_clk)
    resume_mcu_clk                           0x0800497d   Thumb Code    56  gd32f4xx_it.o(i.resume_mcu_clk)
    i.rtc_init                               0x080049b4   Section        0  gd32f4xx_rtc.o(i.rtc_init)
    i.rtc_init_mode_enter                    0x08004a78   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    i.rtc_init_mode_exit                     0x08004ac0   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    i.rtc_register_sync_wait                 0x08004ad4   Section        0  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    i.spi_enable                             0x08004b34   Section        0  gd32f4xx_spi.o(i.spi_enable)
    i.spi_i2s_data_receive                   0x08004b3e   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    i.spi_i2s_data_transmit                  0x08004b46   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    i.spi_i2s_flag_get                       0x08004b4a   Section        0  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    i.spi_init                               0x08004b5a   Section        0  gd32f4xx_spi.o(i.spi_init)
    i.system_clock_168m_25m_hxtal            0x08004b8c   Section        0  system_gd32f4xx.o(i.system_clock_168m_25m_hxtal)
    system_clock_168m_25m_hxtal              0x08004b8d   Thumb Code   240  system_gd32f4xx.o(i.system_clock_168m_25m_hxtal)
    i.system_clock_config                    0x08004c88   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x08004c89   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.systick_config                         0x08004c90   Section        0  systick.o(i.systick_config)
    i.timer_auto_reload_shadow_enable        0x08004ce0   Section        0  gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable)
    i.timer_channel_output_config            0x08004cec   Section        0  gd32f4xx_timer.o(i.timer_channel_output_config)
    i.timer_channel_output_mode_config       0x08004ed8   Section        0  gd32f4xx_timer.o(i.timer_channel_output_mode_config)
    i.timer_channel_output_pulse_value_config 0x08004f32   Section        0  gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config)
    i.timer_channel_output_shadow_config     0x08004f58   Section        0  gd32f4xx_timer.o(i.timer_channel_output_shadow_config)
    i.timer_deinit                           0x08004fb4   Section        0  gd32f4xx_timer.o(i.timer_deinit)
    i.timer_disable                          0x08005138   Section        0  gd32f4xx_timer.o(i.timer_disable)
    i.timer_enable                           0x08005142   Section        0  gd32f4xx_timer.o(i.timer_enable)
    i.timer_init                             0x0800514c   Section        0  gd32f4xx_timer.o(i.timer_init)
    i.timer_interrupt_disable                0x080051e4   Section        0  gd32f4xx_timer.o(i.timer_interrupt_disable)
    i.timer_interrupt_enable                 0x080051ec   Section        0  gd32f4xx_timer.o(i.timer_interrupt_enable)
    i.timer_interrupt_flag_clear             0x080051f4   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    i.timer_interrupt_flag_get               0x080051fa   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    i.trng_deinit                            0x08005212   Section        0  gd32f4xx_trng.o(i.trng_deinit)
    i.trng_enable                            0x08005228   Section        0  gd32f4xx_trng.o(i.trng_enable)
    i.trng_flag_get                          0x0800523c   Section        0  gd32f4xx_trng.o(i.trng_flag_get)
    i.trng_get_true_random_data              0x08005254   Section        0  gd32f4xx_trng.o(i.trng_get_true_random_data)
    i.usart_baudrate_set                     0x08005260   Section        0  gd32f4xx_usart.o(i.usart_baudrate_set)
    i.usart_data_receive                     0x08005348   Section        0  gd32f4xx_usart.o(i.usart_data_receive)
    i.usart_data_transmit                    0x08005352   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x0800535c   Section        0  gd32f4xx_usart.o(i.usart_deinit)
    i.usart_enable                           0x08005438   Section        0  gd32f4xx_usart.o(i.usart_enable)
    i.usart_flag_get                         0x08005442   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.usart_interrupt_flag_clear             0x08005460   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    i.usart_interrupt_flag_get               0x0800547a   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    i.usart_receive_config                   0x080054b2   Section        0  gd32f4xx_usart.o(i.usart_receive_config)
    i.usart_transmit_config                  0x080054c2   Section        0  gd32f4xx_usart.o(i.usart_transmit_config)
    i.usb_clock_active                       0x080054d2   Section        0  drv_usb_dev.o(i.usb_clock_active)
    i.usb_ctlep_startout                     0x080054f4   Section        0  drv_usb_dev.o(i.usb_ctlep_startout)
    i.usb_iepintr_read                       0x0800551c   Section        0  drv_usb_dev.o(i.usb_iepintr_read)
    i.usb_rxfifo_read                        0x08005546   Section        0  drv_usb_core.o(i.usb_rxfifo_read)
    i.usb_timer_irq                          0x08005564   Section        0  gd32f4xx_hw.o(i.usb_timer_irq)
    i.usb_transc_active                      0x0800559c   Section        0  drv_usb_dev.o(i.usb_transc_active)
    i.usb_transc_clrstall                    0x08005634   Section        0  drv_usb_dev.o(i.usb_transc_clrstall)
    i.usb_transc_inxfer                      0x08005678   Section        0  drv_usb_dev.o(i.usb_transc_inxfer)
    i.usb_transc_outxfer                     0x08005784   Section        0  drv_usb_dev.o(i.usb_transc_outxfer)
    i.usb_transc_stall                       0x08005814   Section        0  drv_usb_dev.o(i.usb_transc_stall)
    i.usb_txfifo_flush                       0x08005856   Section        0  drv_usb_core.o(i.usb_txfifo_flush)
    i.usb_txfifo_write                       0x0800587e   Section        0  drv_usb_core.o(i.usb_txfifo_write)
    i.usb_udelay                             0x080058a0   Section        0  gd32f4xx_hw.o(i.usb_udelay)
    i.usbd_class_request                     0x080058ae   Section        0  usbd_enum.o(i.usbd_class_request)
    i.usbd_ctl_recev                         0x080058d4   Section        0  usbd_transc.o(i.usbd_ctl_recev)
    i.usbd_ctl_send                          0x08005902   Section        0  usbd_transc.o(i.usbd_ctl_send)
    i.usbd_ctl_status_recev                  0x08005930   Section        0  usbd_transc.o(i.usbd_ctl_status_recev)
    i.usbd_ctl_status_send                   0x08005950   Section        0  usbd_transc.o(i.usbd_ctl_status_send)
    i.usbd_emptytxfifo_write                 0x08005970   Section        0  drv_usbd_int.o(i.usbd_emptytxfifo_write)
    usbd_emptytxfifo_write                   0x08005971   Thumb Code   140  drv_usbd_int.o(i.usbd_emptytxfifo_write)
    i.usbd_enum_error                        0x080059fc   Section        0  usbd_enum.o(i.usbd_enum_error)
    i.usbd_ep_recev                          0x08005a1a   Section        0  usbd_core.o(i.usbd_ep_recev)
    i.usbd_ep_send                           0x08005a56   Section        0  usbd_core.o(i.usbd_ep_send)
    i.usbd_ep_stall                          0x08005a92   Section        0  usbd_core.o(i.usbd_ep_stall)
    i.usbd_ep_stall_clear                    0x08005acc   Section        0  usbd_core.o(i.usbd_ep_stall_clear)
    i.usbd_in_transc                         0x08005b06   Section        0  usbd_transc.o(i.usbd_in_transc)
    i.usbd_int_enumfinish                    0x08005ba8   Section        0  drv_usbd_int.o(i.usbd_int_enumfinish)
    usbd_int_enumfinish                      0x08005ba9   Thumb Code    98  drv_usbd_int.o(i.usbd_int_enumfinish)
    i.usbd_int_epin                          0x08005c10   Section        0  drv_usbd_int.o(i.usbd_int_epin)
    usbd_int_epin                            0x08005c11   Thumb Code   138  drv_usbd_int.o(i.usbd_int_epin)
    i.usbd_int_epout                         0x08005c9a   Section        0  drv_usbd_int.o(i.usbd_int_epout)
    usbd_int_epout                           0x08005c9b   Thumb Code   206  drv_usbd_int.o(i.usbd_int_epout)
    i.usbd_int_reset                         0x08005d68   Section        0  drv_usbd_int.o(i.usbd_int_reset)
    usbd_int_reset                           0x08005d69   Thumb Code   208  drv_usbd_int.o(i.usbd_int_reset)
    i.usbd_int_rxfifo                        0x08005e40   Section        0  drv_usbd_int.o(i.usbd_int_rxfifo)
    usbd_int_rxfifo                          0x08005e41   Thumb Code   182  drv_usbd_int.o(i.usbd_int_rxfifo)
    i.usbd_int_suspend                       0x08005efc   Section        0  drv_usbd_int.o(i.usbd_int_suspend)
    usbd_int_suspend                         0x08005efd   Thumb Code   104  drv_usbd_int.o(i.usbd_int_suspend)
    i.usbd_isr                               0x08005f64   Section        0  drv_usbd_int.o(i.usbd_isr)
    i.usbd_out_transc                        0x08006042   Section        0  usbd_transc.o(i.usbd_out_transc)
    i.usbd_setup_transc                      0x080060c6   Section        0  usbd_transc.o(i.usbd_setup_transc)
    i.usbd_standard_request                  0x0800614c   Section        0  usbd_enum.o(i.usbd_standard_request)
    i.usbd_vendor_request                    0x08006168   Section        0  usbd_enum.o(i.usbd_vendor_request)
    x$fpl$fpinit                             0x0800616e   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800616e   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x08006178   Section        4  drv_usb_dev.o(.constdata)
    EP0_MAXLEN                               0x08006178   Data           4  drv_usb_dev.o(.constdata)
    .constdata                               0x0800617c   Section       60  drv_usbd_int.o(.constdata)
    USB_SPEED                                0x0800617c   Data           4  drv_usbd_int.o(.constdata)
    <Data1>                                  0x08006180   Data          28  drv_usbd_int.o(.constdata)
    <Data2>                                  0x0800619c   Data          28  drv_usbd_int.o(.constdata)
    .constdata                               0x080061b8   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x080061b8   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x080061cc   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x080061e0   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x080061e0   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x080061f1   Section        4  sys_io.o(.constdata)
    .constdata                               0x080061f5   Section        4  sys_io.o(.constdata)
    .constdata                               0x080061f9   Section        4  sys_io.o(.constdata)
    .data                                    0x20000004   Section        4  system_gd32f4xx.o(.data)
    .data                                    0x20000008   Section       15  main.o(.data)
    .data                                    0x20000018   Section        4  systick.o(.data)
    delay                                    0x20000018   Data           4  systick.o(.data)
    .data                                    0x2000001c   Section        1  adc.o(.data)
    .data                                    0x20000020   Section        6  time.o(.data)
    .data                                    0x20000026   Section        1  usb.o(.data)
    .data                                    0x20000028   Section       62  gd32f470v_start.o(.data)
    GPIO_PORT                                0x20000028   Data          12  gd32f470v_start.o(.data)
    GPIO_PIN                                 0x20000034   Data          12  gd32f470v_start.o(.data)
    GPIO_CLK                                 0x20000040   Data           6  gd32f470v_start.o(.data)
    KEY_PORT                                 0x20000048   Data           4  gd32f470v_start.o(.data)
    KEY_PIN                                  0x2000004c   Data           4  gd32f470v_start.o(.data)
    KEY_CLK                                  0x20000050   Data           2  gd32f470v_start.o(.data)
    KEY_EXTI_LINE                            0x20000054   Data           4  gd32f470v_start.o(.data)
    KEY_PORT_SOURCE                          0x20000058   Data           1  gd32f470v_start.o(.data)
    KEY_PIN_SOURCE                           0x20000059   Data           1  gd32f470v_start.o(.data)
    KEY_IRQn                                 0x2000005a   Data           1  gd32f470v_start.o(.data)
    BEEP_PORT                                0x2000005c   Data           4  gd32f470v_start.o(.data)
    BEEP_GPIO_PIN                            0x20000060   Data           4  gd32f470v_start.o(.data)
    BEEP_CLK                                 0x20000064   Data           2  gd32f470v_start.o(.data)
    .data                                    0x20000068   Section       18  api_w5500.o(.data)
    rx_cnt                                   0x20000068   Data           4  api_w5500.o(.data)
    Temp_Cnt                                 0x2000006c   Data           2  api_w5500.o(.data)
    debug_cnt                                0x20000070   Data           4  api_w5500.o(.data)
    send_cnt                                 0x20000074   Data           4  api_w5500.o(.data)
    reconnect_cnt                            0x20000078   Data           2  api_w5500.o(.data)
    .data                                    0x2000007c   Section       67  usbd_enum.o(.data)
    _std_dev_req                             0x2000007c   Data          52  usbd_enum.o(.data)
    std_desc_get                             0x200000b0   Data          12  usbd_enum.o(.data)
    status                                   0x200000bc   Data           2  usbd_enum.o(.data)
    config                                   0x200000be   Data           1  usbd_enum.o(.data)
    .data                                    0x200000c0   Section        8  gd32f4xx_hw.o(.data)
    .data                                    0x200000c8   Section        4  stdio_streams.o(.data)
    .data                                    0x200000cc   Section        4  stdio_streams.o(.data)
    .data                                    0x200000d0   Section        4  stdio_streams.o(.data)
    .bss                                     0x200000d4   Section       16  gd32f4xx_pmu.o(.bss)
    reg_snap                                 0x200000d4   Data          16  gd32f4xx_pmu.o(.bss)
    .bss                                     0x200000e4   Section       20  rtc.o(.bss)
    .bss                                     0x200000f8   Section       20  time.o(.bss)
    .bss                                     0x2000010c   Section     1172  cdc_acm_core.o(.bss)
    cdc_handler                              0x20000548   Data          88  cdc_acm_core.o(.bss)
    .bss                                     0x200005a0   Section       84  stdio_streams.o(.bss)
    .bss                                     0x200005f4   Section       84  stdio_streams.o(.bss)
    .bss                                     0x20000648   Section       84  stdio_streams.o(.bss)
    .bss                                     0x2000069c   Section       96  libspace.o(.bss)
    HEAP                                     0x20000700   Section    12288  startup_gd32f450_470.o(HEAP)
    Heap_Mem                                 0x20000700   Data       12288  startup_gd32f450_470.o(HEAP)
    STACK                                    0x20003700   Section    20480  startup_gd32f450_470.o(STACK)
    Stack_Mem                                0x20003700   Data       20480  startup_gd32f450_470.o(STACK)
    __initial_sp                             0x20008700   Data           0  startup_gd32f450_470.o(STACK)
    .RAM_D3                                  0x20020000   Section     6167  usart.o(.RAM_D3)
    .RAM_D3                                  0x20021818   Section     2208  api_w5500.o(.RAM_D3)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080001b5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001c3   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001e9   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001e9   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_copy                       0x08000245   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000261   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x0800027d   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x0800027d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_u                                0x08000283   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_x                                0x08000289   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_s                                0x0800028f   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x08000295   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000299   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800029b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_2                     0x0800029f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000005)
    __rt_lib_init_preinit_1                  0x0800029f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_atexit_1                   0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_fp_trap_1                  0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_rand_1                     0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_signal_1                   0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_2                    0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000024)
    __rt_lib_init_user_alloc_1               0x080002a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_alloca_1                   0x080002ab   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002ab   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_cpp_1                      0x080002ab   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002ab   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_return                     0x080002ab   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_stdio_1                    0x080002ab   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002ad   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002af   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080002af   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_stdio_2                0x080002af   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000005)
    __rt_lib_shutdown_fp_trap_1              0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080002b5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002b5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002b5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002c7   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002cd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002d5   Thumb Code     8  startup_gd32f450_470.o(.text)
    _maybe_terminate_alloc                   0x080002d5   Thumb Code     0  maybetermalloc1.o(.emb_text)
    ADC_IRQHandler                           0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_IRQHandler                          0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    SDIO_IRQHandler                          0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART0_IRQHandler                        0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART1_IRQHandler                        0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x080002ef   Thumb Code     0  startup_gd32f450_470.o(.text)
    __user_initial_stackheap                 0x080002f1   Thumb Code    10  startup_gd32f450_470.o(.text)
    malloc                                   0x08000315   Thumb Code    94  h1_alloc.o(.text)
    free                                     0x08000373   Thumb Code    78  h1_free.o(.text)
    __2printf                                0x080003c1   Thumb Code    20  noretval__2printf.o(.text)
    _printf_pre_padding                      0x080003d9   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000405   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x08000427   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000479   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x080004f1   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x080004f1   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x08000549   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strlen                                   0x080006d1   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy                           0x0800070f   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x0800070f   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000775   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x08000799   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000799   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000799   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080007e1   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x080007fd   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080007fd   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080007fd   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000801   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x0800084b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x0800084d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x0800084f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_descriptor                     0x08000851   Thumb Code     8  rt_heap_descriptor_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08000859   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000859   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000859   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __use_no_heap                            0x08000861   Thumb Code     2  hguard.o(.text)
    __heap$guard                             0x08000863   Thumb Code     2  hguard.o(.text)
    _terminate_user_alloc                    0x08000865   Thumb Code     2  init_alloc.o(.text)
    _init_user_alloc                         0x08000867   Thumb Code     2  init_alloc.o(.text)
    __Heap_Full                              0x08000869   Thumb Code    34  init_alloc.o(.text)
    __Heap_Broken                            0x0800088b   Thumb Code     6  init_alloc.o(.text)
    _init_alloc                              0x08000891   Thumb Code    94  init_alloc.o(.text)
    __Heap_Initialize                        0x080008ef   Thumb Code    10  h1_init.o(.text)
    __Heap_DescSize                          0x080008f9   Thumb Code     4  h1_init.o(.text)
    _printf_int_common                       0x080008fd   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_common                      0x080009bb   Thumb Code    32  _printf_char_common.o(.text)
    _printf_cs_common                        0x080009e1   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080009f5   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000a05   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08000a0d   Thumb Code    32  _printf_char_file.o(.text)
    __user_libspace                          0x08000a31   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000a31   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000a31   Thumb Code     0  libspace.o(.text)
    __Heap_ProvideMemory                     0x08000a39   Thumb Code    52  h1_extend.o(.text)
    ferror                                   0x08000a6d   Thumb Code     8  ferror.o(.text)
    _initio                                  0x08000a75   Thumb Code   210  initio.o(.text)
    _terminateio                             0x08000b47   Thumb Code    56  initio.o(.text)
    __rt_SIGRTMEM                            0x08000bad   Thumb Code    14  defsig_rtmem_outer.o(.text)
    _sys_open                                0x08000bbb   Thumb Code    20  sys_io.o(.text)
    _sys_close                               0x08000bcf   Thumb Code    12  sys_io.o(.text)
    _sys_write                               0x08000bdb   Thumb Code    16  sys_io.o(.text)
    _sys_read                                0x08000beb   Thumb Code    14  sys_io.o(.text)
    _sys_istty                               0x08000bf9   Thumb Code    12  sys_io.o(.text)
    _sys_seek                                0x08000c05   Thumb Code    14  sys_io.o(.text)
    _sys_ensure                              0x08000c13   Thumb Code     2  sys_io.o(.text)
    _sys_flen                                0x08000c15   Thumb Code    12  sys_io.o(.text)
    __I$use$semihosting                      0x08000c21   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000c21   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000c23   Thumb Code     0  indicate_semi.o(.text)
    __user_setup_stackheap                   0x08000c23   Thumb Code    74  sys_stackheap_outer.o(.text)
    setvbuf                                  0x08000c6d   Thumb Code    70  setvbuf.o(.text)
    freopen                                  0x08000cb5   Thumb Code   158  fopen.o(.text)
    fopen                                    0x08000d53   Thumb Code    74  fopen.o(.text)
    _fclose_internal                         0x08000da1   Thumb Code    76  fclose.o(.text)
    fclose                                   0x08000da1   Thumb Code     0  fclose.o(.text)
    exit                                     0x08000ded   Thumb Code    18  exit.o(.text)
    __sig_exit                               0x08000dff   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGRTRED                            0x08000e09   Thumb Code    14  defsig_rtred_outer.o(.text)
    __rt_SIGRTMEM_inner                      0x08000e19   Thumb Code    22  defsig_rtmem_inner.o(.text)
    _sys_exit                                0x08000e69   Thumb Code     8  sys_exit.o(.text)
    _fseek                                   0x08000e75   Thumb Code   242  fseek.o(.text)
    fseek                                    0x08000e75   Thumb Code     0  fseek.o(.text)
    _seterr                                  0x08000f6d   Thumb Code    20  stdio.o(.text)
    _writebuf                                0x08000f81   Thumb Code    84  stdio.o(.text)
    _fflush                                  0x08000fd5   Thumb Code    70  stdio.o(.text)
    _deferredlazyseek                        0x0800101b   Thumb Code    60  stdio.o(.text)
    __default_signal_display                 0x0800105d   Thumb Code    50  defsig_general.o(.text)
    __rt_SIGRTRED_inner                      0x08001091   Thumb Code    14  defsig_rtred_inner.o(.text)
    _ttywrch                                 0x080010c5   Thumb Code    14  sys_wrch.o(.text)
    _ftell_internal                          0x080010d3   Thumb Code    66  ftell.o(.text)
    ftell                                    0x080010d3   Thumb Code     0  ftell.o(.text)
    ADC_ConvCpltCallback                     0x08001115   Thumb Code   138  adc.o(i.ADC_ConvCpltCallback)
    ADC_ConvHalfCpltCallback                 0x080011b5   Thumb Code   138  adc.o(i.ADC_ConvHalfCpltCallback)
    API_Chose_TS5A3359_GAIN                  0x08001255   Thumb Code    98  gpio.o(i.API_Chose_TS5A3359_GAIN)
    API_Detect_Gateway                       0x080012bd   Thumb Code   212  api_w5500.o(i.API_Detect_Gateway)
    API_Init_LAN                             0x080013c9   Thumb Code   234  api_w5500.o(i.API_Init_LAN)
    API_Init_Net_Parameters                  0x080016c1   Thumb Code   516  api_w5500.o(i.API_Init_Net_Parameters)
    API_Printf_Hex                           0x0800194d   Thumb Code    34  usart.o(i.API_Printf_Hex)
    API_Process_Socket_Data                  0x0800197d   Thumb Code   104  api_w5500.o(i.API_Process_Socket_Data)
    API_RNG_Init                             0x08001a7d   Thumb Code   288  api_tnrg.o(i.API_RNG_Init)
    API_Read_SOCK_Data_Buffer                0x08001bed   Thumb Code   238  api_w5500.o(i.API_Read_SOCK_Data_Buffer)
    API_Read_W5500_1Byte                     0x08001ce1   Thumb Code    42  api_w5500.o(i.API_Read_W5500_1Byte)
    API_Read_W5500_SOCK_1Byte                0x08001d11   Thumb Code    54  api_w5500.o(i.API_Read_W5500_SOCK_1Byte)
    API_Read_W5500_SOCK_2Byte                0x08001d4d   Thumb Code    70  api_w5500.o(i.API_Read_W5500_SOCK_2Byte)
    API_SPI0_Read_Byte                       0x08001d99   Thumb Code    48  api_w5500.o(i.API_SPI0_Read_Byte)
    API_SPI0_Send_Byte                       0x08001dcd   Thumb Code    48  api_w5500.o(i.API_SPI0_Send_Byte)
    API_SPI0_Send_Short                      0x08001e01   Thumb Code    28  api_w5500.o(i.API_SPI0_Send_Short)
    API_Socket_Connect                       0x08001e1d   Thumb Code    70  api_w5500.o(i.API_Socket_Connect)
    API_Socket_Init                          0x08001e65   Thumb Code   160  api_w5500.o(i.API_Socket_Init)
    API_Socket_Listen                        0x08001f79   Thumb Code   102  api_w5500.o(i.API_Socket_Listen)
    API_Socket_UDP                           0x08001fdf   Thumb Code    60  api_w5500.o(i.API_Socket_UDP)
    API_W5500_1MS_RunTask                    0x0800201d   Thumb Code    82  api_w5500.o(i.API_W5500_1MS_RunTask)
    API_W5500_Check_Connection               0x08002079   Thumb Code    84  api_w5500.o(i.API_W5500_Check_Connection)
    API_W5500_GPIO_Init                      0x080020fd   Thumb Code    36  api_w5500.o(i.API_W5500_GPIO_Init)
    API_W5500_HardWare_Rest                  0x08002125   Thumb Code   106  api_w5500.o(i.API_W5500_HardWare_Rest)
    API_W5500_Interrupt_Process              0x080021bd   Thumb Code   246  api_w5500.o(i.API_W5500_Interrupt_Process)
    API_W5500_ReciveDATA_Handle              0x0800231d   Thumb Code   134  api_w5500.o(i.API_W5500_ReciveDATA_Handle)
    API_W5500_Register_Init                  0x08002409   Thumb Code   114  api_w5500.o(i.API_W5500_Register_Init)
    API_W5500_SPI0_Init                      0x08002481   Thumb Code   184  api_w5500.o(i.API_W5500_SPI0_Init)
    API_W5500_Send_Data_S0                   0x08002549   Thumb Code   116  api_w5500.o(i.API_W5500_Send_Data_S0)
    API_W5500_Socket_Set                     0x0800264d   Thumb Code   192  api_w5500.o(i.API_W5500_Socket_Set)
    API_Write_SOCK_Data_Buffer               0x080027f9   Thumb Code   278  api_w5500.o(i.API_Write_SOCK_Data_Buffer)
    API_Write_W5500_1Byte                    0x08002915   Thumb Code    42  api_w5500.o(i.API_Write_W5500_1Byte)
    API_Write_W5500_2Byte                    0x08002945   Thumb Code    42  api_w5500.o(i.API_Write_W5500_2Byte)
    API_Write_W5500_SOCK_1Byte               0x08002975   Thumb Code    54  api_w5500.o(i.API_Write_W5500_SOCK_1Byte)
    API_Write_W5500_SOCK_2Byte               0x080029b1   Thumb Code    54  api_w5500.o(i.API_Write_W5500_SOCK_2Byte)
    API_Write_W5500_SOCK_4Byte               0x080029ed   Thumb Code    80  api_w5500.o(i.API_Write_W5500_SOCK_4Byte)
    API_Write_W5500_nByte                    0x08002a41   Thumb Code    62  api_w5500.o(i.API_Write_W5500_nByte)
    BusFault_Handler                         0x08002af3   Thumb Code     4  gd32f4xx_it.o(i.BusFault_Handler)
    DMA1_Channel0_IRQHandler                 0x08002af9   Thumb Code    42  gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler)
    DMA1_Channel1_IRQHandler                 0x08002b2d   Thumb Code    42  gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler)
    DMA1_Channel2_IRQHandler                 0x08002b61   Thumb Code    42  gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler)
    DRV_SPI_SwapByte                         0x08002b95   Thumb Code    50  spi.o(i.DRV_SPI_SwapByte)
    DebugMon_Handler                         0x08002bcd   Thumb Code     2  gd32f4xx_it.o(i.DebugMon_Handler)
    EEPROM_SPI_ReadBuffer                    0x08002bd1   Thumb Code    98  eeprom_spi.o(i.EEPROM_SPI_ReadBuffer)
    EEPROM_SPI_SendInstruction               0x08002c3d   Thumb Code    46  eeprom_spi.o(i.EEPROM_SPI_SendInstruction)
    EEPROM_SPI_WaitStandbyState              0x08002c71   Thumb Code    66  eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState)
    EEPROM_SPI_WriteBuffer                   0x08002cbd   Thumb Code   394  eeprom_spi.o(i.EEPROM_SPI_WriteBuffer)
    EEPROM_SPI_WritePage                     0x08002e49   Thumb Code   170  eeprom_spi.o(i.EEPROM_SPI_WritePage)
    EEPROM_WriteDisable                      0x08002efd   Thumb Code    38  eeprom_spi.o(i.EEPROM_WriteDisable)
    EEPROM_WriteEnable                       0x08002f2d   Thumb Code    38  eeprom_spi.o(i.EEPROM_WriteEnable)
    EXTI10_15_IRQHandler                     0x08002f5d   Thumb Code    26  gd32f4xx_it.o(i.EXTI10_15_IRQHandler)
    FML_USART_RecvTask                       0x08002f7d   Thumb Code   174  usart.o(i.FML_USART_RecvTask)
    GPIO_Init                                0x08003035   Thumb Code    60  gpio.o(i.GPIO_Init)
    HardFault_Handler                        0x08003075   Thumb Code     4  gd32f4xx_it.o(i.HardFault_Handler)
    Init_GPIO_TS5A339                        0x08003079   Thumb Code    52  gpio.o(i.Init_GPIO_TS5A339)
    MemManage_Handler                        0x080030b1   Thumb Code     4  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080030b5   Thumb Code     2  gd32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x080030b7   Thumb Code     2  gd32f4xx_it.o(i.PendSV_Handler)
    RTC_Init                                 0x080030b9   Thumb Code    96  rtc.o(i.RTC_Init)
    SPI1_Init                                0x080031c9   Thumb Code   146  spi.o(i.SPI1_Init)
    SVC_Handler                              0x08003265   Thumb Code     2  gd32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08003267   Thumb Code     8  gd32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08003271   Thumb Code   184  system_gd32f4xx.o(i.SystemInit)
    TIMER1_Init                              0x08003339   Thumb Code   118  time.o(i.TIMER1_Init)
    TIMER2_IRQHandler                        0x080033af   Thumb Code     8  gd32f4xx_it.o(i.TIMER2_IRQHandler)
    TIMER3_IRQHandler                        0x080033b9   Thumb Code    20  gd32f4xx_it.o(i.TIMER3_IRQHandler)
    TIMER3_Init                              0x080033d1   Thumb Code    80  time.o(i.TIMER3_Init)
    TIMER6_IRQHandler                        0x08003425   Thumb Code    20  gd32f4xx_it.o(i.TIMER6_IRQHandler)
    TIMER6_Init                              0x0800343d   Thumb Code    80  time.o(i.TIMER6_Init)
    TIM_PeriodElapsedCallback                0x08003491   Thumb Code   446  time.o(i.TIM_PeriodElapsedCallback)
    UART_IDLECallBack                        0x08003665   Thumb Code    30  usart.o(i.UART_IDLECallBack)
    UART_RxCpltCallback                      0x0800368d   Thumb Code    30  usart.o(i.UART_RxCpltCallback)
    USART0_Init                              0x080036b9   Thumb Code   126  usart.o(i.USART0_Init)
    USART2_IRQHandler                        0x08003741   Thumb Code    36  gd32f4xx_it.o(i.USART2_IRQHandler)
    USBFS_IRQHandler                         0x08003771   Thumb Code    10  gd32f4xx_it.o(i.USBFS_IRQHandler)
    USBFS_WKUP_IRQHandler                    0x08003781   Thumb Code    48  gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler)
    UsageFault_Handler                       0x080037b5   Thumb Code     4  gd32f4xx_it.o(i.UsageFault_Handler)
    _is_digit                                0x080037e1   Thumb Code    14  __printf_wp.o(i._is_digit)
    delay_1ms                                0x08003ce5   Thumb Code    16  systick.o(i.delay_1ms)
    delay_decrement                          0x08003cf9   Thumb Code    18  systick.o(i.delay_decrement)
    dma_interrupt_flag_clear                 0x08003d11   Thumb Code    62  gd32f4xx_dma.o(i.dma_interrupt_flag_clear)
    dma_interrupt_flag_get                   0x08003d4f   Thumb Code   516  gd32f4xx_dma.o(i.dma_interrupt_flag_get)
    exti_interrupt_flag_clear                0x08003f55   Thumb Code     6  gd32f4xx_exti.o(i.exti_interrupt_flag_clear)
    exti_interrupt_flag_get                  0x08003f61   Thumb Code    32  gd32f4xx_exti.o(i.exti_interrupt_flag_get)
    fputc                                    0x08003f85   Thumb Code    32  usart.o(i.fputc)
    gd_eval_key_state_get                    0x08003fa9   Thumb Code    22  gd32f470v_start.o(i.gd_eval_key_state_get)
    get_hard_rand_data                       0x08003fc9   Thumb Code    20  api_tnrg.o(i.get_hard_rand_data)
    gpio_af_set                              0x08003fdd   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x0800403b   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x0800403f   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_set)
    gpio_input_bit_get                       0x08004043   Thumb Code    16  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x08004053   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x080040a1   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    main                                     0x08004181   Thumb Code   232  main.o(i.main)
    nvic_irq_enable                          0x080043f9   Thumb Code   186  gd32f4xx_misc.o(i.nvic_irq_enable)
    nvic_priority_group_set                  0x080044bd   Thumb Code    10  gd32f4xx_misc.o(i.nvic_priority_group_set)
    pmu_backup_write_enable                  0x080044d1   Thumb Code    14  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    pmu_to_deepsleepmode                     0x080044e5   Thumb Code   204  gd32f4xx_pmu.o(i.pmu_to_deepsleepmode)
    rcu_ck48m_clock_config                   0x080045d9   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_ck48m_clock_config)
    rcu_clock_freq_get                       0x080045f1   Thumb Code   264  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    rcu_flag_get                             0x08004715   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_flag_get)
    rcu_osci_on                              0x08004739   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_osci_on)
    rcu_osci_stab_wait                       0x0800475d   Thumb Code   342  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    rcu_periph_clock_enable                  0x080048b9   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x080048dd   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08004901   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    rcu_pll48m_clock_config                  0x08004925   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_pll48m_clock_config)
    rcu_rtc_clock_config                     0x0800493d   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    rcu_system_clock_source_config           0x08004955   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_system_clock_source_config)
    rcu_system_clock_source_get              0x0800496d   Thumb Code    10  gd32f4xx_rcu.o(i.rcu_system_clock_source_get)
    rtc_init                                 0x080049b5   Thumb Code   190  gd32f4xx_rtc.o(i.rtc_init)
    rtc_init_mode_enter                      0x08004a79   Thumb Code    66  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    rtc_init_mode_exit                       0x08004ac1   Thumb Code    14  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    rtc_register_sync_wait                   0x08004ad5   Thumb Code    92  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    spi_enable                               0x08004b35   Thumb Code    10  gd32f4xx_spi.o(i.spi_enable)
    spi_i2s_data_receive                     0x08004b3f   Thumb Code     8  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    spi_i2s_data_transmit                    0x08004b47   Thumb Code     4  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    spi_i2s_flag_get                         0x08004b4b   Thumb Code    16  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    spi_init                                 0x08004b5b   Thumb Code    50  gd32f4xx_spi.o(i.spi_init)
    systick_config                           0x08004c91   Thumb Code    74  systick.o(i.systick_config)
    timer_auto_reload_shadow_enable          0x08004ce1   Thumb Code    10  gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable)
    timer_channel_output_config              0x08004ced   Thumb Code   484  gd32f4xx_timer.o(i.timer_channel_output_config)
    timer_channel_output_mode_config         0x08004ed9   Thumb Code    90  gd32f4xx_timer.o(i.timer_channel_output_mode_config)
    timer_channel_output_pulse_value_config  0x08004f33   Thumb Code    38  gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config)
    timer_channel_output_shadow_config       0x08004f59   Thumb Code    90  gd32f4xx_timer.o(i.timer_channel_output_shadow_config)
    timer_deinit                             0x08004fb5   Thumb Code   374  gd32f4xx_timer.o(i.timer_deinit)
    timer_disable                            0x08005139   Thumb Code    10  gd32f4xx_timer.o(i.timer_disable)
    timer_enable                             0x08005143   Thumb Code    10  gd32f4xx_timer.o(i.timer_enable)
    timer_init                               0x0800514d   Thumb Code   122  gd32f4xx_timer.o(i.timer_init)
    timer_interrupt_disable                  0x080051e5   Thumb Code     8  gd32f4xx_timer.o(i.timer_interrupt_disable)
    timer_interrupt_enable                   0x080051ed   Thumb Code     8  gd32f4xx_timer.o(i.timer_interrupt_enable)
    timer_interrupt_flag_clear               0x080051f5   Thumb Code     6  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    timer_interrupt_flag_get                 0x080051fb   Thumb Code    24  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    trng_deinit                              0x08005213   Thumb Code    20  gd32f4xx_trng.o(i.trng_deinit)
    trng_enable                              0x08005229   Thumb Code    14  gd32f4xx_trng.o(i.trng_enable)
    trng_flag_get                            0x0800523d   Thumb Code    18  gd32f4xx_trng.o(i.trng_flag_get)
    trng_get_true_random_data                0x08005255   Thumb Code     6  gd32f4xx_trng.o(i.trng_get_true_random_data)
    usart_baudrate_set                       0x08005261   Thumb Code   224  gd32f4xx_usart.o(i.usart_baudrate_set)
    usart_data_receive                       0x08005349   Thumb Code    10  gd32f4xx_usart.o(i.usart_data_receive)
    usart_data_transmit                      0x08005353   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_deinit                             0x0800535d   Thumb Code   210  gd32f4xx_usart.o(i.usart_deinit)
    usart_enable                             0x08005439   Thumb Code    10  gd32f4xx_usart.o(i.usart_enable)
    usart_flag_get                           0x08005443   Thumb Code    30  gd32f4xx_usart.o(i.usart_flag_get)
    usart_interrupt_flag_clear               0x08005461   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    usart_interrupt_flag_get                 0x0800547b   Thumb Code    56  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    usart_receive_config                     0x080054b3   Thumb Code    16  gd32f4xx_usart.o(i.usart_receive_config)
    usart_transmit_config                    0x080054c3   Thumb Code    16  gd32f4xx_usart.o(i.usart_transmit_config)
    usb_clock_active                         0x080054d3   Thumb Code    32  drv_usb_dev.o(i.usb_clock_active)
    usb_ctlep_startout                       0x080054f5   Thumb Code    34  drv_usb_dev.o(i.usb_ctlep_startout)
    usb_iepintr_read                         0x0800551d   Thumb Code    42  drv_usb_dev.o(i.usb_iepintr_read)
    usb_rxfifo_read                          0x08005547   Thumb Code    30  drv_usb_core.o(i.usb_rxfifo_read)
    usb_timer_irq                            0x08005565   Thumb Code    46  gd32f4xx_hw.o(i.usb_timer_irq)
    usb_transc_active                        0x0800559d   Thumb Code   142  drv_usb_dev.o(i.usb_transc_active)
    usb_transc_clrstall                      0x08005635   Thumb Code    68  drv_usb_dev.o(i.usb_transc_clrstall)
    usb_transc_inxfer                        0x08005679   Thumb Code   268  drv_usb_dev.o(i.usb_transc_inxfer)
    usb_transc_outxfer                       0x08005785   Thumb Code   144  drv_usb_dev.o(i.usb_transc_outxfer)
    usb_transc_stall                         0x08005815   Thumb Code    66  drv_usb_dev.o(i.usb_transc_stall)
    usb_txfifo_flush                         0x08005857   Thumb Code    40  drv_usb_core.o(i.usb_txfifo_flush)
    usb_txfifo_write                         0x0800587f   Thumb Code    34  drv_usb_core.o(i.usb_txfifo_write)
    usb_udelay                               0x080058a1   Thumb Code    14  gd32f4xx_hw.o(i.usb_udelay)
    usbd_class_request                       0x080058af   Thumb Code    38  usbd_enum.o(i.usbd_class_request)
    usbd_ctl_recev                           0x080058d5   Thumb Code    46  usbd_transc.o(i.usbd_ctl_recev)
    usbd_ctl_send                            0x08005903   Thumb Code    46  usbd_transc.o(i.usbd_ctl_send)
    usbd_ctl_status_recev                    0x08005931   Thumb Code    32  usbd_transc.o(i.usbd_ctl_status_recev)
    usbd_ctl_status_send                     0x08005951   Thumb Code    32  usbd_transc.o(i.usbd_ctl_status_send)
    usbd_enum_error                          0x080059fd   Thumb Code    30  usbd_enum.o(i.usbd_enum_error)
    usbd_ep_recev                            0x08005a1b   Thumb Code    60  usbd_core.o(i.usbd_ep_recev)
    usbd_ep_send                             0x08005a57   Thumb Code    60  usbd_core.o(i.usbd_ep_send)
    usbd_ep_stall                            0x08005a93   Thumb Code    58  usbd_core.o(i.usbd_ep_stall)
    usbd_ep_stall_clear                      0x08005acd   Thumb Code    58  usbd_core.o(i.usbd_ep_stall_clear)
    usbd_in_transc                           0x08005b07   Thumb Code   160  usbd_transc.o(i.usbd_in_transc)
    usbd_isr                                 0x08005f65   Thumb Code   222  drv_usbd_int.o(i.usbd_isr)
    usbd_out_transc                          0x08006043   Thumb Code   132  usbd_transc.o(i.usbd_out_transc)
    usbd_setup_transc                        0x080060c7   Thumb Code   132  usbd_transc.o(i.usbd_setup_transc)
    usbd_standard_request                    0x0800614d   Thumb Code    22  usbd_enum.o(i.usbd_standard_request)
    usbd_vendor_request                      0x08006169   Thumb Code     6  usbd_enum.o(i.usbd_vendor_request)
    _fp_init                                 0x0800616f   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08006177   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08006177   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __stdin_name                             0x080061f1   Data           4  sys_io.o(.constdata)
    __stdout_name                            0x080061f5   Data           4  sys_io.o(.constdata)
    __stderr_name                            0x080061f9   Data           4  sys_io.o(.constdata)
    Region$$Table$$Base                      0x08006200   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006230   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000004   Data           4  system_gd32f4xx.o(.data)
    TeseBuff                                 0x20000008   Data          15  main.o(.data)
    my_adcdma                                0x2000001c   Data           1  adc.o(.data)
    tim6_msTic                               0x20000020   Data           4  time.o(.data)
    TempTestTimer                            0x20000024   Data           2  time.o(.data)
    flag_USB_Rx                              0x20000026   Data           1  usb.o(.data)
    delay_time                               0x200000c0   Data           4  gd32f4xx_hw.o(.data)
    timer_prescaler                          0x200000c4   Data           4  gd32f4xx_hw.o(.data)
    __aeabi_stdin                            0x200000c8   Data           4  stdio_streams.o(.data)
    __aeabi_stdout                           0x200000cc   Data           4  stdio_streams.o(.data)
    __aeabi_stderr                           0x200000d0   Data           4  stdio_streams.o(.data)
    rtc_initpara                             0x200000e4   Data          20  rtc.o(.bss)
    g_tTimeSign                              0x200000f8   Data           9  time.o(.bss)
    my_key                                   0x20000102   Data          10  time.o(.bss)
    cdc_acm                                  0x2000010c   Data        1084  cdc_acm_core.o(.bss)
    __stdin                                  0x200005a0   Data          84  stdio_streams.o(.bss)
    __stdout                                 0x200005f4   Data          84  stdio_streams.o(.bss)
    __stderr                                 0x20000648   Data          84  stdio_streams.o(.bss)
    __libspace_start                         0x2000069c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200006fc   Data           0  libspace.o(.bss)
    sg_tUsartDriveHandle                     0x20020000   Data        2068  usart.o(.RAM_D3)
    sg_arrUasrt2RecvBuf                      0x20020814   Data        2049  usart.o(.RAM_D3)
    tmpBuf_test                              0x20021015   Data        2049  usart.o(.RAM_D3)
    usart2_buf                               0x20021816   Data           1  usart.o(.RAM_D3)
    Lan_Para                                 0x20021818   Data        2208  api_w5500.o(.RAM_D3)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000083b8, Max: 0x00200000, ABSOLUTE, COMPRESSED[0x00006344])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006230, Max: 0x00200000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000008   Code   RO         8867  * !!!main             c_w.l(__main.o)
    0x080001b4   0x080001b4   0x00000034   Code   RO         9175    !!!scatter          c_w.l(__scatter.o)
    0x080001e8   0x080001e8   0x0000005a   Code   RO         9173    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x08000242   0x08000242   0x00000002   PAD
    0x08000244   0x08000244   0x0000001a   Code   RO         9177    !!handler_copy      c_w.l(__scatter_copy.o)
    0x0800025e   0x0800025e   0x00000002   PAD
    0x08000260   0x08000260   0x0000001c   Code   RO         9179    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800027c   0x0800027c   0x00000000   Code   RO         8845    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         8843    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000282   0x08000282   0x00000006   Code   RO         8844    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x08000288   0x08000288   0x00000006   Code   RO         8842    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x0800028e   0x0800028e   0x00000006   Code   RO         8841    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x08000294   0x08000294   0x00000004   Code   RO         8930    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000298   0x08000298   0x00000002   Code   RO         9104    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800029a   0x0800029a   0x00000004   Code   RO         8943    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800029e   0x0800029e   0x00000000   Code   RO         8946    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800029e   0x0800029e   0x00000008   Code   RO         8947    .ARM.Collect$$libinit$$00000005  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8949    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8951    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8953    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8956    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8958    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8960    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8962    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8964    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8966    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8968    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8970    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8972    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         8974    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002a6   0x080002a6   0x00000004   Code   RO         8975    .ARM.Collect$$libinit$$00000024  c_w.l(libinit2.o)
    0x080002aa   0x080002aa   0x00000000   Code   RO         8976    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002aa   0x080002aa   0x00000000   Code   RO         8980    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002aa   0x080002aa   0x00000000   Code   RO         8982    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002aa   0x080002aa   0x00000000   Code   RO         8984    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002aa   0x080002aa   0x00000000   Code   RO         8986    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002aa   0x080002aa   0x00000002   Code   RO         8987    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002ac   0x080002ac   0x00000002   Code   RO         9170    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         9106    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         9108    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002ae   0x080002ae   0x00000004   Code   RO         9109    .ARM.Collect$$libshutdown$$00000005  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         9110    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         9113    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         9116    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         9118    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         9121    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000002   Code   RO         9122    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         8887    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         8999    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002b4   0x080002b4   0x00000006   Code   RO         9011    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         9001    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002ba   0x080002ba   0x00000004   Code   RO         9002    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         9004    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000008   Code   RO         9005    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002c6   0x080002c6   0x00000002   Code   RO         9125    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         9144    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002c8   0x080002c8   0x00000004   Code   RO         9145    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002cc   0x080002cc   0x00000006   Code   RO         9146    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002d2   0x080002d2   0x00000002   PAD
    0x080002d4   0x080002d4   0x00000000   Code   RO         9017    .emb_text           c_w.l(maybetermalloc1.o)
    0x080002d4   0x080002d4   0x00000040   Code   RO            4    .text               startup_gd32f450_470.o
    0x08000314   0x08000314   0x0000005e   Code   RO         8721    .text               c_w.l(h1_alloc.o)
    0x08000372   0x08000372   0x0000004e   Code   RO         8723    .text               c_w.l(h1_free.o)
    0x080003c0   0x080003c0   0x00000018   Code   RO         8785    .text               c_w.l(noretval__2printf.o)
    0x080003d8   0x080003d8   0x0000004e   Code   RO         8793    .text               c_w.l(_printf_pad.o)
    0x08000426   0x08000426   0x00000052   Code   RO         8795    .text               c_w.l(_printf_str.o)
    0x08000478   0x08000478   0x00000078   Code   RO         8797    .text               c_w.l(_printf_dec.o)
    0x080004f0   0x080004f0   0x00000058   Code   RO         8802    .text               c_w.l(_printf_hex_int.o)
    0x08000548   0x08000548   0x00000188   Code   RO         8837    .text               c_w.l(__printf_flags_ss_wp.o)
    0x080006d0   0x080006d0   0x0000003e   Code   RO         8853    .text               c_w.l(strlen.o)
    0x0800070e   0x0800070e   0x0000008a   Code   RO         8855    .text               c_w.l(rt_memcpy_v6.o)
    0x08000798   0x08000798   0x00000064   Code   RO         8857    .text               c_w.l(rt_memcpy_w.o)
    0x080007fc   0x080007fc   0x0000004e   Code   RO         8861    .text               c_w.l(rt_memclr_w.o)
    0x0800084a   0x0800084a   0x00000006   Code   RO         8865    .text               c_w.l(heapauxi.o)
    0x08000850   0x08000850   0x00000008   Code   RO         8891    .text               c_w.l(rt_heap_descriptor_intlibspace.o)
    0x08000858   0x08000858   0x00000008   Code   RO         8896    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000860   0x08000860   0x00000004   Code   RO         8898    .text               c_w.l(hguard.o)
    0x08000864   0x08000864   0x0000008a   Code   RO         8900    .text               c_w.l(init_alloc.o)
    0x080008ee   0x080008ee   0x0000000e   Code   RO         8904    .text               c_w.l(h1_init.o)
    0x080008fc   0x080008fc   0x000000b2   Code   RO         8918    .text               c_w.l(_printf_intcommon.o)
    0x080009ae   0x080009ae   0x00000002   PAD
    0x080009b0   0x080009b0   0x00000030   Code   RO         8920    .text               c_w.l(_printf_char_common.o)
    0x080009e0   0x080009e0   0x0000002c   Code   RO         8926    .text               c_w.l(_printf_char.o)
    0x08000a0c   0x08000a0c   0x00000024   Code   RO         8928    .text               c_w.l(_printf_char_file.o)
    0x08000a30   0x08000a30   0x00000008   Code   RO         8995    .text               c_w.l(libspace.o)
    0x08000a38   0x08000a38   0x00000034   Code   RO         9019    .text               c_w.l(h1_extend.o)
    0x08000a6c   0x08000a6c   0x00000008   Code   RO         9027    .text               c_w.l(ferror.o)
    0x08000a74   0x08000a74   0x00000138   Code   RO         9029    .text               c_w.l(initio.o)
    0x08000bac   0x08000bac   0x0000000e   Code   RO         9035    .text               c_w.l(defsig_rtmem_outer.o)
    0x08000bba   0x08000bba   0x00000066   Code   RO         9050    .text               c_w.l(sys_io.o)
    0x08000c20   0x08000c20   0x00000002   Code   RO         9055    .text               c_w.l(use_no_semi.o)
    0x08000c22   0x08000c22   0x00000000   Code   RO         9057    .text               c_w.l(indicate_semi.o)
    0x08000c22   0x08000c22   0x0000004a   Code   RO         9058    .text               c_w.l(sys_stackheap_outer.o)
    0x08000c6c   0x08000c6c   0x00000046   Code   RO         9071    .text               c_w.l(setvbuf.o)
    0x08000cb2   0x08000cb2   0x00000002   PAD
    0x08000cb4   0x08000cb4   0x000000ec   Code   RO         9074    .text               c_w.l(fopen.o)
    0x08000da0   0x08000da0   0x0000004c   Code   RO         9076    .text               c_w.l(fclose.o)
    0x08000dec   0x08000dec   0x00000012   Code   RO         9085    .text               c_w.l(exit.o)
    0x08000dfe   0x08000dfe   0x0000000a   Code   RO         9087    .text               c_w.l(defsig_exit.o)
    0x08000e08   0x08000e08   0x0000000e   Code   RO         9089    .text               c_w.l(defsig_rtred_outer.o)
    0x08000e16   0x08000e16   0x00000002   PAD
    0x08000e18   0x08000e18   0x00000050   Code   RO         9093    .text               c_w.l(defsig_rtmem_inner.o)
    0x08000e68   0x08000e68   0x0000000c   Code   RO         9123    .text               c_w.l(sys_exit.o)
    0x08000e74   0x08000e74   0x000000f8   Code   RO         9131    .text               c_w.l(fseek.o)
    0x08000f6c   0x08000f6c   0x000000f0   Code   RO         9133    .text               c_w.l(stdio.o)
    0x0800105c   0x0800105c   0x00000032   Code   RO         9137    .text               c_w.l(defsig_general.o)
    0x0800108e   0x0800108e   0x00000002   PAD
    0x08001090   0x08001090   0x00000034   Code   RO         9139    .text               c_w.l(defsig_rtred_inner.o)
    0x080010c4   0x080010c4   0x0000000e   Code   RO         9141    .text               c_w.l(sys_wrch.o)
    0x080010d2   0x080010d2   0x00000042   Code   RO         9150    .text               c_w.l(ftell.o)
    0x08001114   0x08001114   0x000000a0   Code   RO         6204    i.ADC_ConvCpltCallback  adc.o
    0x080011b4   0x080011b4   0x000000a0   Code   RO         6205    i.ADC_ConvHalfCpltCallback  adc.o
    0x08001254   0x08001254   0x00000068   Code   RO         7072    i.API_Chose_TS5A3359_GAIN  gpio.o
    0x080012bc   0x080012bc   0x0000010c   Code   RO         7727    i.API_Detect_Gateway  api_w5500.o
    0x080013c8   0x080013c8   0x000002f8   Code   RO         7728    i.API_Init_LAN      api_w5500.o
    0x080016c0   0x080016c0   0x0000028c   Code   RO         7729    i.API_Init_Net_Parameters  api_w5500.o
    0x0800194c   0x0800194c   0x00000030   Code   RO         7279    i.API_Printf_Hex    usart.o
    0x0800197c   0x0800197c   0x00000100   Code   RO         7730    i.API_Process_Socket_Data  api_w5500.o
    0x08001a7c   0x08001a7c   0x00000170   Code   RO         7689    i.API_RNG_Init      api_tnrg.o
    0x08001bec   0x08001bec   0x000000f4   Code   RO         7731    i.API_Read_SOCK_Data_Buffer  api_w5500.o
    0x08001ce0   0x08001ce0   0x00000030   Code   RO         7732    i.API_Read_W5500_1Byte  api_w5500.o
    0x08001d10   0x08001d10   0x0000003c   Code   RO         7733    i.API_Read_W5500_SOCK_1Byte  api_w5500.o
    0x08001d4c   0x08001d4c   0x0000004c   Code   RO         7734    i.API_Read_W5500_SOCK_2Byte  api_w5500.o
    0x08001d98   0x08001d98   0x00000034   Code   RO         7735    i.API_SPI0_Read_Byte  api_w5500.o
    0x08001dcc   0x08001dcc   0x00000034   Code   RO         7736    i.API_SPI0_Send_Byte  api_w5500.o
    0x08001e00   0x08001e00   0x0000001c   Code   RO         7737    i.API_SPI0_Send_Short  api_w5500.o
    0x08001e1c   0x08001e1c   0x00000046   Code   RO         7739    i.API_Socket_Connect  api_w5500.o
    0x08001e62   0x08001e62   0x00000002   PAD
    0x08001e64   0x08001e64   0x00000114   Code   RO         7740    i.API_Socket_Init   api_w5500.o
    0x08001f78   0x08001f78   0x00000066   Code   RO         7741    i.API_Socket_Listen  api_w5500.o
    0x08001fde   0x08001fde   0x0000003c   Code   RO         7742    i.API_Socket_UDP    api_w5500.o
    0x0800201a   0x0800201a   0x00000002   PAD
    0x0800201c   0x0800201c   0x0000005c   Code   RO         7743    i.API_W5500_1MS_RunTask  api_w5500.o
    0x08002078   0x08002078   0x00000084   Code   RO         7744    i.API_W5500_Check_Connection  api_w5500.o
    0x080020fc   0x080020fc   0x00000028   Code   RO         7745    i.API_W5500_GPIO_Init  api_w5500.o
    0x08002124   0x08002124   0x00000098   Code   RO         7746    i.API_W5500_HardWare_Rest  api_w5500.o
    0x080021bc   0x080021bc   0x00000160   Code   RO         7747    i.API_W5500_Interrupt_Process  api_w5500.o
    0x0800231c   0x0800231c   0x000000ec   Code   RO         7749    i.API_W5500_ReciveDATA_Handle  api_w5500.o
    0x08002408   0x08002408   0x00000078   Code   RO         7750    i.API_W5500_Register_Init  api_w5500.o
    0x08002480   0x08002480   0x000000c8   Code   RO         7751    i.API_W5500_SPI0_Init  api_w5500.o
    0x08002548   0x08002548   0x00000104   Code   RO         7752    i.API_W5500_Send_Data_S0  api_w5500.o
    0x0800264c   0x0800264c   0x000001ac   Code   RO         7753    i.API_W5500_Socket_Set  api_w5500.o
    0x080027f8   0x080027f8   0x0000011c   Code   RO         7754    i.API_Write_SOCK_Data_Buffer  api_w5500.o
    0x08002914   0x08002914   0x00000030   Code   RO         7755    i.API_Write_W5500_1Byte  api_w5500.o
    0x08002944   0x08002944   0x00000030   Code   RO         7756    i.API_Write_W5500_2Byte  api_w5500.o
    0x08002974   0x08002974   0x0000003c   Code   RO         7757    i.API_Write_W5500_SOCK_1Byte  api_w5500.o
    0x080029b0   0x080029b0   0x0000003c   Code   RO         7758    i.API_Write_W5500_SOCK_2Byte  api_w5500.o
    0x080029ec   0x080029ec   0x00000054   Code   RO         7759    i.API_Write_W5500_SOCK_4Byte  api_w5500.o
    0x08002a40   0x08002a40   0x00000044   Code   RO         7760    i.API_Write_W5500_nByte  api_w5500.o
    0x08002a84   0x08002a84   0x0000006e   Code   RO         7280    i.AddByteToBuffer   usart.o
    0x08002af2   0x08002af2   0x00000004   Code   RO         5781    i.BusFault_Handler  gd32f4xx_it.o
    0x08002af6   0x08002af6   0x00000002   PAD
    0x08002af8   0x08002af8   0x00000034   Code   RO         5782    i.DMA1_Channel0_IRQHandler  gd32f4xx_it.o
    0x08002b2c   0x08002b2c   0x00000034   Code   RO         5783    i.DMA1_Channel1_IRQHandler  gd32f4xx_it.o
    0x08002b60   0x08002b60   0x00000034   Code   RO         5784    i.DMA1_Channel2_IRQHandler  gd32f4xx_it.o
    0x08002b94   0x08002b94   0x00000038   Code   RO         7184    i.DRV_SPI_SwapByte  spi.o
    0x08002bcc   0x08002bcc   0x00000002   Code   RO         5785    i.DebugMon_Handler  gd32f4xx_it.o
    0x08002bce   0x08002bce   0x00000002   PAD
    0x08002bd0   0x08002bd0   0x0000006c   Code   RO         6351    i.EEPROM_SPI_ReadBuffer  eeprom_spi.o
    0x08002c3c   0x08002c3c   0x00000034   Code   RO         6352    i.EEPROM_SPI_SendInstruction  eeprom_spi.o
    0x08002c70   0x08002c70   0x0000004c   Code   RO         6353    i.EEPROM_SPI_WaitStandbyState  eeprom_spi.o
    0x08002cbc   0x08002cbc   0x0000018a   Code   RO         6354    i.EEPROM_SPI_WriteBuffer  eeprom_spi.o
    0x08002e46   0x08002e46   0x00000002   PAD
    0x08002e48   0x08002e48   0x000000b4   Code   RO         6355    i.EEPROM_SPI_WritePage  eeprom_spi.o
    0x08002efc   0x08002efc   0x00000030   Code   RO         6357    i.EEPROM_WriteDisable  eeprom_spi.o
    0x08002f2c   0x08002f2c   0x00000030   Code   RO         6358    i.EEPROM_WriteEnable  eeprom_spi.o
    0x08002f5c   0x08002f5c   0x00000020   Code   RO         5786    i.EXTI10_15_IRQHandler  gd32f4xx_it.o
    0x08002f7c   0x08002f7c   0x000000b8   Code   RO         7282    i.FML_USART_RecvTask  usart.o
    0x08003034   0x08003034   0x00000040   Code   RO         7073    i.GPIO_Init         gpio.o
    0x08003074   0x08003074   0x00000004   Code   RO         5787    i.HardFault_Handler  gd32f4xx_it.o
    0x08003078   0x08003078   0x00000038   Code   RO         7074    i.Init_GPIO_TS5A339  gpio.o
    0x080030b0   0x080030b0   0x00000004   Code   RO         5788    i.MemManage_Handler  gd32f4xx_it.o
    0x080030b4   0x080030b4   0x00000002   Code   RO         5789    i.NMI_Handler       gd32f4xx_it.o
    0x080030b6   0x080030b6   0x00000002   Code   RO         5790    i.PendSV_Handler    gd32f4xx_it.o
    0x080030b8   0x080030b8   0x00000064   Code   RO         7142    i.RTC_Init          rtc.o
    0x0800311c   0x0800311c   0x0000008e   Code   RO         7285    i.ReadBytesToBuffer  usart.o
    0x080031aa   0x080031aa   0x0000001e   Code   RO         7286    i.RecvDataHandler   usart.o
    0x080031c8   0x080031c8   0x0000009c   Code   RO         7185    i.SPI1_Init         spi.o
    0x08003264   0x08003264   0x00000002   Code   RO         5791    i.SVC_Handler       gd32f4xx_it.o
    0x08003266   0x08003266   0x00000008   Code   RO         5792    i.SysTick_Handler   gd32f4xx_it.o
    0x0800326e   0x0800326e   0x00000002   PAD
    0x08003270   0x08003270   0x000000c8   Code   RO         5736    i.SystemInit        system_gd32f4xx.o
    0x08003338   0x08003338   0x00000076   Code   RO         7214    i.TIMER1_Init       time.o
    0x080033ae   0x080033ae   0x00000008   Code   RO         5793    i.TIMER2_IRQHandler  gd32f4xx_it.o
    0x080033b6   0x080033b6   0x00000002   PAD
    0x080033b8   0x080033b8   0x00000018   Code   RO         5794    i.TIMER3_IRQHandler  gd32f4xx_it.o
    0x080033d0   0x080033d0   0x00000054   Code   RO         7215    i.TIMER3_Init       time.o
    0x08003424   0x08003424   0x00000018   Code   RO         5795    i.TIMER6_IRQHandler  gd32f4xx_it.o
    0x0800343c   0x0800343c   0x00000054   Code   RO         7216    i.TIMER6_Init       time.o
    0x08003490   0x08003490   0x000001d4   Code   RO         7217    i.TIM_PeriodElapsedCallback  time.o
    0x08003664   0x08003664   0x00000028   Code   RO         7287    i.UART_IDLECallBack  usart.o
    0x0800368c   0x0800368c   0x0000002c   Code   RO         7288    i.UART_RxCpltCallback  usart.o
    0x080036b8   0x080036b8   0x00000088   Code   RO         7290    i.USART0_Init       usart.o
    0x08003740   0x08003740   0x00000030   Code   RO         5796    i.USART2_IRQHandler  gd32f4xx_it.o
    0x08003770   0x08003770   0x00000010   Code   RO         5797    i.USBFS_IRQHandler  gd32f4xx_it.o
    0x08003780   0x08003780   0x00000034   Code   RO         5798    i.USBFS_WKUP_IRQHandler  gd32f4xx_it.o
    0x080037b4   0x080037b4   0x00000004   Code   RO         5799    i.UsageFault_Handler  gd32f4xx_it.o
    0x080037b8   0x080037b8   0x00000028   Code   RO         6088    i.__NVIC_SetPriority  systick.o
    0x080037e0   0x080037e0   0x0000000e   Code   RO         8830    i._is_digit         c_w.l(__printf_wp.o)
    0x080037ee   0x080037ee   0x00000014   Code   RO         8354    i._usb_bos_desc_get  usbd_enum.o
    0x08003802   0x08003802   0x00000022   Code   RO         8355    i._usb_config_desc_get  usbd_enum.o
    0x08003824   0x08003824   0x00000014   Code   RO         8356    i._usb_dev_desc_get  usbd_enum.o
    0x08003838   0x08003838   0x00000074   Code   RO         8357    i._usb_std_clearfeature  usbd_enum.o
    0x080038ac   0x080038ac   0x00000040   Code   RO         8358    i._usb_std_getconfiguration  usbd_enum.o
    0x080038ec   0x080038ec   0x00000114   Code   RO         8359    i._usb_std_getdescriptor  usbd_enum.o
    0x08003a00   0x08003a00   0x0000003c   Code   RO         8360    i._usb_std_getinterface  usbd_enum.o
    0x08003a3c   0x08003a3c   0x000000c4   Code   RO         8361    i._usb_std_getstatus  usbd_enum.o
    0x08003b00   0x08003b00   0x00000006   Code   RO         8362    i._usb_std_reserved  usbd_enum.o
    0x08003b06   0x08003b06   0x0000005c   Code   RO         8363    i._usb_std_setaddress  usbd_enum.o
    0x08003b62   0x08003b62   0x00000002   PAD
    0x08003b64   0x08003b64   0x000000b4   Code   RO         8364    i._usb_std_setconfiguration  usbd_enum.o
    0x08003c18   0x08003c18   0x00000006   Code   RO         8365    i._usb_std_setdescriptor  usbd_enum.o
    0x08003c1e   0x08003c1e   0x00000068   Code   RO         8366    i._usb_std_setfeature  usbd_enum.o
    0x08003c86   0x08003c86   0x00000044   Code   RO         8367    i._usb_std_setinterface  usbd_enum.o
    0x08003cca   0x08003cca   0x00000006   Code   RO         8368    i._usb_std_synchframe  usbd_enum.o
    0x08003cd0   0x08003cd0   0x00000014   Code   RO         8369    i._usb_str_desc_get  usbd_enum.o
    0x08003ce4   0x08003ce4   0x00000014   Code   RO         6089    i.delay_1ms         systick.o
    0x08003cf8   0x08003cf8   0x00000018   Code   RO         6090    i.delay_decrement   systick.o
    0x08003d10   0x08003d10   0x0000003e   Code   RO         1249    i.dma_interrupt_flag_clear  gd32f4xx_dma.o
    0x08003d4e   0x08003d4e   0x00000204   Code   RO         1250    i.dma_interrupt_flag_get  gd32f4xx_dma.o
    0x08003f52   0x08003f52   0x00000002   PAD
    0x08003f54   0x08003f54   0x0000000c   Code   RO         2274    i.exti_interrupt_flag_clear  gd32f4xx_exti.o
    0x08003f60   0x08003f60   0x00000024   Code   RO         2275    i.exti_interrupt_flag_get  gd32f4xx_exti.o
    0x08003f84   0x08003f84   0x00000024   Code   RO         7292    i.fputc             usart.o
    0x08003fa8   0x08003fa8   0x00000020   Code   RO         7598    i.gd_eval_key_state_get  gd32f470v_start.o
    0x08003fc8   0x08003fc8   0x00000014   Code   RO         7690    i.get_hard_rand_data  api_tnrg.o
    0x08003fdc   0x08003fdc   0x0000005e   Code   RO         2656    i.gpio_af_set       gd32f4xx_gpio.o
    0x0800403a   0x0800403a   0x00000004   Code   RO         2657    i.gpio_bit_reset    gd32f4xx_gpio.o
    0x0800403e   0x0800403e   0x00000004   Code   RO         2658    i.gpio_bit_set      gd32f4xx_gpio.o
    0x08004042   0x08004042   0x00000010   Code   RO         2662    i.gpio_input_bit_get  gd32f4xx_gpio.o
    0x08004052   0x08004052   0x0000004e   Code   RO         2664    i.gpio_mode_set     gd32f4xx_gpio.o
    0x080040a0   0x080040a0   0x00000042   Code   RO         2666    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x080040e2   0x080040e2   0x00000002   PAD
    0x080040e4   0x080040e4   0x0000002c   Code   RO         8565    i.hw_delay          gd32f4xx_hw.o
    0x08004110   0x08004110   0x00000070   Code   RO         8566    i.hw_time_set       gd32f4xx_hw.o
    0x08004180   0x08004180   0x00000278   Code   RO         6024    i.main              main.o
    0x080043f8   0x080043f8   0x000000c4   Code   RO         3245    i.nvic_irq_enable   gd32f4xx_misc.o
    0x080044bc   0x080044bc   0x00000014   Code   RO         3246    i.nvic_priority_group_set  gd32f4xx_misc.o
    0x080044d0   0x080044d0   0x00000014   Code   RO         3306    i.pmu_backup_write_enable  gd32f4xx_pmu.o
    0x080044e4   0x080044e4   0x000000f4   Code   RO         3320    i.pmu_to_deepsleepmode  gd32f4xx_pmu.o
    0x080045d8   0x080045d8   0x00000018   Code   RO         3455    i.rcu_ck48m_clock_config  gd32f4xx_rcu.o
    0x080045f0   0x080045f0   0x00000124   Code   RO         3458    i.rcu_clock_freq_get  gd32f4xx_rcu.o
    0x08004714   0x08004714   0x00000024   Code   RO         3461    i.rcu_flag_get      gd32f4xx_rcu.o
    0x08004738   0x08004738   0x00000024   Code   RO         3474    i.rcu_osci_on       gd32f4xx_rcu.o
    0x0800475c   0x0800475c   0x0000015c   Code   RO         3475    i.rcu_osci_stab_wait  gd32f4xx_rcu.o
    0x080048b8   0x080048b8   0x00000024   Code   RO         3477    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x080048dc   0x080048dc   0x00000024   Code   RO         3480    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x08004900   0x08004900   0x00000024   Code   RO         3481    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x08004924   0x08004924   0x00000018   Code   RO         3482    i.rcu_pll48m_clock_config  gd32f4xx_rcu.o
    0x0800493c   0x0800493c   0x00000018   Code   RO         3486    i.rcu_rtc_clock_config  gd32f4xx_rcu.o
    0x08004954   0x08004954   0x00000018   Code   RO         3491    i.rcu_system_clock_source_config  gd32f4xx_rcu.o
    0x0800496c   0x0800496c   0x00000010   Code   RO         3492    i.rcu_system_clock_source_get  gd32f4xx_rcu.o
    0x0800497c   0x0800497c   0x00000038   Code   RO         5800    i.resume_mcu_clk    gd32f4xx_it.o
    0x080049b4   0x080049b4   0x000000c4   Code   RO         3767    i.rtc_init          gd32f4xx_rtc.o
    0x08004a78   0x08004a78   0x00000048   Code   RO         3768    i.rtc_init_mode_enter  gd32f4xx_rtc.o
    0x08004ac0   0x08004ac0   0x00000014   Code   RO         3769    i.rtc_init_mode_exit  gd32f4xx_rtc.o
    0x08004ad4   0x08004ad4   0x00000060   Code   RO         3774    i.rtc_register_sync_wait  gd32f4xx_rtc.o
    0x08004b34   0x08004b34   0x0000000a   Code   RO         4335    i.spi_enable        gd32f4xx_spi.o
    0x08004b3e   0x08004b3e   0x00000008   Code   RO         4337    i.spi_i2s_data_receive  gd32f4xx_spi.o
    0x08004b46   0x08004b46   0x00000004   Code   RO         4338    i.spi_i2s_data_transmit  gd32f4xx_spi.o
    0x08004b4a   0x08004b4a   0x00000010   Code   RO         4340    i.spi_i2s_flag_get  gd32f4xx_spi.o
    0x08004b5a   0x08004b5a   0x00000032   Code   RO         4344    i.spi_init          gd32f4xx_spi.o
    0x08004b8c   0x08004b8c   0x000000fc   Code   RO         5737    i.system_clock_168m_25m_hxtal  system_gd32f4xx.o
    0x08004c88   0x08004c88   0x00000008   Code   RO         5738    i.system_clock_config  system_gd32f4xx.o
    0x08004c90   0x08004c90   0x00000050   Code   RO         6091    i.systick_config    systick.o
    0x08004ce0   0x08004ce0   0x0000000a   Code   RO         4632    i.timer_auto_reload_shadow_enable  gd32f4xx_timer.o
    0x08004cea   0x08004cea   0x00000002   PAD
    0x08004cec   0x08004cec   0x000001ec   Code   RO         4649    i.timer_channel_output_config  gd32f4xx_timer.o
    0x08004ed8   0x08004ed8   0x0000005a   Code   RO         4651    i.timer_channel_output_mode_config  gd32f4xx_timer.o
    0x08004f32   0x08004f32   0x00000026   Code   RO         4653    i.timer_channel_output_pulse_value_config  gd32f4xx_timer.o
    0x08004f58   0x08004f58   0x0000005a   Code   RO         4654    i.timer_channel_output_shadow_config  gd32f4xx_timer.o
    0x08004fb2   0x08004fb2   0x00000002   PAD
    0x08004fb4   0x08004fb4   0x00000184   Code   RO         4663    i.timer_deinit      gd32f4xx_timer.o
    0x08005138   0x08005138   0x0000000a   Code   RO         4664    i.timer_disable     gd32f4xx_timer.o
    0x08005142   0x08005142   0x0000000a   Code   RO         4668    i.timer_enable      gd32f4xx_timer.o
    0x0800514c   0x0800514c   0x00000098   Code   RO         4678    i.timer_init        gd32f4xx_timer.o
    0x080051e4   0x080051e4   0x00000008   Code   RO         4684    i.timer_interrupt_disable  gd32f4xx_timer.o
    0x080051ec   0x080051ec   0x00000008   Code   RO         4685    i.timer_interrupt_enable  gd32f4xx_timer.o
    0x080051f4   0x080051f4   0x00000006   Code   RO         4686    i.timer_interrupt_flag_clear  gd32f4xx_timer.o
    0x080051fa   0x080051fa   0x00000018   Code   RO         4687    i.timer_interrupt_flag_get  gd32f4xx_timer.o
    0x08005212   0x08005212   0x00000014   Code   RO         5255    i.trng_deinit       gd32f4xx_trng.o
    0x08005226   0x08005226   0x00000002   PAD
    0x08005228   0x08005228   0x00000014   Code   RO         5257    i.trng_enable       gd32f4xx_trng.o
    0x0800523c   0x0800523c   0x00000018   Code   RO         5258    i.trng_flag_get     gd32f4xx_trng.o
    0x08005254   0x08005254   0x0000000c   Code   RO         5259    i.trng_get_true_random_data  gd32f4xx_trng.o
    0x08005260   0x08005260   0x000000e8   Code   RO         5328    i.usart_baudrate_set  gd32f4xx_usart.o
    0x08005348   0x08005348   0x0000000a   Code   RO         5332    i.usart_data_receive  gd32f4xx_usart.o
    0x08005352   0x08005352   0x00000008   Code   RO         5333    i.usart_data_transmit  gd32f4xx_usart.o
    0x0800535a   0x0800535a   0x00000002   PAD
    0x0800535c   0x0800535c   0x000000dc   Code   RO         5334    i.usart_deinit      gd32f4xx_usart.o
    0x08005438   0x08005438   0x0000000a   Code   RO         5338    i.usart_enable      gd32f4xx_usart.o
    0x08005442   0x08005442   0x0000001e   Code   RO         5340    i.usart_flag_get    gd32f4xx_usart.o
    0x08005460   0x08005460   0x0000001a   Code   RO         5349    i.usart_interrupt_flag_clear  gd32f4xx_usart.o
    0x0800547a   0x0800547a   0x00000038   Code   RO         5350    i.usart_interrupt_flag_get  gd32f4xx_usart.o
    0x080054b2   0x080054b2   0x00000010   Code   RO         5365    i.usart_receive_config  gd32f4xx_usart.o
    0x080054c2   0x080054c2   0x00000010   Code   RO         5380    i.usart_transmit_config  gd32f4xx_usart.o
    0x080054d2   0x080054d2   0x00000020   Code   RO         8047    i.usb_clock_active  drv_usb_dev.o
    0x080054f2   0x080054f2   0x00000002   PAD
    0x080054f4   0x080054f4   0x00000028   Code   RO         8048    i.usb_ctlep_startout  drv_usb_dev.o
    0x0800551c   0x0800551c   0x0000002a   Code   RO         8053    i.usb_iepintr_read  drv_usb_dev.o
    0x08005546   0x08005546   0x0000001e   Code   RO         7962    i.usb_rxfifo_read   drv_usb_core.o
    0x08005564   0x08005564   0x00000038   Code   RO         8572    i.usb_timer_irq     gd32f4xx_hw.o
    0x0800559c   0x0800559c   0x00000098   Code   RO         8056    i.usb_transc_active  drv_usb_dev.o
    0x08005634   0x08005634   0x00000044   Code   RO         8057    i.usb_transc_clrstall  drv_usb_dev.o
    0x08005678   0x08005678   0x0000010c   Code   RO         8059    i.usb_transc_inxfer  drv_usb_dev.o
    0x08005784   0x08005784   0x00000090   Code   RO         8060    i.usb_transc_outxfer  drv_usb_dev.o
    0x08005814   0x08005814   0x00000042   Code   RO         8061    i.usb_transc_stall  drv_usb_dev.o
    0x08005856   0x08005856   0x00000028   Code   RO         7964    i.usb_txfifo_flush  drv_usb_core.o
    0x0800587e   0x0800587e   0x00000022   Code   RO         7965    i.usb_txfifo_write  drv_usb_core.o
    0x080058a0   0x080058a0   0x0000000e   Code   RO         8573    i.usb_udelay        gd32f4xx_hw.o
    0x080058ae   0x080058ae   0x00000026   Code   RO         8372    i.usbd_class_request  usbd_enum.o
    0x080058d4   0x080058d4   0x0000002e   Code   RO         8499    i.usbd_ctl_recev    usbd_transc.o
    0x08005902   0x08005902   0x0000002e   Code   RO         8500    i.usbd_ctl_send     usbd_transc.o
    0x08005930   0x08005930   0x00000020   Code   RO         8501    i.usbd_ctl_status_recev  usbd_transc.o
    0x08005950   0x08005950   0x00000020   Code   RO         8502    i.usbd_ctl_status_send  usbd_transc.o
    0x08005970   0x08005970   0x0000008c   Code   RO         8168    i.usbd_emptytxfifo_write  drv_usbd_int.o
    0x080059fc   0x080059fc   0x0000001e   Code   RO         8373    i.usbd_enum_error   usbd_enum.o
    0x08005a1a   0x08005a1a   0x0000003c   Code   RO         8261    i.usbd_ep_recev     usbd_core.o
    0x08005a56   0x08005a56   0x0000003c   Code   RO         8262    i.usbd_ep_send      usbd_core.o
    0x08005a92   0x08005a92   0x0000003a   Code   RO         8264    i.usbd_ep_stall     usbd_core.o
    0x08005acc   0x08005acc   0x0000003a   Code   RO         8265    i.usbd_ep_stall_clear  usbd_core.o
    0x08005b06   0x08005b06   0x000000a0   Code   RO         8503    i.usbd_in_transc    usbd_transc.o
    0x08005ba6   0x08005ba6   0x00000002   PAD
    0x08005ba8   0x08005ba8   0x00000068   Code   RO         8169    i.usbd_int_enumfinish  drv_usbd_int.o
    0x08005c10   0x08005c10   0x0000008a   Code   RO         8170    i.usbd_int_epin     drv_usbd_int.o
    0x08005c9a   0x08005c9a   0x000000ce   Code   RO         8171    i.usbd_int_epout    drv_usbd_int.o
    0x08005d68   0x08005d68   0x000000d8   Code   RO         8172    i.usbd_int_reset    drv_usbd_int.o
    0x08005e40   0x08005e40   0x000000bc   Code   RO         8173    i.usbd_int_rxfifo   drv_usbd_int.o
    0x08005efc   0x08005efc   0x00000068   Code   RO         8174    i.usbd_int_suspend  drv_usbd_int.o
    0x08005f64   0x08005f64   0x000000de   Code   RO         8175    i.usbd_isr          drv_usbd_int.o
    0x08006042   0x08006042   0x00000084   Code   RO         8504    i.usbd_out_transc   usbd_transc.o
    0x080060c6   0x080060c6   0x00000084   Code   RO         8505    i.usbd_setup_transc  usbd_transc.o
    0x0800614a   0x0800614a   0x00000002   PAD
    0x0800614c   0x0800614c   0x0000001c   Code   RO         8374    i.usbd_standard_request  usbd_enum.o
    0x08006168   0x08006168   0x00000006   Code   RO         8375    i.usbd_vendor_request  usbd_enum.o
    0x0800616e   0x0800616e   0x0000000a   Code   RO         9048    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08006178   0x08006178   0x00000004   Data   RO         8062    .constdata          drv_usb_dev.o
    0x0800617c   0x0800617c   0x0000003c   Data   RO         8176    .constdata          drv_usbd_int.o
    0x080061b8   0x080061b8   0x00000028   Data   RO         8803    .constdata          c_w.l(_printf_hex_int.o)
    0x080061e0   0x080061e0   0x00000011   Data   RO         8838    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x080061f1   0x080061f1   0x00000004   Data   RO         9051    .constdata          c_w.l(sys_io.o)
    0x080061f5   0x080061f5   0x00000004   Data   RO         9052    .constdata          c_w.l(sys_io.o)
    0x080061f9   0x080061f9   0x00000004   Data   RO         9053    .constdata          c_w.l(sys_io.o)
    0x080061fd   0x080061fd   0x00000003   PAD
    0x08006200   0x08006200   0x00000030   Data   RO         9171    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20000000, Load base: 0x08006230, Size: 0x00000000, Max: 0x00000004, ABSOLUTE, UNINIT)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000004, Load base: 0x08006230, Size: 0x000086fc, Max: 0x0001fffc, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000004   0x08006230   0x00000004   Data   RW         5739    .data               system_gd32f4xx.o
    0x20000008   0x08006234   0x0000000f   Data   RW         6026    .data               main.o
    0x20000017   0x08006243   0x00000001   PAD
    0x20000018   0x08006244   0x00000004   Data   RW         6092    .data               systick.o
    0x2000001c   0x08006248   0x00000001   Data   RW         6208    .data               adc.o
    0x2000001d   0x08006249   0x00000003   PAD
    0x20000020   0x0800624c   0x00000006   Data   RW         7219    .data               time.o
    0x20000026   0x08006252   0x00000001   Data   RW         7387    .data               usb.o
    0x20000027   0x08006253   0x00000001   PAD
    0x20000028   0x08006254   0x0000003e   Data   RW         7603    .data               gd32f470v_start.o
    0x20000066   0x08006292   0x00000002   PAD
    0x20000068   0x08006294   0x00000012   Data   RW         7762    .data               api_w5500.o
    0x2000007a   0x080062a6   0x00000002   PAD
    0x2000007c   0x080062a8   0x00000043   Data   RW         8376    .data               usbd_enum.o
    0x200000bf   0x080062eb   0x00000001   PAD
    0x200000c0   0x080062ec   0x00000008   Data   RW         8574    .data               gd32f4xx_hw.o
    0x200000c8   0x080062f4   0x00000004   Data   RW         8936    .data               c_w.l(stdio_streams.o)
    0x200000cc   0x080062f8   0x00000004   Data   RW         8937    .data               c_w.l(stdio_streams.o)
    0x200000d0   0x080062fc   0x00000004   Data   RW         8938    .data               c_w.l(stdio_streams.o)
    0x200000d4        -       0x00000010   Zero   RW         3325    .bss                gd32f4xx_pmu.o
    0x200000e4        -       0x00000014   Zero   RW         7144    .bss                rtc.o
    0x200000f8        -       0x00000014   Zero   RW         7218    .bss                time.o
    0x2000010c        -       0x00000494   Zero   RW         8647    .bss                cdc_acm_core.o
    0x200005a0        -       0x00000054   Zero   RW         8933    .bss                c_w.l(stdio_streams.o)
    0x200005f4        -       0x00000054   Zero   RW         8934    .bss                c_w.l(stdio_streams.o)
    0x20000648        -       0x00000054   Zero   RW         8935    .bss                c_w.l(stdio_streams.o)
    0x2000069c        -       0x00000060   Zero   RW         8996    .bss                c_w.l(libspace.o)
    0x200006fc   0x08006300   0x00000004   PAD
    0x20000700        -       0x00003000   Zero   RW            2    HEAP                startup_gd32f450_470.o
    0x20003700        -       0x00005000   Zero   RW            1    STACK               startup_gd32f450_470.o


    Execution Region RW_IRAM3 (Exec base: 0x20020000, Load base: 0x08006300, Size: 0x000020b8, Max: 0x00030000, ABSOLUTE, COMPRESSED[0x00000044])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20020000   COMPRESSED   0x00001817   Data   RW         7293    .RAM_D3             usart.o
    0x20021817   COMPRESSED   0x00000001   PAD
    0x20021818   COMPRESSED   0x000008a0   Data   RW         7761    .RAM_D3             api_w5500.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0       1300   25lc080a.o
       320         44          0          1          0       4166   adc.o
       388         80          0          0          0       1464   api_tnrg.o
      5668       1784          0       2226          0      24039   api_w5500.o
         0          0          0          0       1172       5215   cdc_acm_core.o
       104          0          0          0          0      21956   drv_usb_core.o
       812         16          4          0          0      22017   drv_usb_dev.o
      1318         28         60          0          0      11731   drv_usbd_int.o
       906         56          0          0          0       6290   eeprom_spi.o
        32         10          0         62          0       1776   gd32f470v_start.o
         0          0          0          0          0      91580   gd32f4xx_adc.o
       578          0          0          0          0       1884   gd32f4xx_dma.o
        48         10          0          0          0       1470   gd32f4xx_exti.o
       262          0          0          0          0       4765   gd32f4xx_gpio.o
       226         28          0          8          0       3310   gd32f4xx_hw.o
       448         66          0          0          0      10829   gd32f4xx_it.o
       216         20          0          0          0       1624   gd32f4xx_misc.o
       264         46          0          0         16       1493   gd32f4xx_pmu.o
       932         84          0          0          0       9807   gd32f4xx_rcu.o
       384         22          0          0          0       3847   gd32f4xx_rtc.o
        88          0          0          0          0       4258   gd32f4xx_spi.o
      1326         52          0          0          0      10496   gd32f4xx_timer.o
        76         18          0          0          0       2556   gd32f4xx_trng.o
       624         18          0          0          0       7622   gd32f4xx_usart.o
       224         14          0          0          0       1774   gpio.o
       632        400          0         15          0       1955   main.o
       100          4          0          0         20        925   rtc.o
       212         16          0          0          0       1271   spi.o
        64         26        428          0      32768       1024   startup_gd32f450_470.o
       460         28          0          4          0       3171   system_gd32f4xx.o
       164         24          0          4          0      33110   systick.o
       754         30          0          6         20       8249   time.o
       770         62          0       6167          0       8584   usart.o
         0          0          0          1          0       1019   usb.o
       236          0          0          0          0       7421   usbd_core.o
      1370         18          0         67          0      16709   usbd_enum.o
       580          0          0          0          0       5531   usbd_transc.o

    ----------------------------------------------------------------------
     20620       <USER>        <GROUP>       8572      33996     346238   Object Totals
         0          0         48          0          0          0   (incl. Generated)
        34          0          0         11          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_u.o
         6          0          0          0          0          0   _printf_x.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        80         58          0          0          0         76   defsig_rtmem_inner.o
        14          0          0          0          0         80   defsig_rtmem_outer.o
        52         38          0          0          0         76   defsig_rtred_inner.o
        14          0          0          0          0         80   defsig_rtred_outer.o
        18          0          0          0          0         80   exit.o
        76          0          0          0          0         88   fclose.o
         8          0          0          0          0         68   ferror.o
       236          4          0          0          0        128   fopen.o
       248          6          0          0          0         84   fseek.o
        66          0          0          0          0         76   ftell.o
        94          0          0          0          0         80   h1_alloc.o
        52          0          0          0          0         68   h1_extend.o
        78          0          0          0          0         80   h1_free.o
        14          0          0          0          0         84   h1_init.o
         6          0          0          0          0        152   heapauxi.o
         4          0          0          0          0        136   hguard.o
         0          0          0          0          0          0   indicate_semi.o
       138          0          0          0          0        168   init_alloc.o
       312         46          0          0          0        112   initio.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         6          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         0          0          0          0          0          0   maybetermalloc1.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_heap_descriptor_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        70          0          0          0          0         80   setvbuf.o
       240          6          0          0          0        156   stdio.o
         0          0          0         12        252          0   stdio_streams.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
       102          0         12          0          0        240   sys_io.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
      3904        <USER>         <GROUP>         12        352       4316   Library Totals
        14          0          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3880        220         69         12        348       4200   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
      3904        <USER>         <GROUP>         12        352       4316   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     24524       3224        612       8584      34348     328286   Grand Totals
     24524       3224        612        276      34348     328286   ELF Image Totals (compressed)
     24524       3224        612        276          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                25136 (  24.55kB)
    Total RW  Size (RW Data + ZI Data)             42932 (  41.93kB)
    Total ROM Size (Code + RO Data + RW Data)      25412 (  24.82kB)

==============================================================================

