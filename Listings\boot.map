Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER2_IRQHandler) for TIMER2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER3_IRQHandler) for TIMER3_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.EXTI10_15_IRQHandler) for EXTI10_15_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) for USBFS_WKUP_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER6_IRQHandler) for TIMER6_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) for DMA1_Channel0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) for DMA1_Channel2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USBFS_IRQHandler) for USBFS_IRQHandler
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_168m_25m_hxtal) for system_clock_168m_25m_hxtal
    gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_interrupt_flag_get) for dma_interrupt_flag_get
    gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) refers to adc.o(i.ADC_ConvHalfCpltCallback) for ADC_ConvHalfCpltCallback
    gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) refers to adc.o(i.ADC_ConvCpltCallback) for ADC_ConvCpltCallback
    gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to gd32f4xx_dma.o(i.dma_interrupt_flag_get) for dma_interrupt_flag_get
    gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to adc.o(i.ADC_ConvHalfCpltCallback) for ADC_ConvHalfCpltCallback
    gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to adc.o(i.ADC_ConvCpltCallback) for ADC_ConvCpltCallback
    gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) refers to gd32f4xx_dma.o(i.dma_interrupt_flag_get) for dma_interrupt_flag_get
    gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) refers to adc.o(i.ADC_ConvHalfCpltCallback) for ADC_ConvHalfCpltCallback
    gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) refers to adc.o(i.ADC_ConvCpltCallback) for ADC_ConvCpltCallback
    gd32f4xx_it.o(i.EXTI10_15_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_get) for exti_interrupt_flag_get
    gd32f4xx_it.o(i.EXTI10_15_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f4xx_it.o(i.EXTI10_15_IRQHandler) refers to time.o(.bss) for my_key
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.TIMER2_IRQHandler) refers to gd32f4xx_hw.o(i.usb_timer_irq) for usb_timer_irq
    gd32f4xx_it.o(i.TIMER3_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER3_IRQHandler) refers to time.o(i.TIM_PeriodElapsedCallback) for TIM_PeriodElapsedCallback
    gd32f4xx_it.o(i.TIMER6_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER6_IRQHandler) refers to time.o(i.TIM_PeriodElapsedCallback) for TIM_PeriodElapsedCallback
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(i.UART_RxCpltCallback) for UART_RxCpltCallback
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(i.UART_IDLECallBack) for UART_IDLECallBack
    gd32f4xx_it.o(i.USBFS_IRQHandler) refers to drv_usbd_int.o(i.usbd_isr) for usbd_isr
    gd32f4xx_it.o(i.USBFS_IRQHandler) refers to cdc_acm_core.o(.bss) for cdc_acm
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to gd32f4xx_it.o(i.resume_mcu_clk) for resume_mcu_clk
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to gd32f4xx_rcu.o(i.rcu_pll48m_clock_config) for rcu_pll48m_clock_config
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to gd32f4xx_rcu.o(i.rcu_ck48m_clock_config) for rcu_ck48m_clock_config
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to drv_usb_dev.o(i.usb_clock_active) for usb_clock_active
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) refers to cdc_acm_core.o(.bss) for cdc_acm
    gd32f4xx_it.o(i.resume_mcu_clk) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_it.o(i.resume_mcu_clk) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_it.o(i.resume_mcu_clk) refers to gd32f4xx_rcu.o(i.rcu_system_clock_source_config) for rcu_system_clock_source_config
    gd32f4xx_it.o(i.resume_mcu_clk) refers to gd32f4xx_rcu.o(i.rcu_system_clock_source_get) for rcu_system_clock_source_get
    main.o(i.main) refers to gd32f4xx_misc.o(i.nvic_vector_table_set) for nvic_vector_table_set
    main.o(i.main) refers to systick.o(i.systick_config) for systick_config
    main.o(i.main) refers to gpio.o(i.GPIO_Init) for GPIO_Init
    main.o(i.main) refers to usart.o(i.USART0_Init) for USART0_Init
    main.o(i.main) refers to time.o(i.TIMER6_Init) for TIMER6_Init
    main.o(i.main) refers to time.o(i.TIMER3_Init) for TIMER3_Init
    main.o(i.main) refers to rtc.o(i.RTC_Init) for RTC_Init
    main.o(i.main) refers to time.o(i.TIMER1_Init) for TIMER1_Init
    main.o(i.main) refers to spi.o(i.SPI1_Init) for SPI1_Init
    main.o(i.main) refers to api_tnrg.o(i.API_RNG_Init) for API_RNG_Init
    main.o(i.main) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    main.o(i.main) refers to api_w5500.o(i.API_Init_LAN) for API_Init_LAN
    main.o(i.main) refers to api_w5500.o(i.API_W5500_ReciveDATA_Handle) for API_W5500_ReciveDATA_Handle
    main.o(i.main) refers to api_w5500.o(i.API_W5500_Send_Data_S0) for API_W5500_Send_Data_S0
    main.o(i.main) refers to time.o(.bss) for g_tTimeSign
    main.o(i.main) refers to main.o(.data) for TeseBuff
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.systick_config) refers to systick.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    25lc080a.o(i.EEPROM_WritePage) refers to malloc.o(i.malloc) for malloc
    25lc080a.o(i.EEPROM_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    25lc080a.o(i.EEPROM_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.EEPROM_WritePage) refers to malloc.o(i.free) for free
    25lc080a.o(i.EEPROM_WritePage) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.EEPROM_WritePage) refers to 25lc080a.o(i.USER_EEPROM_WriteByte) for USER_EEPROM_WriteByte
    25lc080a.o(i.EEPROM_WritePage) refers to 25lc080a.o(i.USER_EEPROM_RedeByte) for USER_EEPROM_RedeByte
    25lc080a.o(i.USER_EEPROM_RedeByte) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_RedePage) refers to malloc.o(i.malloc) for malloc
    25lc080a.o(i.USER_EEPROM_RedePage) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_RedePage) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.USER_EEPROM_RedePage) refers to malloc.o(i.free) for free
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to malloc.o(i.malloc) for malloc
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to 25lc080a.o(i.USER_EEPROM_RedeByte) for USER_EEPROM_RedeByte
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to malloc.o(i.free) for free
    25lc080a.o(i.USER_EEPROM_WritePage) refers to 25lc080a.o(i.EEPROM_WritePage) for EEPROM_WritePage
    adc.o(i.ADC0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_regular_channel_config) for adc_regular_channel_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_source_config) for adc_external_trigger_source_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_resolution_config) for adc_resolution_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_dma_request_after_last_enable) for adc_dma_request_after_last_enable
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_dma_mode_enable) for adc_dma_mode_enable
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_config) for adc_oversample_mode_config
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_enable) for adc_oversample_mode_enable
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    adc.o(i.ADC0_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    adc.o(i.ADC0_Init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    adc.o(i.ADC1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_regular_channel_config) for adc_regular_channel_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_source_config) for adc_external_trigger_source_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_resolution_config) for adc_resolution_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_dma_request_after_last_enable) for adc_dma_request_after_last_enable
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_dma_mode_enable) for adc_dma_mode_enable
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_config) for adc_oversample_mode_config
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_enable) for adc_oversample_mode_enable
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    adc.o(i.ADC1_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    adc.o(i.ADC1_Init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    adc.o(i.ADC2_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_regular_channel_config) for adc_regular_channel_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_source_config) for adc_external_trigger_source_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_resolution_config) for adc_resolution_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_dma_request_after_last_enable) for adc_dma_request_after_last_enable
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_dma_mode_enable) for adc_dma_mode_enable
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_config) for adc_oversample_mode_config
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_oversample_mode_enable) for adc_oversample_mode_enable
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    adc.o(i.ADC2_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    adc.o(i.ADC2_Init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    adc.o(i.ADC_ConvCpltCallback) refers to gd32f4xx_dma.o(i.dma_interrupt_flag_clear) for dma_interrupt_flag_clear
    adc.o(i.ADC_ConvCpltCallback) refers to adc.o(.data) for my_adcdma
    adc.o(i.ADC_ConvHalfCpltCallback) refers to gd32f4xx_dma.o(i.dma_interrupt_flag_clear) for dma_interrupt_flag_clear
    adc.o(i.ADC_ConvHalfCpltCallback) refers to adc.o(.data) for my_adcdma
    dac.o(i.DAC1_Set_Vol) refers to dfltui.o(.text) for __aeabi_ui2d
    dac.o(i.DAC1_Set_Vol) refers to ddiv.o(.text) for __aeabi_ddiv
    dac.o(i.DAC1_Set_Vol) refers to dmul.o(.text) for __aeabi_dmul
    dac.o(i.DAC1_Set_Vol) refers to dfixui.o(.text) for __aeabi_d2uiz
    dac.o(i.DAC1_Set_Vol) refers to gd32f4xx_dac.o(i.dac_data_set) for dac_data_set
    dac.o(i.DAC2_Set_Vol) refers to dfltui.o(.text) for __aeabi_ui2d
    dac.o(i.DAC2_Set_Vol) refers to ddiv.o(.text) for __aeabi_ddiv
    dac.o(i.DAC2_Set_Vol) refers to dmul.o(.text) for __aeabi_dmul
    dac.o(i.DAC2_Set_Vol) refers to dfixui.o(.text) for __aeabi_d2uiz
    dac.o(i.DAC2_Set_Vol) refers to gd32f4xx_dac.o(i.dac_data_set) for dac_data_set
    dac.o(i.DAC_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    dac.o(i.DAC_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_deinit) for dac_deinit
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_trigger_disable) for dac_trigger_disable
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_wave_mode_config) for dac_wave_mode_config
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_output_buffer_disable) for dac_output_buffer_disable
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_concurrent_enable) for dac_concurrent_enable
    dac.o(i.DAC_Init) refers to gd32f4xx_dac.o(i.dac_concurrent_data_set) for dac_concurrent_data_set
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_circulation_enable) for dma_circulation_enable
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_interrupt_enable) for dma_interrupt_enable
    dma.o(i.DMA1_CH0_Init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    dma.o(i.DMA1_CH0_Init) refers to adc.o(.RAM_D3) for adc_data
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_circulation_enable) for dma_circulation_enable
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_interrupt_enable) for dma_interrupt_enable
    dma.o(i.DMA1_CH1_Init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    dma.o(i.DMA1_CH1_Init) refers to adc.o(.RAM_D3) for adc_data
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_circulation_enable) for dma_circulation_enable
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_interrupt_enable) for dma_interrupt_enable
    dma.o(i.DMA1_CH2_Init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    dma.o(i.DMA1_CH2_Init) refers to adc.o(.RAM_D3) for adc_data
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to eeprom_spi.o(i.EEPROM_SendByte) for EEPROM_SendByte
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) refers to eeprom_spi.o(i.EEPROM_SPI_WritePage) for EEPROM_SPI_WritePage
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_WriteEnable) for EEPROM_WriteEnable
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) for EEPROM_SPI_WaitStandbyState
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_WriteDisable) for EEPROM_WriteDisable
    eeprom_spi.o(i.EEPROM_SendByte) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_WriteEnable) for EEPROM_WriteEnable
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_WriteDisable) for EEPROM_WriteDisable
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) for EEPROM_SPI_WaitStandbyState
    esp32_wifi.o(i.CheckCmdRepeatInList) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP32BLE_SendData_Add) refers to memcpya.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.ESP32BLE_SendData_Add) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP32BLE_SendData_Read) refers to esp32_wifi.o(i.ESP_SendBleData) for ESP_SendBleData
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(i.ESP_QueryNetConnectStatus) for ESP_QueryNetConnectStatus
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(i.ESP_QueryWifiInfo) for ESP_QueryWifiInfo
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(i.ESP_SetWifiConnect) for ESP_SetWifiConnect
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(i.ESP_BuildNetConnect) for ESP_BuildNetConnect
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(i.ESP_GetSNTP) for ESP_GetSNTP
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.ESP32_Heart_Beat) refers to user_step.o(.bss) for my_device_info
    esp32_wifi.o(i.ESP32_SendData_Add) refers to memcpya.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.ESP32_SendData_Add) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP32_SendData_Add_Plus) refers to memcpya.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.ESP32_SendData_Add_Plus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP32_SendData_Read) refers to esp32_wifi.o(i.ESP_SendNetData) for ESP_SendNetData
    esp32_wifi.o(i.ESP32_SendData_Read_Plus) refers to esp32_wifi.o(i.ESP_SendNetData) for ESP_SendNetData
    esp32_wifi.o(i.ESP_ATE0) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_ATE0) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_ATE0) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_ATE0) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_ATE1) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_ATE1) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_ATE1) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_ATE1) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_AT_TEST) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_AT_TEST) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_AT_TEST) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_AT_TEST) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEADVDATA) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEADVDATA) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEADVDATA) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEADVDATA) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEADVPARAM) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEADVPARAM) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEADVPARAM) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEADVPARAM) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEADVSTART) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEADVSTART) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEADVSTART) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEADVSTART) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEADVSTOP) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEADVSTOP) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEADVSTOP) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEADVSTOP) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEGATTSSRVCRE) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEGATTSSRVCRE) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEGATTSSRVCRE) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEGATTSSRVCRE) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEGATTSSRVSTART) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEGATTSSRVSTART) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEGATTSSRVSTART) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEGATTSSRVSTART) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLEINIT) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLEINIT) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLEINIT) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLEINIT) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BLENAME) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BLENAME) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BLENAME) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_BLENAME) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_BuildNetConnect) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_CloseNetConnect) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_CloseNetConnect) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_CloseNetConnect) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_CloseNetConnect) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_CloseWifiConnect) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_CloseWifiConnect) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_CloseWifiConnect) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_CloseWifiConnect) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_GetSNTP) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_GetSNTP) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_GetSNTP) refers to user_step.o(.bss) for my_net_time
    esp32_wifi.o(i.ESP_GetSNTP) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_GetSNTP) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to esp32_wifi.o(i.week_str2num) for week_str2num
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to esp32_wifi.o(i.month_str2num) for month_str2num
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to rtc.o(i.rtc_register_set) for rtc_register_set
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to user_step.o(.bss) for my_net_time
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to esp32_wifi.o(.data) for daytimestructure
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to rtc.o(.bss) for rtc_initpara
    esp32_wifi.o(i.ESP_GetSntpTimeInfo) refers to rtc.o(.data) for name_hour
    esp32_wifi.o(i.ESP_GetTcpConnectStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_GetWifiConnectStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_Get_DomainIP) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_Get_DomainIP) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_Get_DomainIP) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_Get_DomainIP) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    esp32_wifi.o(i.ESP_Init) refers to memseta.o(.text) for __aeabi_memclr4
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.InitCmdTaskToList) for InitCmdTaskToList
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.InitSendDataRingBuf) for InitSendDataRingBuf
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.InitSendDataRingBufPlus) for InitSendDataRingBufPlus
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.InitBLESendDataRingBuf) for InitBLESendDataRingBuf
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_SetNetDataRecv) for ESP_SetNetDataRecv
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_QueryVersionInfo) for ESP_QueryVersionInfo
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_SetWorkMode) for ESP_SetWorkMode
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_SET_FindApListRAM) for ESP_SET_FindApListRAM
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_SetSNTP_CFG) for ESP_SetSNTP_CFG
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_RFPOWER) for ESP_RFPOWER
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_XRFPOWER) for ESP_XRFPOWER
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLEINIT) for ESP_BLEINIT
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLEGATTSSRVCRE) for ESP_BLEGATTSSRVCRE
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLEGATTSSRVSTART) for ESP_BLEGATTSSRVSTART
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLENAME) for ESP_BLENAME
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLEADVDATA) for ESP_BLEADVDATA
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_BLEADVPARAM) for ESP_BLEADVPARAM
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_SetWifiConnect) for ESP_SetWifiConnect
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.ESP_QueryNetConnectStatus) for ESP_QueryNetConnectStatus
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_Init) refers to esp32_wifi.o(i.User_Netdata_process) for User_Netdata_process
    esp32_wifi.o(i.ESP_Init) refers to user_step.o(.bss) for my_device_info
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.OnUnpackGeneralAck) for OnUnpackGeneralAck
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.ProcessingNetworkData) for ProcessingNetworkData
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.ProcessingBLEData) for ProcessingBLEData
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.JudgeWifiConnectionStatus) for JudgeWifiConnectionStatus
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.JudgeBLEConnectionStatus) for JudgeBLEConnectionStatus
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(i.JudgeNetConnectionStatus) for JudgeNetConnectionStatus
    esp32_wifi.o(i.ESP_OnReciveParseUsartData) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.ESP_QueryNetConnectStatus) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_QueryNetConnectStatus) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_QueryNetConnectStatus) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_QueryNetConnectStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_QueryVersionInfo) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_QueryVersionInfo) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_QueryVersionInfo) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_QueryVersionInfo) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_QueryWifiInfo) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_QueryWifiInfo) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_QueryWifiInfo) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_QueryWifiInfo) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_QueryWifiInfoLists) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_QueryWifiInfoLists) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_QueryWifiInfoLists) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_QueryWifiInfoLists) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_QueryWorkMode) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_QueryWorkMode) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_QueryWorkMode) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_QueryWorkMode) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_RESET) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_RESET) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_RESET) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_RESET) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_RESTORE) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_RESTORE) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_RESTORE) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_RESTORE) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_RFPOWER) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_RFPOWER) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_RFPOWER) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_RFPOWER) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_RunTask) refers to esp32_wifi.o(i.ProcessAckOverTime) for ProcessAckOverTime
    esp32_wifi.o(i.ESP_RunTask) refers to esp32_wifi.o(i.SendCmdByTaskList) for SendCmdByTaskList
    esp32_wifi.o(i.ESP_RunTask) refers to esp32_wifi.o(i.ESP_SendData) for ESP_SendData
    esp32_wifi.o(i.ESP_RunTask) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SET_FindApListRAM) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SET_FindApListRAM) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SET_FindApListRAM) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_SET_FindApListRAM) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SendBleData) refers to memcpya.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.ESP_SendBleData) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SendBleData) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SendData) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SendData) refers to esp32_wifi.o(i.ESP32_SendData_Read) for ESP32_SendData_Read
    esp32_wifi.o(i.ESP_SendData) refers to esp32_wifi.o(i.ESP32_SendData_Read_Plus) for ESP32_SendData_Read_Plus
    esp32_wifi.o(i.ESP_SendData) refers to esp32_wifi.o(i.ESP32BLE_SendData_Read) for ESP32BLE_SendData_Read
    esp32_wifi.o(i.ESP_SendData) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SendNetData) refers to memcpya.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.ESP_SendNetData) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SendNetData) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetAutoConn) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SetAutoConn) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SetAutoConn) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_SetAutoConn) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetNetDataRecv) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetSNTP_CFG) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SetSNTP_CFG) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SetSNTP_CFG) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_SetSNTP_CFG) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_SetWifiConnect) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetWorkMode) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_SetWorkMode) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_SetWorkMode) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ESP_SetWorkMode) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_StatusTask) refers to usart.o(i.ESP32_IO_Init) for ESP32_IO_Init
    esp32_wifi.o(i.ESP_StatusTask) refers to usart.o(i.FML_USART_Register) for FML_USART_Register
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_Init) for ESP_Init
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_BLEADVSTOP) for ESP_BLEADVSTOP
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_SetWifiConnect) for ESP_SetWifiConnect
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(i.SetWifiInFo) for SetWifiInFo
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(i.GetWifiInFo) for GetWifiInFo
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(i.ESP32BLE_Command_Add) for ESP32BLE_Command_Add
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_CloseNetConnect) for ESP_CloseNetConnect
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_BuildNetConnect) for ESP_BuildNetConnect
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(i.Set_SeverInfo) for Set_SeverInfo
    esp32_wifi.o(i.ESP_StatusTask) refers to memseta.o(.text) for __aeabi_memclr
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(i.Get_SeverInfo) for Get_SeverInfo
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_QueryWifiInfo) for ESP_QueryWifiInfo
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_GetSNTP) for ESP_GetSNTP
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.ESP_OnReciveParseUsartData) for ESP_OnReciveParseUsartData
    esp32_wifi.o(i.ESP_StatusTask) refers to esp32_wifi.o(i.UserProcess_JAPCmd) for UserProcess_JAPCmd
    esp32_wifi.o(i.ESP_StatusTask) refers to user_step.o(.bss) for my_device_info
    esp32_wifi.o(i.ESP_XRFPOWER) refers to esp32_wifi.o(i.CheckCmdRepeatInList) for CheckCmdRepeatInList
    esp32_wifi.o(i.ESP_XRFPOWER) refers to esp32_wifi.o(i.AddCmdTaskToList) for AddCmdTaskToList
    esp32_wifi.o(i.ESP_XRFPOWER) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.ESP_XRFPOWER) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.InitCmdTaskToList) refers to memseta.o(.text) for __aeabi_memclr
    esp32_wifi.o(i.JudgeBLEConnectionStatus) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.JudgeBLEConnectionStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.JudgeNetConnectionStatus) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.JudgeNetConnectionStatus) refers to atoi.o(.text) for atoi
    esp32_wifi.o(i.JudgeNetConnectionStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.JudgeWifiConnectionStatus) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.JudgeWifiConnectionStatus) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.JudgeWifiConnectionStatus) refers to user_step.o(.bss) for my_net_time
    esp32_wifi.o(i.OnPackBLEADVDATA_Cmd) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.OnPackBLENAME_Cmd) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) refers to esp32_wifi.o(.constdata) for .constdata
    esp32_wifi.o(i.OnPackCWModeCmd) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.OnPackJAPCmd) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.OnPackRFPOWERCmd) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.OnPackRFPOWERCmd) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.OnPackTCPOrUDPDataCmd) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackCWModeAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackCWModeAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackCWModeAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackCWModeAck) refers to atoi.o(.text) for atoi
    esp32_wifi.o(i.OnUnpackCWModeAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackGeneralAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackGeneralAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackGeneralAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackGeneralAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackJAPAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackJAPAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackJAPAck) refers to memseta.o(.text) for __aeabi_memclr4
    esp32_wifi.o(i.OnUnpackJAPAck) refers to esp32_wifi.o(i.split) for split
    esp32_wifi.o(i.OnUnpackJAPAck) refers to atoi.o(.text) for atoi
    esp32_wifi.o(i.OnUnpackJAPAck) refers to esp32_wifi.o(i.ConversionSignalIntensity) for ConversionSignalIntensity
    esp32_wifi.o(i.OnUnpackJAPAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackJAPAck) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.OnUnpackJAPAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackJAPAck) refers to user_step.o(.bss) for my_net_time
    esp32_wifi.o(i.OnUnpackRSTAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackRSTAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackRSTAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackRSTAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to esp32_wifi.o(i.ESP_GetSntpTimeInfo) for ESP_GetSntpTimeInfo
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackSntpTimeAck) refers to user_step.o(.bss) for my_net_time
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to memseta.o(.text) for __aeabi_memclr
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to memcpya.o(.text) for __aeabi_memcpy
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.OnUnpackVERSIONAck) refers to user_step.o(.bss) for my_device_info
    esp32_wifi.o(i.OnUnpackyNetConnectStatusAck) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.OnUnpackyNetConnectStatusAck) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.OnUnpackyNetConnectStatusAck) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.OnUnpackyNetConnectStatusAck) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ProcessAckOverTime) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.ProcessAckOverTime) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.ProcessAckOverTime) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.ProcessingBLEData) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.ProcessingBLEData) refers to user_step.o(i.ESP32BLE_Command_Add) for ESP32BLE_Command_Add
    esp32_wifi.o(i.ProcessingBLEData) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.ProcessingNetworkData) refers to strstr.o(.text) for strstr
    esp32_wifi.o(i.ProcessingNetworkData) refers to atoi.o(.text) for atoi
    esp32_wifi.o(i.ProcessingNetworkData) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.SendCmdByTaskList) refers to esp32_wifi.o(i.ReadCmdInfoFromList) for ReadCmdInfoFromList
    esp32_wifi.o(i.SendCmdByTaskList) refers to memcpya.o(.text) for __aeabi_memcpy4
    esp32_wifi.o(i.SendCmdByTaskList) refers to esp32_wifi.o(i.ReadCmdTaskCallFunFromList) for ReadCmdTaskCallFunFromList
    esp32_wifi.o(i.SendCmdByTaskList) refers to esp32_wifi.o(i.DeletCurrCmdTaskFromList) for DeletCurrCmdTaskFromList
    esp32_wifi.o(i.SendCmdByTaskList) refers to strlen.o(.text) for strlen
    esp32_wifi.o(i.SendCmdByTaskList) refers to usart.o(i.UARTx_SendBuffer) for UARTx_SendBuffer
    esp32_wifi.o(i.SendCmdByTaskList) refers to memseta.o(.text) for __aeabi_memclr4
    esp32_wifi.o(i.SendCmdByTaskList) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    esp32_wifi.o(i.SendCmdByTaskList) refers to esp32_wifi.o(.RAM_D3) for sg_tEspHandle
    esp32_wifi.o(i.SendCmdByTaskList) refers to esp32_wifi.o(.data) for sg_ktCmdTable
    esp32_wifi.o(i.UserProcess_BLEADVSTARCmd) refers to esp32_wifi.o(i.ESP_RESET) for ESP_RESET
    esp32_wifi.o(i.UserProcess_BLEADVSTARCmd) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    esp32_wifi.o(i.UserProcess_BLEADVSTARCmd) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.UserProcess_BLEADVSTOPCmd) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.UserProcess_JAPCmd) refers to esp32_wifi.o(.RAM_D3) for My_ESPStatusInfo
    esp32_wifi.o(i.UserProcess_UpDataOkCmd) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    esp32_wifi.o(i.User_Netdata_process) refers to user_step.o(i.ESP32_Command_Add) for ESP32_Command_Add
    esp32_wifi.o(i.User_Netdata_process) refers to user_step.o(i.ESP32_Command_Add_Plus) for ESP32_Command_Add_Plus
    esp32_wifi.o(i.XOnPackRFPOWERCmd) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.month_str2num) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.month_str2num) refers to strcmp.o(.text) for strcmp
    esp32_wifi.o(i.month_str2num) refers to esp32_wifi.o(.data) for Month_name
    esp32_wifi.o(i.split) refers to strlen.o(.text) for strlen
    esp32_wifi.o(i.split) refers to strtok.o(.text) for strtok
    esp32_wifi.o(i.week_str2num) refers to printf8.o(i.__0snprintf$8) for __2snprintf
    esp32_wifi.o(i.week_str2num) refers to strcmp.o(.text) for strcmp
    esp32_wifi.o(i.week_str2num) refers to esp32_wifi.o(.data) for Week_name
    esp32_wifi.o(.constdata) refers to esp32_wifi.o(.conststring) for .conststring
    esp32_wifi.o(.data) refers to esp32_wifi.o(.conststring) for .conststring
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackRSTAck) for OnUnpackRSTAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackVERSIONAck) for OnUnpackVERSIONAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackRFPOWERCmd) for OnPackRFPOWERCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.XOnPackRFPOWERCmd) for XOnPackRFPOWERCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackCWModeCmd) for OnPackCWModeCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackCWModeAck) for OnUnpackCWModeAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackJAPCmd) for OnPackJAPCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackJAPAck) for OnUnpackJAPAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackyNetConnectStatusAck) for OnUnpackyNetConnectStatusAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) for OnPackBuildTCPOrUDPCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck) for OnUnpackBuildTCPOrUDPAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackTCPOrUDPDataCmd) for OnPackTCPOrUDPDataCmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck) for OnUnpackTCPOrUDPDataCmdAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackTCPOrUDPData) for OnPackTCPOrUDPData
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck) for OnUnpackTCPOrUDPDataSendAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd) for OnPackSetSNTPCFG_Cmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackSntpTimeAck) for OnUnpackSntpTimeAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnUnpackDomainIPAck) for OnUnpackDomainIPAck
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackBLENAME_Cmd) for OnPackBLENAME_Cmd
    esp32_wifi.o(.data) refers to esp32_wifi.o(i.OnPackBLEADVDATA_Cmd) for OnPackBLEADVDATA_Cmd
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    flash.o(i.bsp_EraseCpuFlash) refers to flash.o(i.FLASH_If_Init) for FLASH_If_Init
    flash.o(i.bsp_EraseCpuFlash) refers to flash.o(i.bsp_GetSector) for bsp_GetSector
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    flash.o(i.bsp_WriteCpuFlash) refers to flash.o(i.bsp_CmpCpuFlash) for bsp_CmpCpuFlash
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_word_program) for fmc_word_program
    flash.o(i.bsp_WriteCpuFlash) refers to memcpya.o(.text) for __aeabi_memcpy
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    gpio.o(i.API_Chose_TS5A3359_GAIN) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gpio.o(i.API_Chose_TS5A3359_GAIN) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gpio.o(i.GPIO_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gpio.o(i.GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gpio.o(i.GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gpio.o(i.GPIO_Init) refers to gpio.o(i.Init_GPIO_TS5A339) for Init_GPIO_TS5A339
    gpio.o(i.GPIO_Init) refers to gpio.o(i.API_Chose_TS5A3359_GAIN) for API_Chose_TS5A3359_GAIN
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    my_crc.o(i.CRC16_USB) refers to my_crc.o(i.InvertUint8) for InvertUint8
    my_crc.o(i.CRC16_USB) refers to my_crc.o(i.InvertUint16) for InvertUint16
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc.o(i.RTC_Init) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    rtc.o(i.RTC_Init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    rtc.o(i.RTC_Init) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.RTC_Init) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_register_set) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.rtc_register_set) refers to rtc.o(.bss) for rtc_initpara
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    spi.o(i.SPI1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    spi.o(i.SPI1_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    time.o(i.TIMER1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_config) for timer_channel_output_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config) for timer_channel_output_pulse_value_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_mode_config) for timer_channel_output_mode_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_shadow_config) for timer_channel_output_shadow_config
    time.o(i.TIMER3_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER3_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER6_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    time.o(i.TIM_PeriodElapsedCallback) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    time.o(i.TIM_PeriodElapsedCallback) refers to api_w5500.o(i.API_W5500_1MS_RunTask) for API_W5500_1MS_RunTask
    time.o(i.TIM_PeriodElapsedCallback) refers to gd32f470v_start.o(i.gd_eval_key_state_get) for gd_eval_key_state_get
    time.o(i.TIM_PeriodElapsedCallback) refers to time.o(.data) for tim6_msTic
    time.o(i.TIM_PeriodElapsedCallback) refers to time.o(.bss) for g_tTimeSign
    usart.o(i.API_Printf_Hex) refers to printf8.o(i.__0printf$8) for __2printf
    usart.o(i.ESP32_IO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.ESP32_IO_Init) refers to usart.o(i.InitBuffer) for InitBuffer
    usart.o(i.ESP32_IO_Init) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.FML_USART_RecvTask) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.FML_USART_RecvTask) refers to usart.o(i.ReadBytesToBuffer) for ReadBytesToBuffer
    usart.o(i.FML_USART_RecvTask) refers to memcpya.o(.text) for __aeabi_memcpy
    usart.o(i.FML_USART_RecvTask) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.FML_USART_Register) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.InitBuffer) refers to memseta.o(.text) for __aeabi_memclr
    usart.o(i.ReadBytesToBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    usart.o(i.RecvDataHandler) refers to usart.o(i.AddByteToBuffer) for AddByteToBuffer
    usart.o(i.UART_IDLECallBack) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_clear) for usart_interrupt_flag_clear
    usart.o(i.UART_IDLECallBack) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.UART_IDLECallBack) refers to usart.o(i.FML_USART_RecvTask) for FML_USART_RecvTask
    usart.o(i.UART_RxCpltCallback) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.UART_RxCpltCallback) refers to usart.o(i.RecvDataHandler) for RecvDataHandler
    usart.o(i.UART_RxCpltCallback) refers to usart.o(.RAM_D3) for usart2_buf
    usart.o(i.UARTx_SendBuffer) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart.o(i.UARTx_SendBuffer) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.USART0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    user_step.o(i.SysReset_Condition) refers to user_step.o(.NoInit) for g_JumpInit
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f470v_start.o(.data) for BEEP_CLK
    gd32f470v_start.o(i.gd_eval_BEEP_off) refers to gd32f470v_start.o(.data) for BEEP_GPIO_PIN
    gd32f470v_start.o(i.gd_eval_BEEP_on) refers to gd32f470v_start.o(.data) for BEEP_GPIO_PIN
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_syscfg.o(i.syscfg_exti_line_config) for syscfg_exti_line_config
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_exti.o(i.exti_init) for exti_init
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f470v_start.o(.data) for KEY_CLK
    gd32f470v_start.o(i.gd_eval_key_state_get) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    gd32f470v_start.o(i.gd_eval_key_state_get) refers to gd32f470v_start.o(.data) for KEY_PIN
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f470v_start.o(.data) for GPIO_CLK
    gd32f470v_start.o(i.gd_eval_led_off) refers to gd32f470v_start.o(.data) for GPIO_PIN
    gd32f470v_start.o(i.gd_eval_led_on) refers to gd32f470v_start.o(.data) for GPIO_PIN
    gd32f470v_start.o(i.gd_eval_led_toggle) refers to gd32f470v_start.o(.data) for GPIO_PIN
    api_tnrg.o(i.API_RNG_Init) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    api_tnrg.o(i.API_RNG_Init) refers to printf8.o(i.__0printf$8) for __2printf
    api_tnrg.o(i.API_RNG_Init) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_deinit) for trng_deinit
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_enable) for trng_enable
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_flag_get) for trng_flag_get
    api_tnrg.o(i.API_RNG_Init) refers to api_tnrg.o(i.get_hard_rand_data) for get_hard_rand_data
    api_tnrg.o(i.API_RNG_Init) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    api_tnrg.o(i.API_RNG_Init) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_tnrg.o(i.get_hard_rand_data) refers to gd32f4xx_trng.o(i.trng_flag_get) for trng_flag_get
    api_tnrg.o(i.get_hard_rand_data) refers to gd32f4xx_trng.o(i.trng_get_true_random_data) for trng_get_true_random_data
    api_w5500.o(i.API_Detect_Gateway) refers to api_w5500.o(i.API_Write_W5500_SOCK_4Byte) for API_Write_W5500_SOCK_4Byte
    api_w5500.o(i.API_Detect_Gateway) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Detect_Gateway) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_Detect_Gateway) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_Detect_Gateway) refers to printf8.o(i.__0printf$8) for __2printf
    api_w5500.o(i.API_Detect_Gateway) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_SPI0_Init) for API_W5500_SPI0_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_GPIO_Init) for API_W5500_GPIO_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_Init_Net_Parameters) for API_Init_Net_Parameters
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_HardWare_Rest) for API_W5500_HardWare_Rest
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_Register_Init) for API_W5500_Register_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_Detect_Gateway) for API_Detect_Gateway
    api_w5500.o(i.API_Init_LAN) refers to printf8.o(i.__0printf$8) for __2printf
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_Socket_Init) for API_Socket_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_Socket_Set) for API_W5500_Socket_Set
    api_w5500.o(i.API_Init_Net_Parameters) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    api_w5500.o(i.API_Init_Net_Parameters) refers to printf8.o(i.__0printf$8) for __2printf
    api_w5500.o(i.API_Init_Net_Parameters) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_Process_Socket_Data) refers to api_w5500.o(i.API_Read_SOCK_Data_Buffer) for API_Read_SOCK_Data_Buffer
    api_w5500.o(i.API_Process_Socket_Data) refers to printf8.o(i.__0printf$8) for __2printf
    api_w5500.o(i.API_Process_Socket_Data) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_w5500.o(i.API_Process_Socket_Data) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Read_W5500_SOCK_2Byte) for API_Read_W5500_SOCK_2Byte
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_SPI0_Read_Byte) for API_SPI0_Read_Byte
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Write_W5500_SOCK_2Byte) for API_Write_W5500_SOCK_2Byte
    api_w5500.o(i.API_Read_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Read_W5500_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Read_W5500_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Read_W5500_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Read_W5500_1Byte) refers to api_w5500.o(i.API_SPI0_Read_Byte) for API_SPI0_Read_Byte
    api_w5500.o(i.API_Read_W5500_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Read_W5500_SOCK_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Read_W5500_SOCK_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Read_W5500_SOCK_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Read_W5500_SOCK_1Byte) refers to api_w5500.o(i.API_SPI0_Read_Byte) for API_SPI0_Read_Byte
    api_w5500.o(i.API_Read_W5500_SOCK_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Read_W5500_SOCK_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Read_W5500_SOCK_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Read_W5500_SOCK_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Read_W5500_SOCK_2Byte) refers to api_w5500.o(i.API_SPI0_Read_Byte) for API_SPI0_Read_Byte
    api_w5500.o(i.API_Read_W5500_SOCK_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_SPI0_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    api_w5500.o(i.API_SPI0_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    api_w5500.o(i.API_SPI0_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    api_w5500.o(i.API_SPI0_Send_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    api_w5500.o(i.API_SPI0_Send_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    api_w5500.o(i.API_SPI0_Send_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    api_w5500.o(i.API_SPI0_Send_Short) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    api_w5500.o(i.API_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    api_w5500.o(i.API_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    api_w5500.o(i.API_Socket_Connect) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Socket_Connect) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_Socket_Connect) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_Socket_Init) refers to api_w5500.o(i.API_Write_W5500_SOCK_2Byte) for API_Write_W5500_SOCK_2Byte
    api_w5500.o(i.API_Socket_Init) refers to api_w5500.o(i.API_Write_W5500_SOCK_4Byte) for API_Write_W5500_SOCK_4Byte
    api_w5500.o(i.API_Socket_Init) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_Socket_Listen) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Socket_Listen) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_Socket_Listen) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_Socket_UDP) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Socket_UDP) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_Socket_UDP) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_1MS_RunTask) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_1MS_RunTask) refers to api_w5500.o(.data) for Temp_Cnt
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to printf8.o(i.__0printf$8) for __2printf
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to api_w5500.o(i.API_Read_W5500_1Byte) for API_Read_W5500_1Byte
    api_w5500.o(i.API_W5500_HardWare_Rest) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to api_w5500.o(i.API_Read_W5500_1Byte) for API_Read_W5500_1Byte
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to api_w5500.o(i.API_Read_W5500_SOCK_1Byte) for API_Read_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to printf8.o(i.__0printf$8) for __2printf
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to api_w5500.o(i.API_Socket_Init) for API_Socket_Init
    api_w5500.o(i.API_W5500_Interrupt_Process) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to api_w5500.o(i.API_W5500_Interrupt_Process) for API_W5500_Interrupt_Process
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to api_w5500.o(i.API_Process_Socket_Data) for API_Process_Socket_Data
    api_w5500.o(i.API_W5500_ReciveDATA_Handle) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Register_Init) refers to api_w5500.o(i.API_Write_W5500_1Byte) for API_Write_W5500_1Byte
    api_w5500.o(i.API_W5500_Register_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    api_w5500.o(i.API_W5500_Register_Init) refers to api_w5500.o(i.API_Write_W5500_nByte) for API_Write_W5500_nByte
    api_w5500.o(i.API_W5500_Register_Init) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_W5500_Register_Init) refers to api_w5500.o(i.API_Write_W5500_2Byte) for API_Write_W5500_2Byte
    api_w5500.o(i.API_W5500_Register_Init) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    api_w5500.o(i.API_W5500_SPI0_Init) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to api_w5500.o(i.API_Write_SOCK_Data_Buffer) for API_Write_SOCK_Data_Buffer
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to printf8.o(i.__0printf$8) for __2printf
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_w5500.o(i.API_W5500_Send_Data_S0) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Socket_Set) refers to api_w5500.o(i.API_Socket_Listen) for API_Socket_Listen
    api_w5500.o(i.API_W5500_Socket_Set) refers to api_w5500.o(i.API_Socket_Connect) for API_Socket_Connect
    api_w5500.o(i.API_W5500_Socket_Set) refers to printf8.o(i.__0printf$8) for __2printf
    api_w5500.o(i.API_W5500_Socket_Set) refers to api_w5500.o(i.API_Socket_UDP) for API_Socket_UDP
    api_w5500.o(i.API_W5500_Socket_Set) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Read_W5500_SOCK_2Byte) for API_Read_W5500_SOCK_2Byte
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Write_W5500_SOCK_2Byte) for API_Write_W5500_SOCK_2Byte
    api_w5500.o(i.API_Write_SOCK_Data_Buffer) refers to api_w5500.o(i.API_Write_W5500_SOCK_1Byte) for API_Write_W5500_SOCK_1Byte
    api_w5500.o(i.API_Write_W5500_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_W5500_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_W5500_SOCK_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_SOCK_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_SOCK_1Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_SOCK_1Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_W5500_SOCK_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_SOCK_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_SOCK_2Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_SOCK_2Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_W5500_SOCK_4Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_SOCK_4Byte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_SOCK_4Byte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_SOCK_4Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_Write_W5500_nByte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_Write_W5500_nByte) refers to api_w5500.o(i.API_SPI0_Send_Short) for API_SPI0_Send_Short
    api_w5500.o(i.API_Write_W5500_nByte) refers to api_w5500.o(i.API_SPI0_Send_Byte) for API_SPI0_Send_Byte
    api_w5500.o(i.API_Write_W5500_nByte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    drv_usb_core.o(i.usb_basic_init) refers to memseta.o(.text) for __aeabi_memclr4
    drv_usb_core.o(i.usb_basic_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    drv_usb_core.o(i.usb_core_init) refers to drv_usb_core.o(i.usb_core_reset) for usb_core_reset
    drv_usb_core.o(i.usb_core_init) refers to gd32f4xx_hw.o(i.usb_mdelay) for usb_mdelay
    drv_usb_core.o(i.usb_core_reset) refers to gd32f4xx_hw.o(i.usb_udelay) for usb_udelay
    drv_usb_core.o(i.usb_rxfifo_flush) refers to gd32f4xx_hw.o(i.usb_udelay) for usb_udelay
    drv_usb_core.o(i.usb_txfifo_flush) refers to gd32f4xx_hw.o(i.usb_udelay) for usb_udelay
    drv_usb_dev.o(i.usb_dev_stop) refers to drv_usb_core.o(i.usb_rxfifo_flush) for usb_rxfifo_flush
    drv_usb_dev.o(i.usb_dev_stop) refers to drv_usb_core.o(i.usb_txfifo_flush) for usb_txfifo_flush
    drv_usb_dev.o(i.usb_dev_suspend) refers to gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) for pmu_to_deepsleepmode
    drv_usb_dev.o(i.usb_devcore_init) refers to drv_usb_core.o(i.usb_set_txfifo) for usb_set_txfifo
    drv_usb_dev.o(i.usb_devcore_init) refers to drv_usb_core.o(i.usb_txfifo_flush) for usb_txfifo_flush
    drv_usb_dev.o(i.usb_devcore_init) refers to drv_usb_core.o(i.usb_rxfifo_flush) for usb_rxfifo_flush
    drv_usb_dev.o(i.usb_devcore_init) refers to drv_usb_dev.o(i.usb_devint_enable) for usb_devint_enable
    drv_usb_dev.o(i.usb_devcore_init) refers to drv_usb_dev.o(.data) for USBFS_TX_FIFO_SIZE
    drv_usb_dev.o(i.usb_rwkup_active) refers to gd32f4xx_hw.o(i.usb_mdelay) for usb_mdelay
    drv_usb_dev.o(i.usb_transc0_active) refers to drv_usb_dev.o(.constdata) for EP0_MAXLEN
    drv_usb_dev.o(i.usb_transc_active) refers to drv_usb_dev.o(.constdata) for EP0_MAXLEN
    drv_usb_dev.o(i.usb_transc_inxfer) refers to drv_usb_core.o(i.usb_txfifo_write) for usb_txfifo_write
    drv_usbd_int.o(i.usbd_emptytxfifo_write) refers to drv_usb_core.o(i.usb_txfifo_write) for usb_txfifo_write
    drv_usbd_int.o(i.usbd_int_enumfinish) refers to drv_usbd_int.o(.constdata) for USB_SPEED
    drv_usbd_int.o(i.usbd_int_epin) refers to drv_usb_dev.o(i.usb_iepintr_read) for usb_iepintr_read
    drv_usbd_int.o(i.usbd_int_epin) refers to usbd_transc.o(i.usbd_in_transc) for usbd_in_transc
    drv_usbd_int.o(i.usbd_int_epin) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    drv_usbd_int.o(i.usbd_int_epin) refers to drv_usbd_int.o(i.usbd_emptytxfifo_write) for usbd_emptytxfifo_write
    drv_usbd_int.o(i.usbd_int_epout) refers to usbd_transc.o(i.usbd_out_transc) for usbd_out_transc
    drv_usbd_int.o(i.usbd_int_epout) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    drv_usbd_int.o(i.usbd_int_epout) refers to usbd_transc.o(i.usbd_setup_transc) for usbd_setup_transc
    drv_usbd_int.o(i.usbd_int_reset) refers to drv_usb_core.o(i.usb_txfifo_flush) for usb_txfifo_flush
    drv_usbd_int.o(i.usbd_int_reset) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    drv_usbd_int.o(i.usbd_int_reset) refers to memseta.o(.text) for __aeabi_memclr4
    drv_usbd_int.o(i.usbd_int_reset) refers to memcpya.o(.text) for __aeabi_memcpy4
    drv_usbd_int.o(i.usbd_int_reset) refers to drv_usb_dev.o(i.usb_transc_active) for usb_transc_active
    drv_usbd_int.o(i.usbd_int_reset) refers to drv_usbd_int.o(.constdata) for <Data1>
    drv_usbd_int.o(i.usbd_int_rxfifo) refers to drv_usb_core.o(i.usb_rxfifo_read) for usb_rxfifo_read
    drv_usbd_int.o(i.usbd_int_rxfifo) refers to usb.o(.data) for flag_USB_Rx
    drv_usbd_int.o(i.usbd_int_suspend) refers to gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) for pmu_to_deepsleepmode
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_epout) for usbd_int_epout
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_epin) for usbd_int_epin
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_suspend) for usbd_int_suspend
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_rxfifo) for usbd_int_rxfifo
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_reset) for usbd_int_reset
    drv_usbd_int.o(i.usbd_isr) refers to drv_usbd_int.o(i.usbd_int_enumfinish) for usbd_int_enumfinish
    usbd_core.o(i.usbd_connect) refers to gd32f4xx_hw.o(i.usb_mdelay) for usb_mdelay
    usbd_core.o(i.usbd_disconnect) refers to gd32f4xx_hw.o(i.usb_mdelay) for usb_mdelay
    usbd_core.o(i.usbd_ep_clear) refers to drv_usb_dev.o(i.usb_transc_deactivate) for usb_transc_deactivate
    usbd_core.o(i.usbd_ep_recev) refers to drv_usb_dev.o(i.usb_transc_outxfer) for usb_transc_outxfer
    usbd_core.o(i.usbd_ep_send) refers to drv_usb_dev.o(i.usb_transc_inxfer) for usb_transc_inxfer
    usbd_core.o(i.usbd_ep_setup) refers to drv_usb_dev.o(i.usb_transc_active) for usb_transc_active
    usbd_core.o(i.usbd_ep_setup) refers to usbd_core.o(.constdata) for ep_type
    usbd_core.o(i.usbd_ep_stall) refers to drv_usb_dev.o(i.usb_transc_stall) for usb_transc_stall
    usbd_core.o(i.usbd_ep_stall_clear) refers to drv_usb_dev.o(i.usb_transc_clrstall) for usb_transc_clrstall
    usbd_core.o(i.usbd_fifo_flush) refers to drv_usb_core.o(i.usb_txfifo_flush) for usb_txfifo_flush
    usbd_core.o(i.usbd_fifo_flush) refers to drv_usb_core.o(i.usb_rxfifo_flush) for usb_rxfifo_flush
    usbd_core.o(i.usbd_init) refers to usbd_enum.o(i.serial_string_get) for serial_string_get
    usbd_core.o(i.usbd_init) refers to drv_usb_core.o(i.usb_basic_init) for usb_basic_init
    usbd_core.o(i.usbd_init) refers to drv_usb_core.o(i.usb_core_init) for usb_core_init
    usbd_core.o(i.usbd_init) refers to usbd_core.o(i.usbd_disconnect) for usbd_disconnect
    usbd_core.o(i.usbd_init) refers to drv_usb_core.o(i.usb_curmode_set) for usb_curmode_set
    usbd_core.o(i.usbd_init) refers to drv_usb_dev.o(i.usb_devcore_init) for usb_devcore_init
    usbd_core.o(i.usbd_init) refers to usbd_core.o(i.usbd_connect) for usbd_connect
    usbd_enum.o(i._usb_std_clearfeature) refers to usbd_core.o(i.usbd_ep_stall_clear) for usbd_ep_stall_clear
    usbd_enum.o(i._usb_std_getdescriptor) refers to usbd_enum.o(i._usb_bos_desc_get) for _usb_bos_desc_get
    usbd_enum.o(i._usb_std_getdescriptor) refers to usbd_enum.o(.data) for std_desc_get
    usbd_enum.o(i._usb_std_getstatus) refers to usbd_enum.o(.data) for status
    usbd_enum.o(i._usb_std_setconfiguration) refers to usbd_enum.o(.data) for config
    usbd_enum.o(i._usb_std_setfeature) refers to usbd_core.o(i.usbd_ep_stall) for usbd_ep_stall
    usbd_enum.o(i.serial_string_get) refers to usbd_enum.o(i.int_to_unicode) for int_to_unicode
    usbd_enum.o(i.usbd_enum_error) refers to usbd_core.o(i.usbd_ep_stall) for usbd_ep_stall
    usbd_enum.o(i.usbd_enum_error) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    usbd_enum.o(i.usbd_standard_request) refers to usbd_enum.o(.data) for _std_dev_req
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_getstatus) for _usb_std_getstatus
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_clearfeature) for _usb_std_clearfeature
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_reserved) for _usb_std_reserved
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_setfeature) for _usb_std_setfeature
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_setaddress) for _usb_std_setaddress
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_getdescriptor) for _usb_std_getdescriptor
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_setdescriptor) for _usb_std_setdescriptor
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_getconfiguration) for _usb_std_getconfiguration
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_setconfiguration) for _usb_std_setconfiguration
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_getinterface) for _usb_std_getinterface
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_setinterface) for _usb_std_setinterface
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_std_synchframe) for _usb_std_synchframe
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_dev_desc_get) for _usb_dev_desc_get
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_config_desc_get) for _usb_config_desc_get
    usbd_enum.o(.data) refers to usbd_enum.o(i._usb_str_desc_get) for _usb_str_desc_get
    usbd_transc.o(i.usbd_ctl_recev) refers to usbd_core.o(i.usbd_ep_recev) for usbd_ep_recev
    usbd_transc.o(i.usbd_ctl_send) refers to usbd_core.o(i.usbd_ep_send) for usbd_ep_send
    usbd_transc.o(i.usbd_ctl_status_recev) refers to usbd_core.o(i.usbd_ep_recev) for usbd_ep_recev
    usbd_transc.o(i.usbd_ctl_status_recev) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    usbd_transc.o(i.usbd_ctl_status_send) refers to usbd_core.o(i.usbd_ep_send) for usbd_ep_send
    usbd_transc.o(i.usbd_ctl_status_send) refers to drv_usb_dev.o(i.usb_ctlep_startout) for usb_ctlep_startout
    usbd_transc.o(i.usbd_in_transc) refers to usbd_transc.o(i.usbd_ctl_send) for usbd_ctl_send
    usbd_transc.o(i.usbd_in_transc) refers to usbd_core.o(i.usbd_ep_send) for usbd_ep_send
    usbd_transc.o(i.usbd_in_transc) refers to usbd_transc.o(i.usbd_ctl_status_recev) for usbd_ctl_status_recev
    usbd_transc.o(i.usbd_out_transc) refers to usbd_transc.o(i.usbd_ctl_recev) for usbd_ctl_recev
    usbd_transc.o(i.usbd_out_transc) refers to usbd_transc.o(i.usbd_ctl_status_send) for usbd_ctl_status_send
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_enum.o(i.usbd_standard_request) for usbd_standard_request
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_enum.o(i.usbd_class_request) for usbd_class_request
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_enum.o(i.usbd_vendor_request) for usbd_vendor_request
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_transc.o(i.usbd_ctl_status_send) for usbd_ctl_status_send
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_transc.o(i.usbd_ctl_send) for usbd_ctl_send
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_transc.o(i.usbd_ctl_recev) for usbd_ctl_recev
    usbd_transc.o(i.usbd_setup_transc) refers to usbd_enum.o(i.usbd_enum_error) for usbd_enum_error
    gd32f4xx_hw.o(i.hw_delay) refers to gd32f4xx_hw.o(i.hw_time_set) for hw_time_set
    gd32f4xx_hw.o(i.hw_delay) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    gd32f4xx_hw.o(i.hw_delay) refers to gd32f4xx_hw.o(.data) for delay_time
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_interrupt_disable) for timer_interrupt_disable
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable) for timer_auto_reload_shadow_enable
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    gd32f4xx_hw.o(i.hw_time_set) refers to gd32f4xx_hw.o(.data) for timer_prescaler
    gd32f4xx_hw.o(i.usb_gpio_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f4xx_hw.o(i.usb_gpio_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f4xx_hw.o(i.usb_gpio_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f4xx_hw.o(i.usb_gpio_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd32f4xx_hw.o(i.usb_intr_config) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_hw.o(i.usb_intr_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f4xx_hw.o(i.usb_mdelay) refers to gd32f4xx_hw.o(i.hw_delay) for hw_delay
    gd32f4xx_hw.o(i.usb_rcu_config) refers to gd32f4xx_rcu.o(i.rcu_pll48m_clock_config) for rcu_pll48m_clock_config
    gd32f4xx_hw.o(i.usb_rcu_config) refers to gd32f4xx_rcu.o(i.rcu_ck48m_clock_config) for rcu_ck48m_clock_config
    gd32f4xx_hw.o(i.usb_rcu_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f4xx_hw.o(i.usb_timer_init) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_hw.o(i.usb_timer_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f4xx_hw.o(i.usb_timer_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f4xx_hw.o(i.usb_timer_irq) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_hw.o(i.usb_timer_irq) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    gd32f4xx_hw.o(i.usb_timer_irq) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    gd32f4xx_hw.o(i.usb_timer_irq) refers to gd32f4xx_hw.o(.data) for delay_time
    gd32f4xx_hw.o(i.usb_udelay) refers to gd32f4xx_hw.o(i.hw_delay) for hw_delay
    cdc_acm_core.o(i.cdc_acm_data_receive) refers to usbd_core.o(i.usbd_ep_recev) for usbd_ep_recev
    cdc_acm_core.o(i.cdc_acm_data_send) refers to usbd_core.o(i.usbd_ep_send) for usbd_ep_send
    cdc_acm_core.o(i.cdc_acm_deinit) refers to usbd_core.o(i.usbd_ep_clear) for usbd_ep_clear
    cdc_acm_core.o(i.cdc_acm_in) refers to usbd_core.o(i.usbd_ep_send) for usbd_ep_send
    cdc_acm_core.o(i.cdc_acm_init) refers to usbd_core.o(i.usbd_ep_setup) for usbd_ep_setup
    cdc_acm_core.o(i.cdc_acm_init) refers to cdc_acm_core.o(.constdata) for cdc_config_desc
    cdc_acm_core.o(i.cdc_acm_init) refers to cdc_acm_core.o(.bss) for cdc_handler
    cdc_acm_core.o(.constdata) refers to cdc_acm_core.o(.data) for serial_string
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(.constdata) for cdc_dev_desc
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_acm_init) for cdc_acm_init
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_acm_deinit) for cdc_acm_deinit
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_acm_req) for cdc_acm_req
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_ctlx_out) for cdc_ctlx_out
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_acm_in) for cdc_acm_in
    cdc_acm_core.o(.data) refers to cdc_acm_core.o(i.cdc_acm_out) for cdc_acm_out
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_gd32f450_470.o(HEAP), (12288 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_adc.o(i.adc_calibration_enable), (42 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_length_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_clock_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_data_alignment_config), (22 bytes).
    Removing gd32f4xx_adc.o(i.adc_deinit), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_enable), (18 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_config), (52 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_source_config), (48 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_channel_config), (172 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_special_function_config), (90 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_mode_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_regular_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_group_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (286 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_usbsof_signal_select), (28 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_disable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (88 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (68 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_bit_width_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (52 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_subperipheral_select), (38 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_deinit), (164 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_get), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_flow_controller_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_config), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_mode_init), (356 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_mode_init), (344 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_para_struct_init), (34 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_get), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (104 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (280 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (104 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ack_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_clock_config), (228 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_deinit), (88 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_clear), (40 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_get), (30 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_master_addressing), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_mode_addr_config), (28 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (56 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_current_time_get), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_bus_mode_set), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_config), (52 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_index_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_response_config), (56 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_config), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_read), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_transfer_config), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_write), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_deinit), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_set), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_response_get), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_wait_type_set), (28 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.qspi_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_disable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_enable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_receive_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_enable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_stop_bit_set), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_word_length_set), (16 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (56 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(.rrx_text), (6 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_it.o(.rrx_text), (6 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.RAM_D3), (1680 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing 25lc080a.o(.rev16_text), (4 bytes).
    Removing 25lc080a.o(.revsh_text), (4 bytes).
    Removing 25lc080a.o(.rrx_text), (6 bytes).
    Removing 25lc080a.o(i.EEPROM_WritePage), (730 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_RedeByte), (72 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_RedePage), (680 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_WriteByte), (556 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_WritePage), (44 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.ADC0_Init), (164 bytes).
    Removing adc.o(i.ADC1_Init), (164 bytes).
    Removing adc.o(i.ADC2_Init), (164 bytes).
    Removing adc.o(.RAM_D1), (2400 bytes).
    Removing adc.o(.RAM_D3), (4800 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.rrx_text), (6 bytes).
    Removing dac.o(i.DAC1_Set_Vol), (144 bytes).
    Removing dac.o(i.DAC2_Set_Vol), (152 bytes).
    Removing dac.o(i.DAC_Init), (96 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing dma.o(i.DMA1_CH0_Init), (140 bytes).
    Removing dma.o(i.DMA1_CH1_Init), (140 bytes).
    Removing dma.o(i.DMA1_CH2_Init), (140 bytes).
    Removing eeprom_spi.o(.rev16_text), (4 bytes).
    Removing eeprom_spi.o(.revsh_text), (4 bytes).
    Removing eeprom_spi.o(.rrx_text), (6 bytes).
    Removing eeprom_spi.o(i.EEPROM_ReadStatusRegister), (40 bytes).
    Removing eeprom_spi.o(i.EEPROM_SendByte), (68 bytes).
    Removing eeprom_spi.o(i.EEPROM_WriteStatusRegister), (60 bytes).
    Removing esp32_wifi.o(.rev16_text), (4 bytes).
    Removing esp32_wifi.o(.revsh_text), (4 bytes).
    Removing esp32_wifi.o(.rrx_text), (6 bytes).
    Removing esp32_wifi.o(i.AddCmdTaskToList), (122 bytes).
    Removing esp32_wifi.o(i.CheckCmdRepeatInList), (152 bytes).
    Removing esp32_wifi.o(i.ConversionSignalIntensity), (170 bytes).
    Removing esp32_wifi.o(i.DeletCurrCmdTaskFromList), (76 bytes).
    Removing esp32_wifi.o(i.ESP32BLE_SendData_Add), (148 bytes).
    Removing esp32_wifi.o(i.ESP32BLE_SendData_Read), (100 bytes).
    Removing esp32_wifi.o(i.ESP32_Heart_Beat), (124 bytes).
    Removing esp32_wifi.o(i.ESP32_SendData_Add), (160 bytes).
    Removing esp32_wifi.o(i.ESP32_SendData_Add_Plus), (172 bytes).
    Removing esp32_wifi.o(i.ESP32_SendData_Read), (106 bytes).
    Removing esp32_wifi.o(i.ESP32_SendData_Read_Plus), (116 bytes).
    Removing esp32_wifi.o(i.ESP_ATE0), (48 bytes).
    Removing esp32_wifi.o(i.ESP_ATE1), (48 bytes).
    Removing esp32_wifi.o(i.ESP_AT_TEST), (44 bytes).
    Removing esp32_wifi.o(i.ESP_BLEADVDATA), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEADVPARAM), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEADVSTART), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEADVSTOP), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEGATTSSRVCRE), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEGATTSSRVSTART), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLEINIT), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BLENAME), (48 bytes).
    Removing esp32_wifi.o(i.ESP_BuildNetConnect), (140 bytes).
    Removing esp32_wifi.o(i.ESP_CloseNetConnect), (64 bytes).
    Removing esp32_wifi.o(i.ESP_CloseWifiConnect), (48 bytes).
    Removing esp32_wifi.o(i.ESP_GetSNTP), (56 bytes).
    Removing esp32_wifi.o(i.ESP_GetSntpTimeInfo), (616 bytes).
    Removing esp32_wifi.o(i.ESP_GetTcpConnectStatus), (32 bytes).
    Removing esp32_wifi.o(i.ESP_GetWifiConnectStatus), (16 bytes).
    Removing esp32_wifi.o(i.ESP_Get_DomainIP), (48 bytes).
    Removing esp32_wifi.o(i.ESP_Init), (196 bytes).
    Removing esp32_wifi.o(i.ESP_OnReciveParseUsartData), (108 bytes).
    Removing esp32_wifi.o(i.ESP_QueryNetConnectStatus), (48 bytes).
    Removing esp32_wifi.o(i.ESP_QueryVersionInfo), (48 bytes).
    Removing esp32_wifi.o(i.ESP_QueryWifiInfo), (60 bytes).
    Removing esp32_wifi.o(i.ESP_QueryWifiInfoLists), (48 bytes).
    Removing esp32_wifi.o(i.ESP_QueryWorkMode), (48 bytes).
    Removing esp32_wifi.o(i.ESP_RESET), (48 bytes).
    Removing esp32_wifi.o(i.ESP_RESTORE), (48 bytes).
    Removing esp32_wifi.o(i.ESP_RFPOWER), (48 bytes).
    Removing esp32_wifi.o(i.ESP_RunTask), (32 bytes).
    Removing esp32_wifi.o(i.ESP_SET_FindApListRAM), (48 bytes).
    Removing esp32_wifi.o(i.ESP_SendBleData), (72 bytes).
    Removing esp32_wifi.o(i.ESP_SendData), (44 bytes).
    Removing esp32_wifi.o(i.ESP_SendNetData), (108 bytes).
    Removing esp32_wifi.o(i.ESP_SetAutoConn), (48 bytes).
    Removing esp32_wifi.o(i.ESP_SetNetDataRecv), (36 bytes).
    Removing esp32_wifi.o(i.ESP_SetSNTP_CFG), (48 bytes).
    Removing esp32_wifi.o(i.ESP_SetWifiConnect), (84 bytes).
    Removing esp32_wifi.o(i.ESP_SetWorkMode), (60 bytes).
    Removing esp32_wifi.o(i.ESP_StatusTask), (420 bytes).
    Removing esp32_wifi.o(i.ESP_XRFPOWER), (48 bytes).
    Removing esp32_wifi.o(i.InitBLESendDataRingBuf), (18 bytes).
    Removing esp32_wifi.o(i.InitCmdTaskToList), (32 bytes).
    Removing esp32_wifi.o(i.InitSendDataRingBuf), (18 bytes).
    Removing esp32_wifi.o(i.InitSendDataRingBufPlus), (18 bytes).
    Removing esp32_wifi.o(i.JudgeBLEConnectionStatus), (84 bytes).
    Removing esp32_wifi.o(i.JudgeNetConnectionStatus), (200 bytes).
    Removing esp32_wifi.o(i.JudgeWifiConnectionStatus), (104 bytes).
    Removing esp32_wifi.o(i.OnPackBLEADVDATA_Cmd), (104 bytes).
    Removing esp32_wifi.o(i.OnPackBLENAME_Cmd), (52 bytes).
    Removing esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd), (220 bytes).
    Removing esp32_wifi.o(i.OnPackCWModeCmd), (68 bytes).
    Removing esp32_wifi.o(i.OnPackJAPCmd), (92 bytes).
    Removing esp32_wifi.o(i.OnPackRFPOWERCmd), (96 bytes).
    Removing esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd), (164 bytes).
    Removing esp32_wifi.o(i.OnPackTCPOrUDPData), (10 bytes).
    Removing esp32_wifi.o(i.OnPackTCPOrUDPDataCmd), (88 bytes).
    Removing esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck), (228 bytes).
    Removing esp32_wifi.o(i.OnUnpackCWModeAck), (200 bytes).
    Removing esp32_wifi.o(i.OnUnpackDomainIPAck), (2 bytes).
    Removing esp32_wifi.o(i.OnUnpackGeneralAck), (156 bytes).
    Removing esp32_wifi.o(i.OnUnpackJAPAck), (364 bytes).
    Removing esp32_wifi.o(i.OnUnpackRSTAck), (164 bytes).
    Removing esp32_wifi.o(i.OnUnpackSntpTimeAck), (184 bytes).
    Removing esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck), (156 bytes).
    Removing esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck), (184 bytes).
    Removing esp32_wifi.o(i.OnUnpackVERSIONAck), (236 bytes).
    Removing esp32_wifi.o(i.OnUnpackyNetConnectStatusAck), (268 bytes).
    Removing esp32_wifi.o(i.ProcessAckOverTime), (76 bytes).
    Removing esp32_wifi.o(i.ProcessingBLEData), (76 bytes).
    Removing esp32_wifi.o(i.ProcessingNetworkData), (200 bytes).
    Removing esp32_wifi.o(i.ReadCmdInfoFromList), (52 bytes).
    Removing esp32_wifi.o(i.ReadCmdTaskCallFunFromList), (26 bytes).
    Removing esp32_wifi.o(i.SendCmdByTaskList), (236 bytes).
    Removing esp32_wifi.o(i.UserProcess_BLEADVSTARCmd), (64 bytes).
    Removing esp32_wifi.o(i.UserProcess_BLEADVSTOPCmd), (16 bytes).
    Removing esp32_wifi.o(i.UserProcess_JAPCmd), (24 bytes).
    Removing esp32_wifi.o(i.UserProcess_UpDataOkCmd), (20 bytes).
    Removing esp32_wifi.o(i.User_Netdata_process), (60 bytes).
    Removing esp32_wifi.o(i.XOnPackRFPOWERCmd), (32 bytes).
    Removing esp32_wifi.o(i.month_str2num), (60 bytes).
    Removing esp32_wifi.o(i.split), (82 bytes).
    Removing esp32_wifi.o(i.week_str2num), (60 bytes).
    Removing esp32_wifi.o(.RAM_D3), (36630 bytes).
    Removing esp32_wifi.o(.constdata), (8 bytes).
    Removing esp32_wifi.o(.conststring), (540 bytes).
    Removing esp32_wifi.o(.data), (960 bytes).
    Removing flash.o(.rev16_text), (4 bytes).
    Removing flash.o(.revsh_text), (4 bytes).
    Removing flash.o(.rrx_text), (6 bytes).
    Removing flash.o(i.FLASH_If_Init), (16 bytes).
    Removing flash.o(i.bsp_CmpCpuFlash), (68 bytes).
    Removing flash.o(i.bsp_EraseCpuFlash), (38 bytes).
    Removing flash.o(i.bsp_GetSector), (560 bytes).
    Removing flash.o(i.bsp_ReadCpuFlash), (44 bytes).
    Removing flash.o(i.bsp_ReadWordFlash), (6 bytes).
    Removing flash.o(i.bsp_WriteCpuFlash), (160 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing my_crc.o(.rev16_text), (4 bytes).
    Removing my_crc.o(.revsh_text), (4 bytes).
    Removing my_crc.o(.rrx_text), (6 bytes).
    Removing my_crc.o(i.CRC16_USB), (126 bytes).
    Removing my_crc.o(i.InvertUint16), (58 bytes).
    Removing my_crc.o(i.InvertUint8), (58 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(.rrx_text), (6 bytes).
    Removing rtc.o(i.rtc_register_set), (48 bytes).
    Removing rtc.o(.data), (2 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing time.o(.rev16_text), (4 bytes).
    Removing time.o(.revsh_text), (4 bytes).
    Removing time.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.ESP32_IO_Init), (36 bytes).
    Removing usart.o(i.FML_USART_Register), (80 bytes).
    Removing usart.o(i.InitBuffer), (46 bytes).
    Removing usart.o(i.UARTx_SendBuffer), (64 bytes).
    Removing usart.o(i.USART2_Init), (176 bytes).
    Removing usb.o(.rev16_text), (4 bytes).
    Removing usb.o(.revsh_text), (4 bytes).
    Removing usb.o(.rrx_text), (6 bytes).
    Removing usb.o(i.CDC_Command), (2 bytes).
    Removing usb.o(i.USB_RunTask), (2 bytes).
    Removing usb.o(.bss), (2444 bytes).
    Removing user_step.o(.rev16_text), (4 bytes).
    Removing user_step.o(.revsh_text), (4 bytes).
    Removing user_step.o(.rrx_text), (6 bytes).
    Removing user_step.o(i.ESP32BLE_Command_Add), (2 bytes).
    Removing user_step.o(i.ESP32BLE_Command_Read), (2 bytes).
    Removing user_step.o(i.ESP32_Command_Add), (2 bytes).
    Removing user_step.o(i.ESP32_Command_Add_Plus), (2 bytes).
    Removing user_step.o(i.ESP32_Command_Read), (2 bytes).
    Removing user_step.o(i.ESP32_Command_Read_Plus), (2 bytes).
    Removing user_step.o(i.Float2char), (2 bytes).
    Removing user_step.o(i.GetWifiInFo), (4 bytes).
    Removing user_step.o(i.Get_AlgorithmInFo), (4 bytes).
    Removing user_step.o(i.Get_DevID), (4 bytes).
    Removing user_step.o(i.Get_HardVer), (4 bytes).
    Removing user_step.o(i.Get_SeverInfo), (2 bytes).
    Removing user_step.o(i.Get_zhcdInFo), (4 bytes).
    Removing user_step.o(i.IPD_Command), (2 bytes).
    Removing user_step.o(i.SetWifiInFo), (4 bytes).
    Removing user_step.o(i.Set_AlgorithmInFo), (4 bytes).
    Removing user_step.o(i.Set_DevID), (4 bytes).
    Removing user_step.o(i.Set_SeverInfo), (6 bytes).
    Removing user_step.o(i.Sever_Buf2Info), (2 bytes).
    Removing user_step.o(i.Sever_Info2Buf), (2 bytes).
    Removing user_step.o(i.SysReset_Condition), (56 bytes).
    Removing user_step.o(i.User_Init), (2 bytes).
    Removing user_step.o(.NoInit), (4 bytes).
    Removing user_step.o(.bss), (9450 bytes).
    Removing user_step.o(.constdata), (5 bytes).
    Removing gd32f470v_start.o(.rev16_text), (4 bytes).
    Removing gd32f470v_start.o(.revsh_text), (4 bytes).
    Removing gd32f470v_start.o(.rrx_text), (6 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_init), (68 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_off), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_on), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_key_init), (128 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_init), (84 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_off), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_on), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_toggle), (24 bytes).
    Removing api_lan_data_process .o(.rev16_text), (4 bytes).
    Removing api_lan_data_process .o(.revsh_text), (4 bytes).
    Removing api_lan_data_process .o(.rrx_text), (6 bytes).
    Removing api_tnrg.o(.rev16_text), (4 bytes).
    Removing api_tnrg.o(.revsh_text), (4 bytes).
    Removing api_tnrg.o(.rrx_text), (6 bytes).
    Removing api_w5500.o(.rev16_text), (4 bytes).
    Removing api_w5500.o(.revsh_text), (4 bytes).
    Removing api_w5500.o(.rrx_text), (6 bytes).
    Removing api_w5500.o(i.API_SPI_SwapByte), (56 bytes).
    Removing drv_usb_core.o(.rev16_text), (4 bytes).
    Removing drv_usb_core.o(.revsh_text), (4 bytes).
    Removing drv_usb_core.o(.rrx_text), (6 bytes).
    Removing drv_usb_core.o(i.usb_basic_init), (244 bytes).
    Removing drv_usb_core.o(i.usb_core_init), (184 bytes).
    Removing drv_usb_core.o(i.usb_core_reset), (38 bytes).
    Removing drv_usb_core.o(i.usb_curmode_set), (46 bytes).
    Removing drv_usb_core.o(i.usb_rxfifo_flush), (34 bytes).
    Removing drv_usb_core.o(i.usb_set_txfifo), (72 bytes).
    Removing drv_usb_dev.o(.rev16_text), (4 bytes).
    Removing drv_usb_dev.o(.revsh_text), (4 bytes).
    Removing drv_usb_dev.o(.rrx_text), (6 bytes).
    Removing drv_usb_dev.o(i.usb_dev_stop), (84 bytes).
    Removing drv_usb_dev.o(i.usb_dev_suspend), (50 bytes).
    Removing drv_usb_dev.o(i.usb_devcore_init), (336 bytes).
    Removing drv_usb_dev.o(i.usb_devint_enable), (64 bytes).
    Removing drv_usb_dev.o(i.usb_rwkup_active), (72 bytes).
    Removing drv_usb_dev.o(i.usb_transc0_active), (92 bytes).
    Removing drv_usb_dev.o(i.usb_transc_deactivate), (100 bytes).
    Removing drv_usb_dev.o(.data), (8 bytes).
    Removing drv_usbd_int.o(.rev16_text), (4 bytes).
    Removing drv_usbd_int.o(.revsh_text), (4 bytes).
    Removing drv_usbd_int.o(.rrx_text), (6 bytes).
    Removing usbd_core.o(.rev16_text), (4 bytes).
    Removing usbd_core.o(.revsh_text), (4 bytes).
    Removing usbd_core.o(.rrx_text), (6 bytes).
    Removing usbd_core.o(i.usbd_connect), (28 bytes).
    Removing usbd_core.o(i.usbd_disconnect), (28 bytes).
    Removing usbd_core.o(i.usbd_ep_clear), (52 bytes).
    Removing usbd_core.o(i.usbd_ep_setup), (112 bytes).
    Removing usbd_core.o(i.usbd_fifo_flush), (36 bytes).
    Removing usbd_core.o(i.usbd_init), (124 bytes).
    Removing usbd_core.o(.constdata), (16 bytes).
    Removing usbd_enum.o(.rev16_text), (4 bytes).
    Removing usbd_enum.o(.revsh_text), (4 bytes).
    Removing usbd_enum.o(.rrx_text), (6 bytes).
    Removing usbd_enum.o(i.int_to_unicode), (60 bytes).
    Removing usbd_enum.o(i.serial_string_get), (76 bytes).
    Removing usbd_transc.o(.rev16_text), (4 bytes).
    Removing usbd_transc.o(.revsh_text), (4 bytes).
    Removing usbd_transc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_hw.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_hw.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_hw.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_hw.o(i.usb_gpio_config), (64 bytes).
    Removing gd32f4xx_hw.o(i.usb_intr_config), (22 bytes).
    Removing gd32f4xx_hw.o(i.usb_mdelay), (14 bytes).
    Removing gd32f4xx_hw.o(i.usb_rcu_config), (24 bytes).
    Removing gd32f4xx_hw.o(i.usb_timer_init), (30 bytes).
    Removing cdc_acm_core.o(.rev16_text), (4 bytes).
    Removing cdc_acm_core.o(.revsh_text), (4 bytes).
    Removing cdc_acm_core.o(.rrx_text), (6 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_check_ready), (34 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_data_receive), (28 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_data_send), (34 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_deinit), (34 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_in), (70 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_init), (96 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_out), (32 bytes).
    Removing cdc_acm_core.o(i.cdc_acm_req), (176 bytes).
    Removing cdc_acm_core.o(i.cdc_ctlx_out), (82 bytes).
    Removing cdc_acm_core.o(.constdata), (375 bytes).
    Removing cdc_acm_core.o(.data), (196 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfltui.o(.text), (26 bytes).
    Removing dfixui.o(.text), (50 bytes).
    Removing depilogue.o(.text), (186 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).

1187 unused section(s) (total 141792 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    Firmware\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s 0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    Firmware\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    Firmware\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    USBFS-object\\cdc_acm_core.c             0x00000000   Number         0  cdc_acm_core.o ABSOLUTE
    USBFS-object\\drv_usb_core.c             0x00000000   Number         0  drv_usb_core.o ABSOLUTE
    USBFS-object\\drv_usb_dev.c              0x00000000   Number         0  drv_usb_dev.o ABSOLUTE
    USBFS-object\\drv_usbd_int.c             0x00000000   Number         0  drv_usbd_int.o ABSOLUTE
    USBFS-object\\gd32f4xx_hw.c              0x00000000   Number         0  gd32f4xx_hw.o ABSOLUTE
    USBFS-object\\usbd_core.c                0x00000000   Number         0  usbd_core.o ABSOLUTE
    USBFS-object\\usbd_enum.c                0x00000000   Number         0  usbd_enum.o ABSOLUTE
    USBFS-object\\usbd_transc.c              0x00000000   Number         0  usbd_transc.o ABSOLUTE
    USBFS-object\cdc_acm_core.c              0x00000000   Number         0  cdc_acm_core.o ABSOLUTE
    USBFS-object\drv_usb_core.c              0x00000000   Number         0  drv_usb_core.o ABSOLUTE
    USBFS-object\drv_usb_dev.c               0x00000000   Number         0  drv_usb_dev.o ABSOLUTE
    USBFS-object\drv_usbd_int.c              0x00000000   Number         0  drv_usbd_int.o ABSOLUTE
    USBFS-object\gd32f4xx_hw.c               0x00000000   Number         0  gd32f4xx_hw.o ABSOLUTE
    USBFS-object\usbd_core.c                 0x00000000   Number         0  usbd_core.o ABSOLUTE
    USBFS-object\usbd_enum.c                 0x00000000   Number         0  usbd_enum.o ABSOLUTE
    USBFS-object\usbd_transc.c               0x00000000   Number         0  usbd_transc.o ABSOLUTE
    USER\\gd32f4xx_it.c                      0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    USER\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    USER\\systick.c                          0x00000000   Number         0  systick.o ABSOLUTE
    USER\gd32f4xx_it.c                       0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    USER\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    USER\systick.c                           0x00000000   Number         0  systick.o ABSOLUTE
    Utilities\25LC080A.c                     0x00000000   Number         0  25lc080a.o ABSOLUTE
    Utilities\API_LAN_DATA_Process .c        0x00000000   Number         0  api_lan_data_process .o ABSOLUTE
    Utilities\API_TNRG.c                     0x00000000   Number         0  api_tnrg.o ABSOLUTE
    Utilities\API_W5500.c                    0x00000000   Number         0  api_w5500.o ABSOLUTE
    Utilities\My_CRC.c                       0x00000000   Number         0  my_crc.o ABSOLUTE
    Utilities\\25LC080A.c                    0x00000000   Number         0  25lc080a.o ABSOLUTE
    Utilities\\API_LAN_DATA_Process .c       0x00000000   Number         0  api_lan_data_process .o ABSOLUTE
    Utilities\\API_TNRG.c                    0x00000000   Number         0  api_tnrg.o ABSOLUTE
    Utilities\\API_W5500.c                   0x00000000   Number         0  api_w5500.o ABSOLUTE
    Utilities\\My_CRC.c                      0x00000000   Number         0  my_crc.o ABSOLUTE
    Utilities\\adc.c                         0x00000000   Number         0  adc.o ABSOLUTE
    Utilities\\dac.c                         0x00000000   Number         0  dac.o ABSOLUTE
    Utilities\\dma.c                         0x00000000   Number         0  dma.o ABSOLUTE
    Utilities\\eeprom_spi.c                  0x00000000   Number         0  eeprom_spi.o ABSOLUTE
    Utilities\\esp32_wifi.c                  0x00000000   Number         0  esp32_wifi.o ABSOLUTE
    Utilities\\flash.c                       0x00000000   Number         0  flash.o ABSOLUTE
    Utilities\\gd32f470v_start.c             0x00000000   Number         0  gd32f470v_start.o ABSOLUTE
    Utilities\\gpio.c                        0x00000000   Number         0  gpio.o ABSOLUTE
    Utilities\\rtc.c                         0x00000000   Number         0  rtc.o ABSOLUTE
    Utilities\\spi.c                         0x00000000   Number         0  spi.o ABSOLUTE
    Utilities\\time.c                        0x00000000   Number         0  time.o ABSOLUTE
    Utilities\\usart.c                       0x00000000   Number         0  usart.o ABSOLUTE
    Utilities\\usb.c                         0x00000000   Number         0  usb.o ABSOLUTE
    Utilities\\user_step.c                   0x00000000   Number         0  user_step.o ABSOLUTE
    Utilities\adc.c                          0x00000000   Number         0  adc.o ABSOLUTE
    Utilities\dac.c                          0x00000000   Number         0  dac.o ABSOLUTE
    Utilities\dma.c                          0x00000000   Number         0  dma.o ABSOLUTE
    Utilities\eeprom_spi.c                   0x00000000   Number         0  eeprom_spi.o ABSOLUTE
    Utilities\esp32_wifi.c                   0x00000000   Number         0  esp32_wifi.o ABSOLUTE
    Utilities\flash.c                        0x00000000   Number         0  flash.o ABSOLUTE
    Utilities\gd32f470v_start.c              0x00000000   Number         0  gd32f470v_start.o ABSOLUTE
    Utilities\gpio.c                         0x00000000   Number         0  gpio.o ABSOLUTE
    Utilities\rtc.c                          0x00000000   Number         0  rtc.o ABSOLUTE
    Utilities\spi.c                          0x00000000   Number         0  spi.o ABSOLUTE
    Utilities\time.c                         0x00000000   Number         0  time.o ABSOLUTE
    Utilities\usart.c                        0x00000000   Number         0  usart.o ABSOLUTE
    Utilities\usb.c                          0x00000000   Number         0  usb.o ABSOLUTE
    Utilities\user_step.c                    0x00000000   Number         0  user_step.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08040000   Section      428  startup_gd32f450_470.o(RESET)
    .ARM.Collect$$$$00000000                 0x080401ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080401ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080401b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080401b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080401b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080401b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080401bc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080401c0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080401c0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080401c0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080401c0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080401c4   Section       36  startup_gd32f450_470.o(.text)
    $v0                                      0x080401c4   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x080401e8   Section        0  memcpya.o(.text)
    .text                                    0x0804020c   Section        0  memseta.o(.text)
    .text                                    0x08040230   Section        0  uldiv.o(.text)
    .text                                    0x08040292   Section        0  llushr.o(.text)
    .text                                    0x080402b4   Section       36  init.o(.text)
    .text                                    0x080402d8   Section        0  llshl.o(.text)
    .text                                    0x080402f6   Section        0  __dczerorl2.o(.text)
    i.ADC_ConvCpltCallback                   0x0804034c   Section        0  adc.o(i.ADC_ConvCpltCallback)
    i.ADC_ConvHalfCpltCallback               0x080403ec   Section        0  adc.o(i.ADC_ConvHalfCpltCallback)
    i.API_Chose_TS5A3359_GAIN                0x0804048c   Section        0  gpio.o(i.API_Chose_TS5A3359_GAIN)
    i.API_Detect_Gateway                     0x080404f4   Section        0  api_w5500.o(i.API_Detect_Gateway)
    i.API_Init_LAN                           0x08040608   Section        0  api_w5500.o(i.API_Init_LAN)
    i.API_Init_Net_Parameters                0x08040674   Section        0  api_w5500.o(i.API_Init_Net_Parameters)
    i.API_Printf_Hex                         0x08040930   Section        0  usart.o(i.API_Printf_Hex)
    i.API_Process_Socket_Data                0x08040960   Section        0  api_w5500.o(i.API_Process_Socket_Data)
    i.API_RNG_Init                           0x0804099c   Section        0  api_tnrg.o(i.API_RNG_Init)
    i.API_Read_SOCK_Data_Buffer              0x08040b0c   Section        0  api_w5500.o(i.API_Read_SOCK_Data_Buffer)
    i.API_Read_W5500_1Byte                   0x08040c00   Section        0  api_w5500.o(i.API_Read_W5500_1Byte)
    i.API_Read_W5500_SOCK_1Byte              0x08040c30   Section        0  api_w5500.o(i.API_Read_W5500_SOCK_1Byte)
    i.API_Read_W5500_SOCK_2Byte              0x08040c6c   Section        0  api_w5500.o(i.API_Read_W5500_SOCK_2Byte)
    i.API_SPI0_Read_Byte                     0x08040cb8   Section        0  api_w5500.o(i.API_SPI0_Read_Byte)
    i.API_SPI0_Send_Byte                     0x08040cec   Section        0  api_w5500.o(i.API_SPI0_Send_Byte)
    i.API_SPI0_Send_Short                    0x08040d20   Section        0  api_w5500.o(i.API_SPI0_Send_Short)
    i.API_Socket_Connect                     0x08040d3c   Section        0  api_w5500.o(i.API_Socket_Connect)
    i.API_Socket_Init                        0x08040d84   Section        0  api_w5500.o(i.API_Socket_Init)
    i.API_Socket_Listen                      0x08040df4   Section        0  api_w5500.o(i.API_Socket_Listen)
    i.API_Socket_UDP                         0x08040e5a   Section        0  api_w5500.o(i.API_Socket_UDP)
    i.API_W5500_1MS_RunTask                  0x08040e98   Section        0  api_w5500.o(i.API_W5500_1MS_RunTask)
    i.API_W5500_GPIO_Init                    0x08040ef4   Section        0  api_w5500.o(i.API_W5500_GPIO_Init)
    i.API_W5500_HardWare_Rest                0x08040f1c   Section        0  api_w5500.o(i.API_W5500_HardWare_Rest)
    i.API_W5500_Interrupt_Process            0x08040fb8   Section        0  api_w5500.o(i.API_W5500_Interrupt_Process)
    i.API_W5500_ReciveDATA_Handle            0x0804114c   Section        0  api_w5500.o(i.API_W5500_ReciveDATA_Handle)
    i.API_W5500_Register_Init                0x0804117c   Section        0  api_w5500.o(i.API_W5500_Register_Init)
    i.API_W5500_SPI0_Init                    0x080411f4   Section        0  api_w5500.o(i.API_W5500_SPI0_Init)
    i.API_W5500_Send_Data_S0                 0x080412bc   Section        0  api_w5500.o(i.API_W5500_Send_Data_S0)
    i.API_W5500_Socket_Set                   0x08041310   Section        0  api_w5500.o(i.API_W5500_Socket_Set)
    i.API_Write_SOCK_Data_Buffer             0x080413b8   Section        0  api_w5500.o(i.API_Write_SOCK_Data_Buffer)
    i.API_Write_W5500_1Byte                  0x080414d4   Section        0  api_w5500.o(i.API_Write_W5500_1Byte)
    i.API_Write_W5500_2Byte                  0x08041504   Section        0  api_w5500.o(i.API_Write_W5500_2Byte)
    i.API_Write_W5500_SOCK_1Byte             0x08041534   Section        0  api_w5500.o(i.API_Write_W5500_SOCK_1Byte)
    i.API_Write_W5500_SOCK_2Byte             0x08041570   Section        0  api_w5500.o(i.API_Write_W5500_SOCK_2Byte)
    i.API_Write_W5500_SOCK_4Byte             0x080415ac   Section        0  api_w5500.o(i.API_Write_W5500_SOCK_4Byte)
    i.API_Write_W5500_nByte                  0x08041600   Section        0  api_w5500.o(i.API_Write_W5500_nByte)
    i.AddByteToBuffer                        0x08041644   Section        0  usart.o(i.AddByteToBuffer)
    AddByteToBuffer                          0x08041645   Thumb Code   110  usart.o(i.AddByteToBuffer)
    i.BusFault_Handler                       0x080416b2   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Channel0_IRQHandler               0x080416b8   Section        0  gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler)
    i.DMA1_Channel1_IRQHandler               0x080416ec   Section        0  gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA1_Channel2_IRQHandler               0x08041720   Section        0  gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler)
    i.DRV_SPI_SwapByte                       0x08041754   Section        0  spi.o(i.DRV_SPI_SwapByte)
    i.DebugMon_Handler                       0x0804178c   Section        0  gd32f4xx_it.o(i.DebugMon_Handler)
    i.EEPROM_SPI_ReadBuffer                  0x08041790   Section        0  eeprom_spi.o(i.EEPROM_SPI_ReadBuffer)
    i.EEPROM_SPI_SendInstruction             0x080417fc   Section        0  eeprom_spi.o(i.EEPROM_SPI_SendInstruction)
    i.EEPROM_SPI_WaitStandbyState            0x08041830   Section        0  eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState)
    i.EEPROM_SPI_WriteBuffer                 0x0804187c   Section        0  eeprom_spi.o(i.EEPROM_SPI_WriteBuffer)
    i.EEPROM_SPI_WritePage                   0x08041a08   Section        0  eeprom_spi.o(i.EEPROM_SPI_WritePage)
    i.EEPROM_WriteDisable                    0x08041abc   Section        0  eeprom_spi.o(i.EEPROM_WriteDisable)
    i.EEPROM_WriteEnable                     0x08041aec   Section        0  eeprom_spi.o(i.EEPROM_WriteEnable)
    i.EXTI10_15_IRQHandler                   0x08041b1c   Section        0  gd32f4xx_it.o(i.EXTI10_15_IRQHandler)
    i.FML_USART_RecvTask                     0x08041b3c   Section        0  usart.o(i.FML_USART_RecvTask)
    i.GPIO_Init                              0x08041bf4   Section        0  gpio.o(i.GPIO_Init)
    i.HardFault_Handler                      0x08041c34   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.Init_GPIO_TS5A339                      0x08041c38   Section        0  gpio.o(i.Init_GPIO_TS5A339)
    i.MemManage_Handler                      0x08041c70   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08041c74   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08041c76   Section        0  gd32f4xx_it.o(i.PendSV_Handler)
    i.RTC_Init                               0x08041c78   Section        0  rtc.o(i.RTC_Init)
    i.ReadBytesToBuffer                      0x08041cdc   Section        0  usart.o(i.ReadBytesToBuffer)
    ReadBytesToBuffer                        0x08041cdd   Thumb Code   142  usart.o(i.ReadBytesToBuffer)
    i.RecvDataHandler                        0x08041d6a   Section        0  usart.o(i.RecvDataHandler)
    RecvDataHandler                          0x08041d6b   Thumb Code    30  usart.o(i.RecvDataHandler)
    i.SPI1_Init                              0x08041d88   Section        0  spi.o(i.SPI1_Init)
    i.SVC_Handler                            0x08041e24   Section        0  gd32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08041e26   Section        0  gd32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08041e30   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.TIMER1_Init                            0x08041ef8   Section        0  time.o(i.TIMER1_Init)
    i.TIMER2_IRQHandler                      0x08041f6e   Section        0  gd32f4xx_it.o(i.TIMER2_IRQHandler)
    i.TIMER3_IRQHandler                      0x08041f78   Section        0  gd32f4xx_it.o(i.TIMER3_IRQHandler)
    i.TIMER3_Init                            0x08041f90   Section        0  time.o(i.TIMER3_Init)
    i.TIMER6_IRQHandler                      0x08041fe4   Section        0  gd32f4xx_it.o(i.TIMER6_IRQHandler)
    i.TIMER6_Init                            0x08041ffc   Section        0  time.o(i.TIMER6_Init)
    i.TIM_PeriodElapsedCallback              0x08042050   Section        0  time.o(i.TIM_PeriodElapsedCallback)
    i.UART_IDLECallBack                      0x08042224   Section        0  usart.o(i.UART_IDLECallBack)
    i.UART_RxCpltCallback                    0x0804224c   Section        0  usart.o(i.UART_RxCpltCallback)
    i.USART0_Init                            0x08042278   Section        0  usart.o(i.USART0_Init)
    i.USART2_IRQHandler                      0x08042300   Section        0  gd32f4xx_it.o(i.USART2_IRQHandler)
    i.USBFS_IRQHandler                       0x08042330   Section        0  gd32f4xx_it.o(i.USBFS_IRQHandler)
    i.USBFS_WKUP_IRQHandler                  0x08042340   Section        0  gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler)
    i.UsageFault_Handler                     0x08042374   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i.__0printf$8                            0x08042378   Section        0  printf8.o(i.__0printf$8)
    i.__NVIC_SetPriority                     0x08042398   Section        0  systick.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08042399   Thumb Code    32  systick.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x080423c0   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080423ce   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080423d0   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x080423e0   Section        0  printf8.o(i._printf_core)
    _printf_core                             0x080423e1   Thumb Code   984  printf8.o(i._printf_core)
    i._printf_post_padding                   0x080427e4   Section        0  printf8.o(i._printf_post_padding)
    _printf_post_padding                     0x080427e5   Thumb Code    36  printf8.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08042808   Section        0  printf8.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08042809   Thumb Code    46  printf8.o(i._printf_pre_padding)
    i._usb_bos_desc_get                      0x08042836   Section        0  usbd_enum.o(i._usb_bos_desc_get)
    _usb_bos_desc_get                        0x08042837   Thumb Code    20  usbd_enum.o(i._usb_bos_desc_get)
    i._usb_config_desc_get                   0x0804284a   Section        0  usbd_enum.o(i._usb_config_desc_get)
    _usb_config_desc_get                     0x0804284b   Thumb Code    34  usbd_enum.o(i._usb_config_desc_get)
    i._usb_dev_desc_get                      0x0804286c   Section        0  usbd_enum.o(i._usb_dev_desc_get)
    _usb_dev_desc_get                        0x0804286d   Thumb Code    20  usbd_enum.o(i._usb_dev_desc_get)
    i._usb_std_clearfeature                  0x08042880   Section        0  usbd_enum.o(i._usb_std_clearfeature)
    _usb_std_clearfeature                    0x08042881   Thumb Code   116  usbd_enum.o(i._usb_std_clearfeature)
    i._usb_std_getconfiguration              0x080428f4   Section        0  usbd_enum.o(i._usb_std_getconfiguration)
    _usb_std_getconfiguration                0x080428f5   Thumb Code    64  usbd_enum.o(i._usb_std_getconfiguration)
    i._usb_std_getdescriptor                 0x08042934   Section        0  usbd_enum.o(i._usb_std_getdescriptor)
    _usb_std_getdescriptor                   0x08042935   Thumb Code   272  usbd_enum.o(i._usb_std_getdescriptor)
    i._usb_std_getinterface                  0x08042a48   Section        0  usbd_enum.o(i._usb_std_getinterface)
    _usb_std_getinterface                    0x08042a49   Thumb Code    60  usbd_enum.o(i._usb_std_getinterface)
    i._usb_std_getstatus                     0x08042a84   Section        0  usbd_enum.o(i._usb_std_getstatus)
    _usb_std_getstatus                       0x08042a85   Thumb Code   192  usbd_enum.o(i._usb_std_getstatus)
    i._usb_std_reserved                      0x08042b48   Section        0  usbd_enum.o(i._usb_std_reserved)
    _usb_std_reserved                        0x08042b49   Thumb Code     6  usbd_enum.o(i._usb_std_reserved)
    i._usb_std_setaddress                    0x08042b4e   Section        0  usbd_enum.o(i._usb_std_setaddress)
    _usb_std_setaddress                      0x08042b4f   Thumb Code    92  usbd_enum.o(i._usb_std_setaddress)
    i._usb_std_setconfiguration              0x08042bac   Section        0  usbd_enum.o(i._usb_std_setconfiguration)
    _usb_std_setconfiguration                0x08042bad   Thumb Code   176  usbd_enum.o(i._usb_std_setconfiguration)
    i._usb_std_setdescriptor                 0x08042c60   Section        0  usbd_enum.o(i._usb_std_setdescriptor)
    _usb_std_setdescriptor                   0x08042c61   Thumb Code     6  usbd_enum.o(i._usb_std_setdescriptor)
    i._usb_std_setfeature                    0x08042c66   Section        0  usbd_enum.o(i._usb_std_setfeature)
    _usb_std_setfeature                      0x08042c67   Thumb Code   104  usbd_enum.o(i._usb_std_setfeature)
    i._usb_std_setinterface                  0x08042cce   Section        0  usbd_enum.o(i._usb_std_setinterface)
    _usb_std_setinterface                    0x08042ccf   Thumb Code    68  usbd_enum.o(i._usb_std_setinterface)
    i._usb_std_synchframe                    0x08042d12   Section        0  usbd_enum.o(i._usb_std_synchframe)
    _usb_std_synchframe                      0x08042d13   Thumb Code     6  usbd_enum.o(i._usb_std_synchframe)
    i._usb_str_desc_get                      0x08042d18   Section        0  usbd_enum.o(i._usb_str_desc_get)
    _usb_str_desc_get                        0x08042d19   Thumb Code    20  usbd_enum.o(i._usb_str_desc_get)
    i.delay_1ms                              0x08042d2c   Section        0  systick.o(i.delay_1ms)
    i.delay_decrement                        0x08042d40   Section        0  systick.o(i.delay_decrement)
    i.dma_interrupt_flag_clear               0x08042d58   Section        0  gd32f4xx_dma.o(i.dma_interrupt_flag_clear)
    i.dma_interrupt_flag_get                 0x08042d96   Section        0  gd32f4xx_dma.o(i.dma_interrupt_flag_get)
    i.exti_interrupt_flag_clear              0x08042f9c   Section        0  gd32f4xx_exti.o(i.exti_interrupt_flag_clear)
    i.exti_interrupt_flag_get                0x08042fa8   Section        0  gd32f4xx_exti.o(i.exti_interrupt_flag_get)
    i.fputc                                  0x08042fcc   Section        0  usart.o(i.fputc)
    i.gd_eval_key_state_get                  0x08042ff0   Section        0  gd32f470v_start.o(i.gd_eval_key_state_get)
    i.get_hard_rand_data                     0x08043010   Section        0  api_tnrg.o(i.get_hard_rand_data)
    i.gpio_af_set                            0x08043024   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x08043082   Section        0  gd32f4xx_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x08043086   Section        0  gd32f4xx_gpio.o(i.gpio_bit_set)
    i.gpio_input_bit_get                     0x0804308a   Section        0  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x0804309a   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x080430e8   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.hw_delay                               0x0804312c   Section        0  gd32f4xx_hw.o(i.hw_delay)
    hw_delay                                 0x0804312d   Thumb Code    34  gd32f4xx_hw.o(i.hw_delay)
    i.hw_time_set                            0x08043158   Section        0  gd32f4xx_hw.o(i.hw_time_set)
    hw_time_set                              0x08043159   Thumb Code   104  gd32f4xx_hw.o(i.hw_time_set)
    i.main                                   0x080431c8   Section        0  main.o(i.main)
    i.nvic_irq_enable                        0x08043234   Section        0  gd32f4xx_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_set                0x080432f8   Section        0  gd32f4xx_misc.o(i.nvic_priority_group_set)
    i.nvic_vector_table_set                  0x0804330c   Section        0  gd32f4xx_misc.o(i.nvic_vector_table_set)
    i.pmu_backup_write_enable                0x08043324   Section        0  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    i.pmu_to_deepsleepmode                   0x08043338   Section        0  gd32f4xx_pmu.o(i.pmu_to_deepsleepmode)
    i.rcu_ck48m_clock_config                 0x0804342c   Section        0  gd32f4xx_rcu.o(i.rcu_ck48m_clock_config)
    i.rcu_clock_freq_get                     0x08043444   Section        0  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    i.rcu_flag_get                           0x08043568   Section        0  gd32f4xx_rcu.o(i.rcu_flag_get)
    i.rcu_osci_on                            0x0804358c   Section        0  gd32f4xx_rcu.o(i.rcu_osci_on)
    i.rcu_osci_stab_wait                     0x080435b0   Section        0  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    i.rcu_periph_clock_enable                0x0804370c   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x08043730   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x08043754   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.rcu_pll48m_clock_config                0x08043778   Section        0  gd32f4xx_rcu.o(i.rcu_pll48m_clock_config)
    i.rcu_rtc_clock_config                   0x08043790   Section        0  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    i.rcu_system_clock_source_config         0x080437a8   Section        0  gd32f4xx_rcu.o(i.rcu_system_clock_source_config)
    i.rcu_system_clock_source_get            0x080437c0   Section        0  gd32f4xx_rcu.o(i.rcu_system_clock_source_get)
    i.resume_mcu_clk                         0x080437d0   Section        0  gd32f4xx_it.o(i.resume_mcu_clk)
    resume_mcu_clk                           0x080437d1   Thumb Code    56  gd32f4xx_it.o(i.resume_mcu_clk)
    i.rtc_init                               0x08043808   Section        0  gd32f4xx_rtc.o(i.rtc_init)
    i.rtc_init_mode_enter                    0x080438cc   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    i.rtc_init_mode_exit                     0x08043914   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    i.rtc_register_sync_wait                 0x08043928   Section        0  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    i.spi_enable                             0x08043988   Section        0  gd32f4xx_spi.o(i.spi_enable)
    i.spi_i2s_data_receive                   0x08043992   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    i.spi_i2s_data_transmit                  0x0804399a   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    i.spi_i2s_flag_get                       0x0804399e   Section        0  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    i.spi_init                               0x080439ae   Section        0  gd32f4xx_spi.o(i.spi_init)
    i.system_clock_168m_25m_hxtal            0x080439e0   Section        0  system_gd32f4xx.o(i.system_clock_168m_25m_hxtal)
    system_clock_168m_25m_hxtal              0x080439e1   Thumb Code   240  system_gd32f4xx.o(i.system_clock_168m_25m_hxtal)
    i.system_clock_config                    0x08043adc   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x08043add   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.systick_config                         0x08043ae4   Section        0  systick.o(i.systick_config)
    i.timer_auto_reload_shadow_enable        0x08043b34   Section        0  gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable)
    i.timer_channel_output_config            0x08043b40   Section        0  gd32f4xx_timer.o(i.timer_channel_output_config)
    i.timer_channel_output_mode_config       0x08043d2c   Section        0  gd32f4xx_timer.o(i.timer_channel_output_mode_config)
    i.timer_channel_output_pulse_value_config 0x08043d86   Section        0  gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config)
    i.timer_channel_output_shadow_config     0x08043dac   Section        0  gd32f4xx_timer.o(i.timer_channel_output_shadow_config)
    i.timer_deinit                           0x08043e08   Section        0  gd32f4xx_timer.o(i.timer_deinit)
    i.timer_disable                          0x08043f8c   Section        0  gd32f4xx_timer.o(i.timer_disable)
    i.timer_enable                           0x08043f96   Section        0  gd32f4xx_timer.o(i.timer_enable)
    i.timer_init                             0x08043fa0   Section        0  gd32f4xx_timer.o(i.timer_init)
    i.timer_interrupt_disable                0x08044038   Section        0  gd32f4xx_timer.o(i.timer_interrupt_disable)
    i.timer_interrupt_enable                 0x08044040   Section        0  gd32f4xx_timer.o(i.timer_interrupt_enable)
    i.timer_interrupt_flag_clear             0x08044048   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    i.timer_interrupt_flag_get               0x0804404e   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    i.trng_deinit                            0x08044066   Section        0  gd32f4xx_trng.o(i.trng_deinit)
    i.trng_enable                            0x0804407c   Section        0  gd32f4xx_trng.o(i.trng_enable)
    i.trng_flag_get                          0x08044090   Section        0  gd32f4xx_trng.o(i.trng_flag_get)
    i.trng_get_true_random_data              0x080440a8   Section        0  gd32f4xx_trng.o(i.trng_get_true_random_data)
    i.usart_baudrate_set                     0x080440b4   Section        0  gd32f4xx_usart.o(i.usart_baudrate_set)
    i.usart_data_receive                     0x0804419c   Section        0  gd32f4xx_usart.o(i.usart_data_receive)
    i.usart_data_transmit                    0x080441a6   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x080441b0   Section        0  gd32f4xx_usart.o(i.usart_deinit)
    i.usart_enable                           0x0804428c   Section        0  gd32f4xx_usart.o(i.usart_enable)
    i.usart_flag_get                         0x08044296   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.usart_interrupt_flag_clear             0x080442b4   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    i.usart_interrupt_flag_get               0x080442ce   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    i.usart_receive_config                   0x08044306   Section        0  gd32f4xx_usart.o(i.usart_receive_config)
    i.usart_transmit_config                  0x08044316   Section        0  gd32f4xx_usart.o(i.usart_transmit_config)
    i.usb_clock_active                       0x08044326   Section        0  drv_usb_dev.o(i.usb_clock_active)
    i.usb_ctlep_startout                     0x08044348   Section        0  drv_usb_dev.o(i.usb_ctlep_startout)
    i.usb_iepintr_read                       0x08044370   Section        0  drv_usb_dev.o(i.usb_iepintr_read)
    i.usb_rxfifo_read                        0x0804439a   Section        0  drv_usb_core.o(i.usb_rxfifo_read)
    i.usb_timer_irq                          0x080443b8   Section        0  gd32f4xx_hw.o(i.usb_timer_irq)
    i.usb_transc_active                      0x080443f0   Section        0  drv_usb_dev.o(i.usb_transc_active)
    i.usb_transc_clrstall                    0x08044488   Section        0  drv_usb_dev.o(i.usb_transc_clrstall)
    i.usb_transc_inxfer                      0x080444cc   Section        0  drv_usb_dev.o(i.usb_transc_inxfer)
    i.usb_transc_outxfer                     0x080445d8   Section        0  drv_usb_dev.o(i.usb_transc_outxfer)
    i.usb_transc_stall                       0x08044668   Section        0  drv_usb_dev.o(i.usb_transc_stall)
    i.usb_txfifo_flush                       0x080446aa   Section        0  drv_usb_core.o(i.usb_txfifo_flush)
    i.usb_txfifo_write                       0x080446d2   Section        0  drv_usb_core.o(i.usb_txfifo_write)
    i.usb_udelay                             0x080446f4   Section        0  gd32f4xx_hw.o(i.usb_udelay)
    i.usbd_class_request                     0x08044702   Section        0  usbd_enum.o(i.usbd_class_request)
    i.usbd_ctl_recev                         0x08044728   Section        0  usbd_transc.o(i.usbd_ctl_recev)
    i.usbd_ctl_send                          0x08044756   Section        0  usbd_transc.o(i.usbd_ctl_send)
    i.usbd_ctl_status_recev                  0x08044784   Section        0  usbd_transc.o(i.usbd_ctl_status_recev)
    i.usbd_ctl_status_send                   0x080447a4   Section        0  usbd_transc.o(i.usbd_ctl_status_send)
    i.usbd_emptytxfifo_write                 0x080447c4   Section        0  drv_usbd_int.o(i.usbd_emptytxfifo_write)
    usbd_emptytxfifo_write                   0x080447c5   Thumb Code   140  drv_usbd_int.o(i.usbd_emptytxfifo_write)
    i.usbd_enum_error                        0x08044850   Section        0  usbd_enum.o(i.usbd_enum_error)
    i.usbd_ep_recev                          0x0804486e   Section        0  usbd_core.o(i.usbd_ep_recev)
    i.usbd_ep_send                           0x080448aa   Section        0  usbd_core.o(i.usbd_ep_send)
    i.usbd_ep_stall                          0x080448e6   Section        0  usbd_core.o(i.usbd_ep_stall)
    i.usbd_ep_stall_clear                    0x08044920   Section        0  usbd_core.o(i.usbd_ep_stall_clear)
    i.usbd_in_transc                         0x0804495a   Section        0  usbd_transc.o(i.usbd_in_transc)
    i.usbd_int_enumfinish                    0x080449fc   Section        0  drv_usbd_int.o(i.usbd_int_enumfinish)
    usbd_int_enumfinish                      0x080449fd   Thumb Code    98  drv_usbd_int.o(i.usbd_int_enumfinish)
    i.usbd_int_epin                          0x08044a64   Section        0  drv_usbd_int.o(i.usbd_int_epin)
    usbd_int_epin                            0x08044a65   Thumb Code   138  drv_usbd_int.o(i.usbd_int_epin)
    i.usbd_int_epout                         0x08044aee   Section        0  drv_usbd_int.o(i.usbd_int_epout)
    usbd_int_epout                           0x08044aef   Thumb Code   206  drv_usbd_int.o(i.usbd_int_epout)
    i.usbd_int_reset                         0x08044bbc   Section        0  drv_usbd_int.o(i.usbd_int_reset)
    usbd_int_reset                           0x08044bbd   Thumb Code   208  drv_usbd_int.o(i.usbd_int_reset)
    i.usbd_int_rxfifo                        0x08044c94   Section        0  drv_usbd_int.o(i.usbd_int_rxfifo)
    usbd_int_rxfifo                          0x08044c95   Thumb Code   182  drv_usbd_int.o(i.usbd_int_rxfifo)
    i.usbd_int_suspend                       0x08044d50   Section        0  drv_usbd_int.o(i.usbd_int_suspend)
    usbd_int_suspend                         0x08044d51   Thumb Code   104  drv_usbd_int.o(i.usbd_int_suspend)
    i.usbd_isr                               0x08044db8   Section        0  drv_usbd_int.o(i.usbd_isr)
    i.usbd_out_transc                        0x08044e96   Section        0  usbd_transc.o(i.usbd_out_transc)
    i.usbd_setup_transc                      0x08044f1a   Section        0  usbd_transc.o(i.usbd_setup_transc)
    i.usbd_standard_request                  0x08044fa0   Section        0  usbd_enum.o(i.usbd_standard_request)
    i.usbd_vendor_request                    0x08044fbc   Section        0  usbd_enum.o(i.usbd_vendor_request)
    .constdata                               0x08044fc2   Section        4  drv_usb_dev.o(.constdata)
    EP0_MAXLEN                               0x08044fc2   Data           4  drv_usb_dev.o(.constdata)
    .constdata                               0x08044fc8   Section       60  drv_usbd_int.o(.constdata)
    USB_SPEED                                0x08044fc8   Data           4  drv_usbd_int.o(.constdata)
    <Data1>                                  0x08044fcc   Data          28  drv_usbd_int.o(.constdata)
    <Data2>                                  0x08044fe8   Data          28  drv_usbd_int.o(.constdata)
    .data                                    0x20000004   Section        4  system_gd32f4xx.o(.data)
    .data                                    0x20000008   Section       15  main.o(.data)
    .data                                    0x20000018   Section        4  systick.o(.data)
    delay                                    0x20000018   Data           4  systick.o(.data)
    .data                                    0x2000001c   Section        1  adc.o(.data)
    .data                                    0x20000020   Section        6  time.o(.data)
    .data                                    0x20000026   Section        1  usb.o(.data)
    .data                                    0x20000028   Section       62  gd32f470v_start.o(.data)
    GPIO_PORT                                0x20000028   Data          12  gd32f470v_start.o(.data)
    GPIO_PIN                                 0x20000034   Data          12  gd32f470v_start.o(.data)
    GPIO_CLK                                 0x20000040   Data           6  gd32f470v_start.o(.data)
    KEY_PORT                                 0x20000048   Data           4  gd32f470v_start.o(.data)
    KEY_PIN                                  0x2000004c   Data           4  gd32f470v_start.o(.data)
    KEY_CLK                                  0x20000050   Data           2  gd32f470v_start.o(.data)
    KEY_EXTI_LINE                            0x20000054   Data           4  gd32f470v_start.o(.data)
    KEY_PORT_SOURCE                          0x20000058   Data           1  gd32f470v_start.o(.data)
    KEY_PIN_SOURCE                           0x20000059   Data           1  gd32f470v_start.o(.data)
    KEY_IRQn                                 0x2000005a   Data           1  gd32f470v_start.o(.data)
    BEEP_PORT                                0x2000005c   Data           4  gd32f470v_start.o(.data)
    BEEP_GPIO_PIN                            0x20000060   Data           4  gd32f470v_start.o(.data)
    BEEP_CLK                                 0x20000064   Data           2  gd32f470v_start.o(.data)
    .data                                    0x20000066   Section        2  api_w5500.o(.data)
    Temp_Cnt                                 0x20000066   Data           2  api_w5500.o(.data)
    .data                                    0x20000068   Section       67  usbd_enum.o(.data)
    _std_dev_req                             0x20000068   Data          52  usbd_enum.o(.data)
    std_desc_get                             0x2000009c   Data          12  usbd_enum.o(.data)
    status                                   0x200000a8   Data           2  usbd_enum.o(.data)
    config                                   0x200000aa   Data           1  usbd_enum.o(.data)
    .data                                    0x200000ac   Section        8  gd32f4xx_hw.o(.data)
    .data                                    0x200000b4   Section        4  stdout.o(.data)
    .bss                                     0x200000b8   Section       16  gd32f4xx_pmu.o(.bss)
    reg_snap                                 0x200000b8   Data          16  gd32f4xx_pmu.o(.bss)
    .bss                                     0x200000c8   Section       20  rtc.o(.bss)
    .bss                                     0x200000dc   Section       20  time.o(.bss)
    .bss                                     0x200000f0   Section     1172  cdc_acm_core.o(.bss)
    cdc_handler                              0x2000052c   Data          88  cdc_acm_core.o(.bss)
    STACK                                    0x20000588   Section    20480  startup_gd32f450_470.o(STACK)
    .RAM_D3                                  0x20020000   Section     6167  usart.o(.RAM_D3)
    .RAM_D3                                  0x20021818   Section     2208  api_w5500.o(.RAM_D3)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08040000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080401ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080401ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080401ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080401b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080401b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080401b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080401b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080401b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080401bd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080401c1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080401c1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080401c5   Thumb Code     8  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SDIO_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART0_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART1_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    __aeabi_memcpy                           0x080401e9   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x080401e9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x080401e9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0804020d   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0804020d   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0804020d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0804021b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0804021b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0804021b   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0804021f   Thumb Code    18  memseta.o(.text)
    __aeabi_uldivmod                         0x08040231   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsr                             0x08040293   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08040293   Thumb Code     0  llushr.o(.text)
    __scatterload                            0x080402b5   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080402b5   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x080402d9   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080402d9   Thumb Code     0  llshl.o(.text)
    __decompress                             0x080402f7   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x080402f7   Thumb Code    86  __dczerorl2.o(.text)
    ADC_ConvCpltCallback                     0x0804034d   Thumb Code   138  adc.o(i.ADC_ConvCpltCallback)
    ADC_ConvHalfCpltCallback                 0x080403ed   Thumb Code   138  adc.o(i.ADC_ConvHalfCpltCallback)
    API_Chose_TS5A3359_GAIN                  0x0804048d   Thumb Code    98  gpio.o(i.API_Chose_TS5A3359_GAIN)
    API_Detect_Gateway                       0x080404f5   Thumb Code   212  api_w5500.o(i.API_Detect_Gateway)
    API_Init_LAN                             0x08040609   Thumb Code    58  api_w5500.o(i.API_Init_LAN)
    API_Init_Net_Parameters                  0x08040675   Thumb Code   516  api_w5500.o(i.API_Init_Net_Parameters)
    API_Printf_Hex                           0x08040931   Thumb Code    34  usart.o(i.API_Printf_Hex)
    API_Process_Socket_Data                  0x08040961   Thumb Code    30  api_w5500.o(i.API_Process_Socket_Data)
    API_RNG_Init                             0x0804099d   Thumb Code   288  api_tnrg.o(i.API_RNG_Init)
    API_Read_SOCK_Data_Buffer                0x08040b0d   Thumb Code   238  api_w5500.o(i.API_Read_SOCK_Data_Buffer)
    API_Read_W5500_1Byte                     0x08040c01   Thumb Code    42  api_w5500.o(i.API_Read_W5500_1Byte)
    API_Read_W5500_SOCK_1Byte                0x08040c31   Thumb Code    54  api_w5500.o(i.API_Read_W5500_SOCK_1Byte)
    API_Read_W5500_SOCK_2Byte                0x08040c6d   Thumb Code    70  api_w5500.o(i.API_Read_W5500_SOCK_2Byte)
    API_SPI0_Read_Byte                       0x08040cb9   Thumb Code    48  api_w5500.o(i.API_SPI0_Read_Byte)
    API_SPI0_Send_Byte                       0x08040ced   Thumb Code    48  api_w5500.o(i.API_SPI0_Send_Byte)
    API_SPI0_Send_Short                      0x08040d21   Thumb Code    28  api_w5500.o(i.API_SPI0_Send_Short)
    API_Socket_Connect                       0x08040d3d   Thumb Code    70  api_w5500.o(i.API_Socket_Connect)
    API_Socket_Init                          0x08040d85   Thumb Code   106  api_w5500.o(i.API_Socket_Init)
    API_Socket_Listen                        0x08040df5   Thumb Code   102  api_w5500.o(i.API_Socket_Listen)
    API_Socket_UDP                           0x08040e5b   Thumb Code    60  api_w5500.o(i.API_Socket_UDP)
    API_W5500_1MS_RunTask                    0x08040e99   Thumb Code    82  api_w5500.o(i.API_W5500_1MS_RunTask)
    API_W5500_GPIO_Init                      0x08040ef5   Thumb Code    36  api_w5500.o(i.API_W5500_GPIO_Init)
    API_W5500_HardWare_Rest                  0x08040f1d   Thumb Code   106  api_w5500.o(i.API_W5500_HardWare_Rest)
    API_W5500_Interrupt_Process              0x08040fb9   Thumb Code   246  api_w5500.o(i.API_W5500_Interrupt_Process)
    API_W5500_ReciveDATA_Handle              0x0804114d   Thumb Code    42  api_w5500.o(i.API_W5500_ReciveDATA_Handle)
    API_W5500_Register_Init                  0x0804117d   Thumb Code   114  api_w5500.o(i.API_W5500_Register_Init)
    API_W5500_SPI0_Init                      0x080411f5   Thumb Code   184  api_w5500.o(i.API_W5500_SPI0_Init)
    API_W5500_Send_Data_S0                   0x080412bd   Thumb Code    58  api_w5500.o(i.API_W5500_Send_Data_S0)
    API_W5500_Socket_Set                     0x08041311   Thumb Code   132  api_w5500.o(i.API_W5500_Socket_Set)
    API_Write_SOCK_Data_Buffer               0x080413b9   Thumb Code   278  api_w5500.o(i.API_Write_SOCK_Data_Buffer)
    API_Write_W5500_1Byte                    0x080414d5   Thumb Code    42  api_w5500.o(i.API_Write_W5500_1Byte)
    API_Write_W5500_2Byte                    0x08041505   Thumb Code    42  api_w5500.o(i.API_Write_W5500_2Byte)
    API_Write_W5500_SOCK_1Byte               0x08041535   Thumb Code    54  api_w5500.o(i.API_Write_W5500_SOCK_1Byte)
    API_Write_W5500_SOCK_2Byte               0x08041571   Thumb Code    54  api_w5500.o(i.API_Write_W5500_SOCK_2Byte)
    API_Write_W5500_SOCK_4Byte               0x080415ad   Thumb Code    80  api_w5500.o(i.API_Write_W5500_SOCK_4Byte)
    API_Write_W5500_nByte                    0x08041601   Thumb Code    62  api_w5500.o(i.API_Write_W5500_nByte)
    BusFault_Handler                         0x080416b3   Thumb Code     4  gd32f4xx_it.o(i.BusFault_Handler)
    DMA1_Channel0_IRQHandler                 0x080416b9   Thumb Code    42  gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler)
    DMA1_Channel1_IRQHandler                 0x080416ed   Thumb Code    42  gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler)
    DMA1_Channel2_IRQHandler                 0x08041721   Thumb Code    42  gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler)
    DRV_SPI_SwapByte                         0x08041755   Thumb Code    50  spi.o(i.DRV_SPI_SwapByte)
    DebugMon_Handler                         0x0804178d   Thumb Code     2  gd32f4xx_it.o(i.DebugMon_Handler)
    EEPROM_SPI_ReadBuffer                    0x08041791   Thumb Code    98  eeprom_spi.o(i.EEPROM_SPI_ReadBuffer)
    EEPROM_SPI_SendInstruction               0x080417fd   Thumb Code    46  eeprom_spi.o(i.EEPROM_SPI_SendInstruction)
    EEPROM_SPI_WaitStandbyState              0x08041831   Thumb Code    66  eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState)
    EEPROM_SPI_WriteBuffer                   0x0804187d   Thumb Code   394  eeprom_spi.o(i.EEPROM_SPI_WriteBuffer)
    EEPROM_SPI_WritePage                     0x08041a09   Thumb Code   170  eeprom_spi.o(i.EEPROM_SPI_WritePage)
    EEPROM_WriteDisable                      0x08041abd   Thumb Code    38  eeprom_spi.o(i.EEPROM_WriteDisable)
    EEPROM_WriteEnable                       0x08041aed   Thumb Code    38  eeprom_spi.o(i.EEPROM_WriteEnable)
    EXTI10_15_IRQHandler                     0x08041b1d   Thumb Code    26  gd32f4xx_it.o(i.EXTI10_15_IRQHandler)
    FML_USART_RecvTask                       0x08041b3d   Thumb Code   174  usart.o(i.FML_USART_RecvTask)
    GPIO_Init                                0x08041bf5   Thumb Code    60  gpio.o(i.GPIO_Init)
    HardFault_Handler                        0x08041c35   Thumb Code     4  gd32f4xx_it.o(i.HardFault_Handler)
    Init_GPIO_TS5A339                        0x08041c39   Thumb Code    52  gpio.o(i.Init_GPIO_TS5A339)
    MemManage_Handler                        0x08041c71   Thumb Code     4  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08041c75   Thumb Code     2  gd32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08041c77   Thumb Code     2  gd32f4xx_it.o(i.PendSV_Handler)
    RTC_Init                                 0x08041c79   Thumb Code    96  rtc.o(i.RTC_Init)
    SPI1_Init                                0x08041d89   Thumb Code   146  spi.o(i.SPI1_Init)
    SVC_Handler                              0x08041e25   Thumb Code     2  gd32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08041e27   Thumb Code     8  gd32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08041e31   Thumb Code   184  system_gd32f4xx.o(i.SystemInit)
    TIMER1_Init                              0x08041ef9   Thumb Code   118  time.o(i.TIMER1_Init)
    TIMER2_IRQHandler                        0x08041f6f   Thumb Code     8  gd32f4xx_it.o(i.TIMER2_IRQHandler)
    TIMER3_IRQHandler                        0x08041f79   Thumb Code    20  gd32f4xx_it.o(i.TIMER3_IRQHandler)
    TIMER3_Init                              0x08041f91   Thumb Code    80  time.o(i.TIMER3_Init)
    TIMER6_IRQHandler                        0x08041fe5   Thumb Code    20  gd32f4xx_it.o(i.TIMER6_IRQHandler)
    TIMER6_Init                              0x08041ffd   Thumb Code    80  time.o(i.TIMER6_Init)
    TIM_PeriodElapsedCallback                0x08042051   Thumb Code   446  time.o(i.TIM_PeriodElapsedCallback)
    UART_IDLECallBack                        0x08042225   Thumb Code    30  usart.o(i.UART_IDLECallBack)
    UART_RxCpltCallback                      0x0804224d   Thumb Code    30  usart.o(i.UART_RxCpltCallback)
    USART0_Init                              0x08042279   Thumb Code   126  usart.o(i.USART0_Init)
    USART2_IRQHandler                        0x08042301   Thumb Code    36  gd32f4xx_it.o(i.USART2_IRQHandler)
    USBFS_IRQHandler                         0x08042331   Thumb Code    10  gd32f4xx_it.o(i.USBFS_IRQHandler)
    USBFS_WKUP_IRQHandler                    0x08042341   Thumb Code    48  gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler)
    UsageFault_Handler                       0x08042375   Thumb Code     4  gd32f4xx_it.o(i.UsageFault_Handler)
    __0printf$8                              0x08042379   Thumb Code    22  printf8.o(i.__0printf$8)
    __1printf$8                              0x08042379   Thumb Code     0  printf8.o(i.__0printf$8)
    __2printf                                0x08042379   Thumb Code     0  printf8.o(i.__0printf$8)
    __scatterload_copy                       0x080423c1   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080423cf   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080423d1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    delay_1ms                                0x08042d2d   Thumb Code    16  systick.o(i.delay_1ms)
    delay_decrement                          0x08042d41   Thumb Code    18  systick.o(i.delay_decrement)
    dma_interrupt_flag_clear                 0x08042d59   Thumb Code    62  gd32f4xx_dma.o(i.dma_interrupt_flag_clear)
    dma_interrupt_flag_get                   0x08042d97   Thumb Code   516  gd32f4xx_dma.o(i.dma_interrupt_flag_get)
    exti_interrupt_flag_clear                0x08042f9d   Thumb Code     6  gd32f4xx_exti.o(i.exti_interrupt_flag_clear)
    exti_interrupt_flag_get                  0x08042fa9   Thumb Code    32  gd32f4xx_exti.o(i.exti_interrupt_flag_get)
    fputc                                    0x08042fcd   Thumb Code    32  usart.o(i.fputc)
    gd_eval_key_state_get                    0x08042ff1   Thumb Code    22  gd32f470v_start.o(i.gd_eval_key_state_get)
    get_hard_rand_data                       0x08043011   Thumb Code    20  api_tnrg.o(i.get_hard_rand_data)
    gpio_af_set                              0x08043025   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x08043083   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x08043087   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_set)
    gpio_input_bit_get                       0x0804308b   Thumb Code    16  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x0804309b   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x080430e9   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    main                                     0x080431c9   Thumb Code   100  main.o(i.main)
    nvic_irq_enable                          0x08043235   Thumb Code   186  gd32f4xx_misc.o(i.nvic_irq_enable)
    nvic_priority_group_set                  0x080432f9   Thumb Code    10  gd32f4xx_misc.o(i.nvic_priority_group_set)
    nvic_vector_table_set                    0x0804330d   Thumb Code    16  gd32f4xx_misc.o(i.nvic_vector_table_set)
    pmu_backup_write_enable                  0x08043325   Thumb Code    14  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    pmu_to_deepsleepmode                     0x08043339   Thumb Code   204  gd32f4xx_pmu.o(i.pmu_to_deepsleepmode)
    rcu_ck48m_clock_config                   0x0804342d   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_ck48m_clock_config)
    rcu_clock_freq_get                       0x08043445   Thumb Code   264  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    rcu_flag_get                             0x08043569   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_flag_get)
    rcu_osci_on                              0x0804358d   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_osci_on)
    rcu_osci_stab_wait                       0x080435b1   Thumb Code   342  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    rcu_periph_clock_enable                  0x0804370d   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x08043731   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08043755   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    rcu_pll48m_clock_config                  0x08043779   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_pll48m_clock_config)
    rcu_rtc_clock_config                     0x08043791   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    rcu_system_clock_source_config           0x080437a9   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_system_clock_source_config)
    rcu_system_clock_source_get              0x080437c1   Thumb Code    10  gd32f4xx_rcu.o(i.rcu_system_clock_source_get)
    rtc_init                                 0x08043809   Thumb Code   190  gd32f4xx_rtc.o(i.rtc_init)
    rtc_init_mode_enter                      0x080438cd   Thumb Code    66  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    rtc_init_mode_exit                       0x08043915   Thumb Code    14  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    rtc_register_sync_wait                   0x08043929   Thumb Code    92  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    spi_enable                               0x08043989   Thumb Code    10  gd32f4xx_spi.o(i.spi_enable)
    spi_i2s_data_receive                     0x08043993   Thumb Code     8  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    spi_i2s_data_transmit                    0x0804399b   Thumb Code     4  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    spi_i2s_flag_get                         0x0804399f   Thumb Code    16  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    spi_init                                 0x080439af   Thumb Code    50  gd32f4xx_spi.o(i.spi_init)
    systick_config                           0x08043ae5   Thumb Code    74  systick.o(i.systick_config)
    timer_auto_reload_shadow_enable          0x08043b35   Thumb Code    10  gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable)
    timer_channel_output_config              0x08043b41   Thumb Code   484  gd32f4xx_timer.o(i.timer_channel_output_config)
    timer_channel_output_mode_config         0x08043d2d   Thumb Code    90  gd32f4xx_timer.o(i.timer_channel_output_mode_config)
    timer_channel_output_pulse_value_config  0x08043d87   Thumb Code    38  gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config)
    timer_channel_output_shadow_config       0x08043dad   Thumb Code    90  gd32f4xx_timer.o(i.timer_channel_output_shadow_config)
    timer_deinit                             0x08043e09   Thumb Code   374  gd32f4xx_timer.o(i.timer_deinit)
    timer_disable                            0x08043f8d   Thumb Code    10  gd32f4xx_timer.o(i.timer_disable)
    timer_enable                             0x08043f97   Thumb Code    10  gd32f4xx_timer.o(i.timer_enable)
    timer_init                               0x08043fa1   Thumb Code   122  gd32f4xx_timer.o(i.timer_init)
    timer_interrupt_disable                  0x08044039   Thumb Code     8  gd32f4xx_timer.o(i.timer_interrupt_disable)
    timer_interrupt_enable                   0x08044041   Thumb Code     8  gd32f4xx_timer.o(i.timer_interrupt_enable)
    timer_interrupt_flag_clear               0x08044049   Thumb Code     6  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    timer_interrupt_flag_get                 0x0804404f   Thumb Code    24  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    trng_deinit                              0x08044067   Thumb Code    20  gd32f4xx_trng.o(i.trng_deinit)
    trng_enable                              0x0804407d   Thumb Code    14  gd32f4xx_trng.o(i.trng_enable)
    trng_flag_get                            0x08044091   Thumb Code    18  gd32f4xx_trng.o(i.trng_flag_get)
    trng_get_true_random_data                0x080440a9   Thumb Code     6  gd32f4xx_trng.o(i.trng_get_true_random_data)
    usart_baudrate_set                       0x080440b5   Thumb Code   224  gd32f4xx_usart.o(i.usart_baudrate_set)
    usart_data_receive                       0x0804419d   Thumb Code    10  gd32f4xx_usart.o(i.usart_data_receive)
    usart_data_transmit                      0x080441a7   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_deinit                             0x080441b1   Thumb Code   210  gd32f4xx_usart.o(i.usart_deinit)
    usart_enable                             0x0804428d   Thumb Code    10  gd32f4xx_usart.o(i.usart_enable)
    usart_flag_get                           0x08044297   Thumb Code    30  gd32f4xx_usart.o(i.usart_flag_get)
    usart_interrupt_flag_clear               0x080442b5   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    usart_interrupt_flag_get                 0x080442cf   Thumb Code    56  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    usart_receive_config                     0x08044307   Thumb Code    16  gd32f4xx_usart.o(i.usart_receive_config)
    usart_transmit_config                    0x08044317   Thumb Code    16  gd32f4xx_usart.o(i.usart_transmit_config)
    usb_clock_active                         0x08044327   Thumb Code    32  drv_usb_dev.o(i.usb_clock_active)
    usb_ctlep_startout                       0x08044349   Thumb Code    34  drv_usb_dev.o(i.usb_ctlep_startout)
    usb_iepintr_read                         0x08044371   Thumb Code    42  drv_usb_dev.o(i.usb_iepintr_read)
    usb_rxfifo_read                          0x0804439b   Thumb Code    30  drv_usb_core.o(i.usb_rxfifo_read)
    usb_timer_irq                            0x080443b9   Thumb Code    46  gd32f4xx_hw.o(i.usb_timer_irq)
    usb_transc_active                        0x080443f1   Thumb Code   142  drv_usb_dev.o(i.usb_transc_active)
    usb_transc_clrstall                      0x08044489   Thumb Code    68  drv_usb_dev.o(i.usb_transc_clrstall)
    usb_transc_inxfer                        0x080444cd   Thumb Code   268  drv_usb_dev.o(i.usb_transc_inxfer)
    usb_transc_outxfer                       0x080445d9   Thumb Code   144  drv_usb_dev.o(i.usb_transc_outxfer)
    usb_transc_stall                         0x08044669   Thumb Code    66  drv_usb_dev.o(i.usb_transc_stall)
    usb_txfifo_flush                         0x080446ab   Thumb Code    40  drv_usb_core.o(i.usb_txfifo_flush)
    usb_txfifo_write                         0x080446d3   Thumb Code    34  drv_usb_core.o(i.usb_txfifo_write)
    usb_udelay                               0x080446f5   Thumb Code    14  gd32f4xx_hw.o(i.usb_udelay)
    usbd_class_request                       0x08044703   Thumb Code    38  usbd_enum.o(i.usbd_class_request)
    usbd_ctl_recev                           0x08044729   Thumb Code    46  usbd_transc.o(i.usbd_ctl_recev)
    usbd_ctl_send                            0x08044757   Thumb Code    46  usbd_transc.o(i.usbd_ctl_send)
    usbd_ctl_status_recev                    0x08044785   Thumb Code    32  usbd_transc.o(i.usbd_ctl_status_recev)
    usbd_ctl_status_send                     0x080447a5   Thumb Code    32  usbd_transc.o(i.usbd_ctl_status_send)
    usbd_enum_error                          0x08044851   Thumb Code    30  usbd_enum.o(i.usbd_enum_error)
    usbd_ep_recev                            0x0804486f   Thumb Code    60  usbd_core.o(i.usbd_ep_recev)
    usbd_ep_send                             0x080448ab   Thumb Code    60  usbd_core.o(i.usbd_ep_send)
    usbd_ep_stall                            0x080448e7   Thumb Code    58  usbd_core.o(i.usbd_ep_stall)
    usbd_ep_stall_clear                      0x08044921   Thumb Code    58  usbd_core.o(i.usbd_ep_stall_clear)
    usbd_in_transc                           0x0804495b   Thumb Code   160  usbd_transc.o(i.usbd_in_transc)
    usbd_isr                                 0x08044db9   Thumb Code   222  drv_usbd_int.o(i.usbd_isr)
    usbd_out_transc                          0x08044e97   Thumb Code   132  usbd_transc.o(i.usbd_out_transc)
    usbd_setup_transc                        0x08044f1b   Thumb Code   132  usbd_transc.o(i.usbd_setup_transc)
    usbd_standard_request                    0x08044fa1   Thumb Code    22  usbd_enum.o(i.usbd_standard_request)
    usbd_vendor_request                      0x08044fbd   Thumb Code     6  usbd_enum.o(i.usbd_vendor_request)
    Region$$Table$$Base                      0x08045004   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08045034   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000004   Data           4  system_gd32f4xx.o(.data)
    TeseBuff                                 0x20000008   Data          15  main.o(.data)
    my_adcdma                                0x2000001c   Data           1  adc.o(.data)
    tim6_msTic                               0x20000020   Data           4  time.o(.data)
    TempTestTimer                            0x20000024   Data           2  time.o(.data)
    flag_USB_Rx                              0x20000026   Data           1  usb.o(.data)
    delay_time                               0x200000ac   Data           4  gd32f4xx_hw.o(.data)
    timer_prescaler                          0x200000b0   Data           4  gd32f4xx_hw.o(.data)
    __stdout                                 0x200000b4   Data           4  stdout.o(.data)
    rtc_initpara                             0x200000c8   Data          20  rtc.o(.bss)
    g_tTimeSign                              0x200000dc   Data           9  time.o(.bss)
    my_key                                   0x200000e6   Data          10  time.o(.bss)
    cdc_acm                                  0x200000f0   Data        1084  cdc_acm_core.o(.bss)
    __initial_sp                             0x20005588   Data           0  startup_gd32f450_470.o(STACK)
    sg_tUsartDriveHandle                     0x20020000   Data        2068  usart.o(.RAM_D3)
    sg_arrUasrt2RecvBuf                      0x20020814   Data        2049  usart.o(.RAM_D3)
    tmpBuf_test                              0x20021015   Data        2049  usart.o(.RAM_D3)
    usart2_buf                               0x20021816   Data           1  usart.o(.RAM_D3)
    Lan_Para                                 0x20021818   Data        2208  api_w5500.o(.RAM_D3)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080401ad

  Load Region LR_IROM1 (Base: 0x08040000, Size: 0x000071a0, Max: 0x000c0000, ABSOLUTE, COMPRESSED[0x0000512c])

    Execution Region ER_IROM1 (Exec base: 0x08040000, Load base: 0x08040000, Size: 0x00005034, Max: 0x000c0000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08040000   0x08040000   0x000001ac   Data   RO            3    RESET               startup_gd32f450_470.o
    0x080401ac   0x080401ac   0x00000000   Code   RO         8703  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080401ac   0x080401ac   0x00000004   Code   RO         9023    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080401b0   0x080401b0   0x00000004   Code   RO         9026    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080401b4   0x080401b4   0x00000000   Code   RO         9028    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080401b4   0x080401b4   0x00000000   Code   RO         9030    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080401b4   0x080401b4   0x00000008   Code   RO         9031    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080401bc   0x080401bc   0x00000004   Code   RO         9038    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080401c0   0x080401c0   0x00000000   Code   RO         9033    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080401c0   0x080401c0   0x00000000   Code   RO         9035    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080401c0   0x080401c0   0x00000004   Code   RO         9024    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080401c4   0x080401c4   0x00000024   Code   RO            4    .text               startup_gd32f450_470.o
    0x080401e8   0x080401e8   0x00000024   Code   RO         8706    .text               mc_w.l(memcpya.o)
    0x0804020c   0x0804020c   0x00000024   Code   RO         8708    .text               mc_w.l(memseta.o)
    0x08040230   0x08040230   0x00000062   Code   RO         9042    .text               mc_w.l(uldiv.o)
    0x08040292   0x08040292   0x00000020   Code   RO         9044    .text               mc_w.l(llushr.o)
    0x080402b2   0x080402b2   0x00000002   PAD
    0x080402b4   0x080402b4   0x00000024   Code   RO         9066    .text               mc_w.l(init.o)
    0x080402d8   0x080402d8   0x0000001e   Code   RO         9068    .text               mc_w.l(llshl.o)
    0x080402f6   0x080402f6   0x00000056   Code   RO         9112    .text               mc_w.l(__dczerorl2.o)
    0x0804034c   0x0804034c   0x000000a0   Code   RO         6204    i.ADC_ConvCpltCallback  adc.o
    0x080403ec   0x080403ec   0x000000a0   Code   RO         6205    i.ADC_ConvHalfCpltCallback  adc.o
    0x0804048c   0x0804048c   0x00000068   Code   RO         7072    i.API_Chose_TS5A3359_GAIN  gpio.o
    0x080404f4   0x080404f4   0x00000114   Code   RO         7727    i.API_Detect_Gateway  api_w5500.o
    0x08040608   0x08040608   0x0000006c   Code   RO         7728    i.API_Init_LAN      api_w5500.o
    0x08040674   0x08040674   0x000002bc   Code   RO         7729    i.API_Init_Net_Parameters  api_w5500.o
    0x08040930   0x08040930   0x00000030   Code   RO         7279    i.API_Printf_Hex    usart.o
    0x08040960   0x08040960   0x0000003c   Code   RO         7730    i.API_Process_Socket_Data  api_w5500.o
    0x0804099c   0x0804099c   0x00000170   Code   RO         7689    i.API_RNG_Init      api_tnrg.o
    0x08040b0c   0x08040b0c   0x000000f4   Code   RO         7731    i.API_Read_SOCK_Data_Buffer  api_w5500.o
    0x08040c00   0x08040c00   0x00000030   Code   RO         7732    i.API_Read_W5500_1Byte  api_w5500.o
    0x08040c30   0x08040c30   0x0000003c   Code   RO         7733    i.API_Read_W5500_SOCK_1Byte  api_w5500.o
    0x08040c6c   0x08040c6c   0x0000004c   Code   RO         7734    i.API_Read_W5500_SOCK_2Byte  api_w5500.o
    0x08040cb8   0x08040cb8   0x00000034   Code   RO         7735    i.API_SPI0_Read_Byte  api_w5500.o
    0x08040cec   0x08040cec   0x00000034   Code   RO         7736    i.API_SPI0_Send_Byte  api_w5500.o
    0x08040d20   0x08040d20   0x0000001c   Code   RO         7737    i.API_SPI0_Send_Short  api_w5500.o
    0x08040d3c   0x08040d3c   0x00000046   Code   RO         7739    i.API_Socket_Connect  api_w5500.o
    0x08040d82   0x08040d82   0x00000002   PAD
    0x08040d84   0x08040d84   0x00000070   Code   RO         7740    i.API_Socket_Init   api_w5500.o
    0x08040df4   0x08040df4   0x00000066   Code   RO         7741    i.API_Socket_Listen  api_w5500.o
    0x08040e5a   0x08040e5a   0x0000003c   Code   RO         7742    i.API_Socket_UDP    api_w5500.o
    0x08040e96   0x08040e96   0x00000002   PAD
    0x08040e98   0x08040e98   0x0000005c   Code   RO         7743    i.API_W5500_1MS_RunTask  api_w5500.o
    0x08040ef4   0x08040ef4   0x00000028   Code   RO         7744    i.API_W5500_GPIO_Init  api_w5500.o
    0x08040f1c   0x08040f1c   0x0000009c   Code   RO         7745    i.API_W5500_HardWare_Rest  api_w5500.o
    0x08040fb8   0x08040fb8   0x00000194   Code   RO         7746    i.API_W5500_Interrupt_Process  api_w5500.o
    0x0804114c   0x0804114c   0x00000030   Code   RO         7747    i.API_W5500_ReciveDATA_Handle  api_w5500.o
    0x0804117c   0x0804117c   0x00000078   Code   RO         7748    i.API_W5500_Register_Init  api_w5500.o
    0x080411f4   0x080411f4   0x000000c8   Code   RO         7749    i.API_W5500_SPI0_Init  api_w5500.o
    0x080412bc   0x080412bc   0x00000054   Code   RO         7750    i.API_W5500_Send_Data_S0  api_w5500.o
    0x08041310   0x08041310   0x000000a8   Code   RO         7751    i.API_W5500_Socket_Set  api_w5500.o
    0x080413b8   0x080413b8   0x0000011c   Code   RO         7752    i.API_Write_SOCK_Data_Buffer  api_w5500.o
    0x080414d4   0x080414d4   0x00000030   Code   RO         7753    i.API_Write_W5500_1Byte  api_w5500.o
    0x08041504   0x08041504   0x00000030   Code   RO         7754    i.API_Write_W5500_2Byte  api_w5500.o
    0x08041534   0x08041534   0x0000003c   Code   RO         7755    i.API_Write_W5500_SOCK_1Byte  api_w5500.o
    0x08041570   0x08041570   0x0000003c   Code   RO         7756    i.API_Write_W5500_SOCK_2Byte  api_w5500.o
    0x080415ac   0x080415ac   0x00000054   Code   RO         7757    i.API_Write_W5500_SOCK_4Byte  api_w5500.o
    0x08041600   0x08041600   0x00000044   Code   RO         7758    i.API_Write_W5500_nByte  api_w5500.o
    0x08041644   0x08041644   0x0000006e   Code   RO         7280    i.AddByteToBuffer   usart.o
    0x080416b2   0x080416b2   0x00000004   Code   RO         5781    i.BusFault_Handler  gd32f4xx_it.o
    0x080416b6   0x080416b6   0x00000002   PAD
    0x080416b8   0x080416b8   0x00000034   Code   RO         5782    i.DMA1_Channel0_IRQHandler  gd32f4xx_it.o
    0x080416ec   0x080416ec   0x00000034   Code   RO         5783    i.DMA1_Channel1_IRQHandler  gd32f4xx_it.o
    0x08041720   0x08041720   0x00000034   Code   RO         5784    i.DMA1_Channel2_IRQHandler  gd32f4xx_it.o
    0x08041754   0x08041754   0x00000038   Code   RO         7184    i.DRV_SPI_SwapByte  spi.o
    0x0804178c   0x0804178c   0x00000002   Code   RO         5785    i.DebugMon_Handler  gd32f4xx_it.o
    0x0804178e   0x0804178e   0x00000002   PAD
    0x08041790   0x08041790   0x0000006c   Code   RO         6351    i.EEPROM_SPI_ReadBuffer  eeprom_spi.o
    0x080417fc   0x080417fc   0x00000034   Code   RO         6352    i.EEPROM_SPI_SendInstruction  eeprom_spi.o
    0x08041830   0x08041830   0x0000004c   Code   RO         6353    i.EEPROM_SPI_WaitStandbyState  eeprom_spi.o
    0x0804187c   0x0804187c   0x0000018a   Code   RO         6354    i.EEPROM_SPI_WriteBuffer  eeprom_spi.o
    0x08041a06   0x08041a06   0x00000002   PAD
    0x08041a08   0x08041a08   0x000000b4   Code   RO         6355    i.EEPROM_SPI_WritePage  eeprom_spi.o
    0x08041abc   0x08041abc   0x00000030   Code   RO         6357    i.EEPROM_WriteDisable  eeprom_spi.o
    0x08041aec   0x08041aec   0x00000030   Code   RO         6358    i.EEPROM_WriteEnable  eeprom_spi.o
    0x08041b1c   0x08041b1c   0x00000020   Code   RO         5786    i.EXTI10_15_IRQHandler  gd32f4xx_it.o
    0x08041b3c   0x08041b3c   0x000000b8   Code   RO         7282    i.FML_USART_RecvTask  usart.o
    0x08041bf4   0x08041bf4   0x00000040   Code   RO         7073    i.GPIO_Init         gpio.o
    0x08041c34   0x08041c34   0x00000004   Code   RO         5787    i.HardFault_Handler  gd32f4xx_it.o
    0x08041c38   0x08041c38   0x00000038   Code   RO         7074    i.Init_GPIO_TS5A339  gpio.o
    0x08041c70   0x08041c70   0x00000004   Code   RO         5788    i.MemManage_Handler  gd32f4xx_it.o
    0x08041c74   0x08041c74   0x00000002   Code   RO         5789    i.NMI_Handler       gd32f4xx_it.o
    0x08041c76   0x08041c76   0x00000002   Code   RO         5790    i.PendSV_Handler    gd32f4xx_it.o
    0x08041c78   0x08041c78   0x00000064   Code   RO         7142    i.RTC_Init          rtc.o
    0x08041cdc   0x08041cdc   0x0000008e   Code   RO         7285    i.ReadBytesToBuffer  usart.o
    0x08041d6a   0x08041d6a   0x0000001e   Code   RO         7286    i.RecvDataHandler   usart.o
    0x08041d88   0x08041d88   0x0000009c   Code   RO         7185    i.SPI1_Init         spi.o
    0x08041e24   0x08041e24   0x00000002   Code   RO         5791    i.SVC_Handler       gd32f4xx_it.o
    0x08041e26   0x08041e26   0x00000008   Code   RO         5792    i.SysTick_Handler   gd32f4xx_it.o
    0x08041e2e   0x08041e2e   0x00000002   PAD
    0x08041e30   0x08041e30   0x000000c8   Code   RO         5736    i.SystemInit        system_gd32f4xx.o
    0x08041ef8   0x08041ef8   0x00000076   Code   RO         7214    i.TIMER1_Init       time.o
    0x08041f6e   0x08041f6e   0x00000008   Code   RO         5793    i.TIMER2_IRQHandler  gd32f4xx_it.o
    0x08041f76   0x08041f76   0x00000002   PAD
    0x08041f78   0x08041f78   0x00000018   Code   RO         5794    i.TIMER3_IRQHandler  gd32f4xx_it.o
    0x08041f90   0x08041f90   0x00000054   Code   RO         7215    i.TIMER3_Init       time.o
    0x08041fe4   0x08041fe4   0x00000018   Code   RO         5795    i.TIMER6_IRQHandler  gd32f4xx_it.o
    0x08041ffc   0x08041ffc   0x00000054   Code   RO         7216    i.TIMER6_Init       time.o
    0x08042050   0x08042050   0x000001d4   Code   RO         7217    i.TIM_PeriodElapsedCallback  time.o
    0x08042224   0x08042224   0x00000028   Code   RO         7287    i.UART_IDLECallBack  usart.o
    0x0804224c   0x0804224c   0x0000002c   Code   RO         7288    i.UART_RxCpltCallback  usart.o
    0x08042278   0x08042278   0x00000088   Code   RO         7290    i.USART0_Init       usart.o
    0x08042300   0x08042300   0x00000030   Code   RO         5796    i.USART2_IRQHandler  gd32f4xx_it.o
    0x08042330   0x08042330   0x00000010   Code   RO         5797    i.USBFS_IRQHandler  gd32f4xx_it.o
    0x08042340   0x08042340   0x00000034   Code   RO         5798    i.USBFS_WKUP_IRQHandler  gd32f4xx_it.o
    0x08042374   0x08042374   0x00000004   Code   RO         5799    i.UsageFault_Handler  gd32f4xx_it.o
    0x08042378   0x08042378   0x00000020   Code   RO         8931    i.__0printf$8       mc_w.l(printf8.o)
    0x08042398   0x08042398   0x00000028   Code   RO         6088    i.__NVIC_SetPriority  systick.o
    0x080423c0   0x080423c0   0x0000000e   Code   RO         9106    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080423ce   0x080423ce   0x00000002   Code   RO         9107    i.__scatterload_null  mc_w.l(handlers.o)
    0x080423d0   0x080423d0   0x0000000e   Code   RO         9108    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080423de   0x080423de   0x00000002   PAD
    0x080423e0   0x080423e0   0x00000404   Code   RO         8938    i._printf_core      mc_w.l(printf8.o)
    0x080427e4   0x080427e4   0x00000024   Code   RO         8939    i._printf_post_padding  mc_w.l(printf8.o)
    0x08042808   0x08042808   0x0000002e   Code   RO         8940    i._printf_pre_padding  mc_w.l(printf8.o)
    0x08042836   0x08042836   0x00000014   Code   RO         8342    i._usb_bos_desc_get  usbd_enum.o
    0x0804284a   0x0804284a   0x00000022   Code   RO         8343    i._usb_config_desc_get  usbd_enum.o
    0x0804286c   0x0804286c   0x00000014   Code   RO         8344    i._usb_dev_desc_get  usbd_enum.o
    0x08042880   0x08042880   0x00000074   Code   RO         8345    i._usb_std_clearfeature  usbd_enum.o
    0x080428f4   0x080428f4   0x00000040   Code   RO         8346    i._usb_std_getconfiguration  usbd_enum.o
    0x08042934   0x08042934   0x00000114   Code   RO         8347    i._usb_std_getdescriptor  usbd_enum.o
    0x08042a48   0x08042a48   0x0000003c   Code   RO         8348    i._usb_std_getinterface  usbd_enum.o
    0x08042a84   0x08042a84   0x000000c4   Code   RO         8349    i._usb_std_getstatus  usbd_enum.o
    0x08042b48   0x08042b48   0x00000006   Code   RO         8350    i._usb_std_reserved  usbd_enum.o
    0x08042b4e   0x08042b4e   0x0000005c   Code   RO         8351    i._usb_std_setaddress  usbd_enum.o
    0x08042baa   0x08042baa   0x00000002   PAD
    0x08042bac   0x08042bac   0x000000b4   Code   RO         8352    i._usb_std_setconfiguration  usbd_enum.o
    0x08042c60   0x08042c60   0x00000006   Code   RO         8353    i._usb_std_setdescriptor  usbd_enum.o
    0x08042c66   0x08042c66   0x00000068   Code   RO         8354    i._usb_std_setfeature  usbd_enum.o
    0x08042cce   0x08042cce   0x00000044   Code   RO         8355    i._usb_std_setinterface  usbd_enum.o
    0x08042d12   0x08042d12   0x00000006   Code   RO         8356    i._usb_std_synchframe  usbd_enum.o
    0x08042d18   0x08042d18   0x00000014   Code   RO         8357    i._usb_str_desc_get  usbd_enum.o
    0x08042d2c   0x08042d2c   0x00000014   Code   RO         6089    i.delay_1ms         systick.o
    0x08042d40   0x08042d40   0x00000018   Code   RO         6090    i.delay_decrement   systick.o
    0x08042d58   0x08042d58   0x0000003e   Code   RO         1249    i.dma_interrupt_flag_clear  gd32f4xx_dma.o
    0x08042d96   0x08042d96   0x00000204   Code   RO         1250    i.dma_interrupt_flag_get  gd32f4xx_dma.o
    0x08042f9a   0x08042f9a   0x00000002   PAD
    0x08042f9c   0x08042f9c   0x0000000c   Code   RO         2274    i.exti_interrupt_flag_clear  gd32f4xx_exti.o
    0x08042fa8   0x08042fa8   0x00000024   Code   RO         2275    i.exti_interrupt_flag_get  gd32f4xx_exti.o
    0x08042fcc   0x08042fcc   0x00000024   Code   RO         7292    i.fputc             usart.o
    0x08042ff0   0x08042ff0   0x00000020   Code   RO         7598    i.gd_eval_key_state_get  gd32f470v_start.o
    0x08043010   0x08043010   0x00000014   Code   RO         7690    i.get_hard_rand_data  api_tnrg.o
    0x08043024   0x08043024   0x0000005e   Code   RO         2656    i.gpio_af_set       gd32f4xx_gpio.o
    0x08043082   0x08043082   0x00000004   Code   RO         2657    i.gpio_bit_reset    gd32f4xx_gpio.o
    0x08043086   0x08043086   0x00000004   Code   RO         2658    i.gpio_bit_set      gd32f4xx_gpio.o
    0x0804308a   0x0804308a   0x00000010   Code   RO         2662    i.gpio_input_bit_get  gd32f4xx_gpio.o
    0x0804309a   0x0804309a   0x0000004e   Code   RO         2664    i.gpio_mode_set     gd32f4xx_gpio.o
    0x080430e8   0x080430e8   0x00000042   Code   RO         2666    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x0804312a   0x0804312a   0x00000002   PAD
    0x0804312c   0x0804312c   0x0000002c   Code   RO         8553    i.hw_delay          gd32f4xx_hw.o
    0x08043158   0x08043158   0x00000070   Code   RO         8554    i.hw_time_set       gd32f4xx_hw.o
    0x080431c8   0x080431c8   0x0000006c   Code   RO         6024    i.main              main.o
    0x08043234   0x08043234   0x000000c4   Code   RO         3245    i.nvic_irq_enable   gd32f4xx_misc.o
    0x080432f8   0x080432f8   0x00000014   Code   RO         3246    i.nvic_priority_group_set  gd32f4xx_misc.o
    0x0804330c   0x0804330c   0x00000018   Code   RO         3247    i.nvic_vector_table_set  gd32f4xx_misc.o
    0x08043324   0x08043324   0x00000014   Code   RO         3306    i.pmu_backup_write_enable  gd32f4xx_pmu.o
    0x08043338   0x08043338   0x000000f4   Code   RO         3320    i.pmu_to_deepsleepmode  gd32f4xx_pmu.o
    0x0804342c   0x0804342c   0x00000018   Code   RO         3455    i.rcu_ck48m_clock_config  gd32f4xx_rcu.o
    0x08043444   0x08043444   0x00000124   Code   RO         3458    i.rcu_clock_freq_get  gd32f4xx_rcu.o
    0x08043568   0x08043568   0x00000024   Code   RO         3461    i.rcu_flag_get      gd32f4xx_rcu.o
    0x0804358c   0x0804358c   0x00000024   Code   RO         3474    i.rcu_osci_on       gd32f4xx_rcu.o
    0x080435b0   0x080435b0   0x0000015c   Code   RO         3475    i.rcu_osci_stab_wait  gd32f4xx_rcu.o
    0x0804370c   0x0804370c   0x00000024   Code   RO         3477    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x08043730   0x08043730   0x00000024   Code   RO         3480    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x08043754   0x08043754   0x00000024   Code   RO         3481    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x08043778   0x08043778   0x00000018   Code   RO         3482    i.rcu_pll48m_clock_config  gd32f4xx_rcu.o
    0x08043790   0x08043790   0x00000018   Code   RO         3486    i.rcu_rtc_clock_config  gd32f4xx_rcu.o
    0x080437a8   0x080437a8   0x00000018   Code   RO         3491    i.rcu_system_clock_source_config  gd32f4xx_rcu.o
    0x080437c0   0x080437c0   0x00000010   Code   RO         3492    i.rcu_system_clock_source_get  gd32f4xx_rcu.o
    0x080437d0   0x080437d0   0x00000038   Code   RO         5800    i.resume_mcu_clk    gd32f4xx_it.o
    0x08043808   0x08043808   0x000000c4   Code   RO         3767    i.rtc_init          gd32f4xx_rtc.o
    0x080438cc   0x080438cc   0x00000048   Code   RO         3768    i.rtc_init_mode_enter  gd32f4xx_rtc.o
    0x08043914   0x08043914   0x00000014   Code   RO         3769    i.rtc_init_mode_exit  gd32f4xx_rtc.o
    0x08043928   0x08043928   0x00000060   Code   RO         3774    i.rtc_register_sync_wait  gd32f4xx_rtc.o
    0x08043988   0x08043988   0x0000000a   Code   RO         4335    i.spi_enable        gd32f4xx_spi.o
    0x08043992   0x08043992   0x00000008   Code   RO         4337    i.spi_i2s_data_receive  gd32f4xx_spi.o
    0x0804399a   0x0804399a   0x00000004   Code   RO         4338    i.spi_i2s_data_transmit  gd32f4xx_spi.o
    0x0804399e   0x0804399e   0x00000010   Code   RO         4340    i.spi_i2s_flag_get  gd32f4xx_spi.o
    0x080439ae   0x080439ae   0x00000032   Code   RO         4344    i.spi_init          gd32f4xx_spi.o
    0x080439e0   0x080439e0   0x000000fc   Code   RO         5737    i.system_clock_168m_25m_hxtal  system_gd32f4xx.o
    0x08043adc   0x08043adc   0x00000008   Code   RO         5738    i.system_clock_config  system_gd32f4xx.o
    0x08043ae4   0x08043ae4   0x00000050   Code   RO         6091    i.systick_config    systick.o
    0x08043b34   0x08043b34   0x0000000a   Code   RO         4632    i.timer_auto_reload_shadow_enable  gd32f4xx_timer.o
    0x08043b3e   0x08043b3e   0x00000002   PAD
    0x08043b40   0x08043b40   0x000001ec   Code   RO         4649    i.timer_channel_output_config  gd32f4xx_timer.o
    0x08043d2c   0x08043d2c   0x0000005a   Code   RO         4651    i.timer_channel_output_mode_config  gd32f4xx_timer.o
    0x08043d86   0x08043d86   0x00000026   Code   RO         4653    i.timer_channel_output_pulse_value_config  gd32f4xx_timer.o
    0x08043dac   0x08043dac   0x0000005a   Code   RO         4654    i.timer_channel_output_shadow_config  gd32f4xx_timer.o
    0x08043e06   0x08043e06   0x00000002   PAD
    0x08043e08   0x08043e08   0x00000184   Code   RO         4663    i.timer_deinit      gd32f4xx_timer.o
    0x08043f8c   0x08043f8c   0x0000000a   Code   RO         4664    i.timer_disable     gd32f4xx_timer.o
    0x08043f96   0x08043f96   0x0000000a   Code   RO         4668    i.timer_enable      gd32f4xx_timer.o
    0x08043fa0   0x08043fa0   0x00000098   Code   RO         4678    i.timer_init        gd32f4xx_timer.o
    0x08044038   0x08044038   0x00000008   Code   RO         4684    i.timer_interrupt_disable  gd32f4xx_timer.o
    0x08044040   0x08044040   0x00000008   Code   RO         4685    i.timer_interrupt_enable  gd32f4xx_timer.o
    0x08044048   0x08044048   0x00000006   Code   RO         4686    i.timer_interrupt_flag_clear  gd32f4xx_timer.o
    0x0804404e   0x0804404e   0x00000018   Code   RO         4687    i.timer_interrupt_flag_get  gd32f4xx_timer.o
    0x08044066   0x08044066   0x00000014   Code   RO         5255    i.trng_deinit       gd32f4xx_trng.o
    0x0804407a   0x0804407a   0x00000002   PAD
    0x0804407c   0x0804407c   0x00000014   Code   RO         5257    i.trng_enable       gd32f4xx_trng.o
    0x08044090   0x08044090   0x00000018   Code   RO         5258    i.trng_flag_get     gd32f4xx_trng.o
    0x080440a8   0x080440a8   0x0000000c   Code   RO         5259    i.trng_get_true_random_data  gd32f4xx_trng.o
    0x080440b4   0x080440b4   0x000000e8   Code   RO         5328    i.usart_baudrate_set  gd32f4xx_usart.o
    0x0804419c   0x0804419c   0x0000000a   Code   RO         5332    i.usart_data_receive  gd32f4xx_usart.o
    0x080441a6   0x080441a6   0x00000008   Code   RO         5333    i.usart_data_transmit  gd32f4xx_usart.o
    0x080441ae   0x080441ae   0x00000002   PAD
    0x080441b0   0x080441b0   0x000000dc   Code   RO         5334    i.usart_deinit      gd32f4xx_usart.o
    0x0804428c   0x0804428c   0x0000000a   Code   RO         5338    i.usart_enable      gd32f4xx_usart.o
    0x08044296   0x08044296   0x0000001e   Code   RO         5340    i.usart_flag_get    gd32f4xx_usart.o
    0x080442b4   0x080442b4   0x0000001a   Code   RO         5349    i.usart_interrupt_flag_clear  gd32f4xx_usart.o
    0x080442ce   0x080442ce   0x00000038   Code   RO         5350    i.usart_interrupt_flag_get  gd32f4xx_usart.o
    0x08044306   0x08044306   0x00000010   Code   RO         5365    i.usart_receive_config  gd32f4xx_usart.o
    0x08044316   0x08044316   0x00000010   Code   RO         5380    i.usart_transmit_config  gd32f4xx_usart.o
    0x08044326   0x08044326   0x00000020   Code   RO         8035    i.usb_clock_active  drv_usb_dev.o
    0x08044346   0x08044346   0x00000002   PAD
    0x08044348   0x08044348   0x00000028   Code   RO         8036    i.usb_ctlep_startout  drv_usb_dev.o
    0x08044370   0x08044370   0x0000002a   Code   RO         8041    i.usb_iepintr_read  drv_usb_dev.o
    0x0804439a   0x0804439a   0x0000001e   Code   RO         7950    i.usb_rxfifo_read   drv_usb_core.o
    0x080443b8   0x080443b8   0x00000038   Code   RO         8560    i.usb_timer_irq     gd32f4xx_hw.o
    0x080443f0   0x080443f0   0x00000098   Code   RO         8044    i.usb_transc_active  drv_usb_dev.o
    0x08044488   0x08044488   0x00000044   Code   RO         8045    i.usb_transc_clrstall  drv_usb_dev.o
    0x080444cc   0x080444cc   0x0000010c   Code   RO         8047    i.usb_transc_inxfer  drv_usb_dev.o
    0x080445d8   0x080445d8   0x00000090   Code   RO         8048    i.usb_transc_outxfer  drv_usb_dev.o
    0x08044668   0x08044668   0x00000042   Code   RO         8049    i.usb_transc_stall  drv_usb_dev.o
    0x080446aa   0x080446aa   0x00000028   Code   RO         7952    i.usb_txfifo_flush  drv_usb_core.o
    0x080446d2   0x080446d2   0x00000022   Code   RO         7953    i.usb_txfifo_write  drv_usb_core.o
    0x080446f4   0x080446f4   0x0000000e   Code   RO         8561    i.usb_udelay        gd32f4xx_hw.o
    0x08044702   0x08044702   0x00000026   Code   RO         8360    i.usbd_class_request  usbd_enum.o
    0x08044728   0x08044728   0x0000002e   Code   RO         8487    i.usbd_ctl_recev    usbd_transc.o
    0x08044756   0x08044756   0x0000002e   Code   RO         8488    i.usbd_ctl_send     usbd_transc.o
    0x08044784   0x08044784   0x00000020   Code   RO         8489    i.usbd_ctl_status_recev  usbd_transc.o
    0x080447a4   0x080447a4   0x00000020   Code   RO         8490    i.usbd_ctl_status_send  usbd_transc.o
    0x080447c4   0x080447c4   0x0000008c   Code   RO         8156    i.usbd_emptytxfifo_write  drv_usbd_int.o
    0x08044850   0x08044850   0x0000001e   Code   RO         8361    i.usbd_enum_error   usbd_enum.o
    0x0804486e   0x0804486e   0x0000003c   Code   RO         8249    i.usbd_ep_recev     usbd_core.o
    0x080448aa   0x080448aa   0x0000003c   Code   RO         8250    i.usbd_ep_send      usbd_core.o
    0x080448e6   0x080448e6   0x0000003a   Code   RO         8252    i.usbd_ep_stall     usbd_core.o
    0x08044920   0x08044920   0x0000003a   Code   RO         8253    i.usbd_ep_stall_clear  usbd_core.o
    0x0804495a   0x0804495a   0x000000a0   Code   RO         8491    i.usbd_in_transc    usbd_transc.o
    0x080449fa   0x080449fa   0x00000002   PAD
    0x080449fc   0x080449fc   0x00000068   Code   RO         8157    i.usbd_int_enumfinish  drv_usbd_int.o
    0x08044a64   0x08044a64   0x0000008a   Code   RO         8158    i.usbd_int_epin     drv_usbd_int.o
    0x08044aee   0x08044aee   0x000000ce   Code   RO         8159    i.usbd_int_epout    drv_usbd_int.o
    0x08044bbc   0x08044bbc   0x000000d8   Code   RO         8160    i.usbd_int_reset    drv_usbd_int.o
    0x08044c94   0x08044c94   0x000000bc   Code   RO         8161    i.usbd_int_rxfifo   drv_usbd_int.o
    0x08044d50   0x08044d50   0x00000068   Code   RO         8162    i.usbd_int_suspend  drv_usbd_int.o
    0x08044db8   0x08044db8   0x000000de   Code   RO         8163    i.usbd_isr          drv_usbd_int.o
    0x08044e96   0x08044e96   0x00000084   Code   RO         8492    i.usbd_out_transc   usbd_transc.o
    0x08044f1a   0x08044f1a   0x00000084   Code   RO         8493    i.usbd_setup_transc  usbd_transc.o
    0x08044f9e   0x08044f9e   0x00000002   PAD
    0x08044fa0   0x08044fa0   0x0000001c   Code   RO         8362    i.usbd_standard_request  usbd_enum.o
    0x08044fbc   0x08044fbc   0x00000006   Code   RO         8363    i.usbd_vendor_request  usbd_enum.o
    0x08044fc2   0x08044fc2   0x00000004   Data   RO         8050    .constdata          drv_usb_dev.o
    0x08044fc6   0x08044fc6   0x00000002   PAD
    0x08044fc8   0x08044fc8   0x0000003c   Data   RO         8164    .constdata          drv_usbd_int.o
    0x08045004   0x08045004   0x00000030   Data   RO         9104    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20000000, Load base: 0x08045034, Size: 0x00000000, Max: 0x00000004, ABSOLUTE, UNINIT)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000004, Load base: 0x08045034, Size: 0x00005584, Max: 0x0001fffc, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000004   0x08045034   0x00000004   Data   RW         5739    .data               system_gd32f4xx.o
    0x20000008   0x08045038   0x0000000f   Data   RW         6026    .data               main.o
    0x20000017   0x08045047   0x00000001   PAD
    0x20000018   0x08045048   0x00000004   Data   RW         6092    .data               systick.o
    0x2000001c   0x0804504c   0x00000001   Data   RW         6208    .data               adc.o
    0x2000001d   0x0804504d   0x00000003   PAD
    0x20000020   0x08045050   0x00000006   Data   RW         7219    .data               time.o
    0x20000026   0x08045056   0x00000001   Data   RW         7387    .data               usb.o
    0x20000027   0x08045057   0x00000001   PAD
    0x20000028   0x08045058   0x0000003e   Data   RW         7603    .data               gd32f470v_start.o
    0x20000066   0x08045096   0x00000002   Data   RW         7760    .data               api_w5500.o
    0x20000068   0x08045098   0x00000043   Data   RW         8364    .data               usbd_enum.o
    0x200000ab   0x080450db   0x00000001   PAD
    0x200000ac   0x080450dc   0x00000008   Data   RW         8562    .data               gd32f4xx_hw.o
    0x200000b4   0x080450e4   0x00000004   Data   RW         9039    .data               mc_w.l(stdout.o)
    0x200000b8        -       0x00000010   Zero   RW         3325    .bss                gd32f4xx_pmu.o
    0x200000c8        -       0x00000014   Zero   RW         7144    .bss                rtc.o
    0x200000dc        -       0x00000014   Zero   RW         7218    .bss                time.o
    0x200000f0        -       0x00000494   Zero   RW         8635    .bss                cdc_acm_core.o
    0x20000584   0x080450e8   0x00000004   PAD
    0x20000588        -       0x00005000   Zero   RW            1    STACK               startup_gd32f450_470.o


    Execution Region RW_IRAM3 (Exec base: 0x20020000, Load base: 0x080450e8, Size: 0x000020b8, Max: 0x00030000, ABSOLUTE, COMPRESSED[0x00000044])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20020000   COMPRESSED   0x00001817   Data   RW         7293    .RAM_D3             usart.o
    0x20021817   COMPRESSED   0x00000001   PAD
    0x20021818   COMPRESSED   0x000008a0   Data   RW         7759    .RAM_D3             api_w5500.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0       1300   25lc080a.o
       320         44          0          1          0       4166   adc.o
       388         80          0          0          0       1464   api_tnrg.o
      4012        726          0       2210          0      22762   api_w5500.o
         0          0          0          0       1172       5215   cdc_acm_core.o
       104          0          0          0          0      21956   drv_usb_core.o
       812         16          4          0          0      22017   drv_usb_dev.o
      1318         28         60          0          0      11731   drv_usbd_int.o
       906         56          0          0          0       6290   eeprom_spi.o
        32         10          0         62          0       1776   gd32f470v_start.o
         0          0          0          0          0      91580   gd32f4xx_adc.o
       578          0          0          0          0       1884   gd32f4xx_dma.o
        48         10          0          0          0       1470   gd32f4xx_exti.o
       262          0          0          0          0       4765   gd32f4xx_gpio.o
       226         28          0          8          0       3310   gd32f4xx_hw.o
       448         66          0          0          0      10829   gd32f4xx_it.o
       240         28          0          0          0       2324   gd32f4xx_misc.o
       264         46          0          0         16       1493   gd32f4xx_pmu.o
       932         84          0          0          0       9807   gd32f4xx_rcu.o
       384         22          0          0          0       3847   gd32f4xx_rtc.o
        88          0          0          0          0       4258   gd32f4xx_spi.o
      1326         52          0          0          0      10496   gd32f4xx_timer.o
        76         18          0          0          0       2556   gd32f4xx_trng.o
       624         18          0          0          0       7622   gd32f4xx_usart.o
       224         14          0          0          0       1774   gpio.o
       108          8          0         15          0       1807   main.o
       100          4          0          0         20        925   rtc.o
       212         16          0          0          0       1271   spi.o
        36          8        428          0      20480        964   startup_gd32f450_470.o
       460         28          0          4          0       3171   system_gd32f4xx.o
       164         24          0          4          0      33110   systick.o
       754         30          0          6         20       8249   time.o
       770         62          0       6167          0       8584   usart.o
         0          0          0          1          0       1019   usb.o
       236          0          0          0          0       7421   usbd_core.o
      1370         18          0         67          0      16709   usbd_enum.o
       580          0          0          0          0       5531   usbd_transc.o

    ----------------------------------------------------------------------
     18436       <USER>        <GROUP>       8552      21712     345453   Object Totals
         0          0         48          0          0          0   (incl. Generated)
        34          0          2          7          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      1142         54          0          0          0        352   printf8.o
         0          0          0          4          0          0   stdout.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
      1554         <USER>          <GROUP>          4          0        824   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1550         70          0          4          0        824   mc_w.l

    ----------------------------------------------------------------------
      1554         <USER>          <GROUP>          4          0        824   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     19990       1614        542       8556      21712     326097   Grand Totals
     19990       1614        542        248      21712     326097   ELF Image Totals (compressed)
     19990       1614        542        248          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                20532 (  20.05kB)
    Total RW  Size (RW Data + ZI Data)             30268 (  29.56kB)
    Total ROM Size (Code + RO Data + RW Data)      20780 (  20.29kB)

==============================================================================

